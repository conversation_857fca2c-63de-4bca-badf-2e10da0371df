# Security Guard Management App - Technical Documentation

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture Overview](#architecture-overview)
3. [Technology Stack](#technology-stack)
4. [Data Models & Firestore Schema](#data-models--firestore-schema)
5. [Core Features](#core-features)
6. [User Roles & Access Control](#user-roles--access-control)
7. [Application Flow](#application-flow)
8. [UI/UX Design System](#uiux-design-system)
9. [Localization](#localization)
10. [Firebase Configuration](#firebase-configuration)
11. [Development Setup](#development-setup)
12. [Testing & Quality Assurance](#testing--quality-assurance)
13. [Deployment](#deployment)
14. [Future Enhancements](#future-enhancements)

---

## Project Overview

The **Security Guard Management App** is a comprehensive mobile-first application built with Flutter and Firebase, designed to streamline security guard shift management, attendance tracking, and administrative operations for security companies.

### Key Objectives
- **Simplify Scheduling**: Efficient event creation and guard assignment
- **Digital Time Tracking**: Compliant with local labor regulations
- **Role-Based Access**: Secure multi-tier user management
- **Real-time Updates**: Live synchronization across all devices
- **Multi-language Support**: Slovenian and English localization
- **Responsive Design**: Optimized for mobile, tablet, and desktop

### Current Status
- **Development Phase**: Phase 4 (Admin Panel Development) - 80% Complete
- **Live Environment**: http://localhost:8080 (Brave browser testing)
- **Firebase Integration**: Fully configured and operational
- **Authentication**: Working with test credentials
- **Multi-platform**: iOS, Android, and Web support

---

## Architecture Overview

The application follows a **Clean Architecture** pattern with clear separation of concerns:

```
lib/
├── core/                    # Core utilities and configurations
│   ├── constants/          # App-wide constants
│   ├── theme/             # Material Design 3 theming
│   ├── localization/      # Internationalization setup
│   └── utils/             # Helper utilities
├── features/              # Feature-based modules
│   ├── auth/              # Authentication & authorization
│   ├── dashboard/         # Main dashboard interface
│   ├── events/            # Event management system
│   ├── reports/           # Analytics and reporting
│   ├── users/             # User management
│   ├── profile/           # User profile management
│   └── admin/             # Administrative functions
├── shared/                # Shared components and services
│   ├── models/            # Data models
│   ├── services/          # Business logic services
│   ├── repositories/      # Data access layer
│   ├── widgets/           # Reusable UI components
│   └── providers/         # State management
└── l10n/                  # Generated localization files
```

### Architectural Principles
- **Feature-First Organization**: Each feature is self-contained
- **Dependency Injection**: Using Riverpod for state management
- **Repository Pattern**: Abstracted data access layer
- **Service Layer**: Business logic separation
- **Responsive Design**: Adaptive layouts for all screen sizes

---

## Technology Stack

### Frontend Framework
- **Flutter 3.8+**: Cross-platform mobile and web development
- **Material Design 3**: Modern UI components and theming
- **Responsive Design**: Adaptive layouts for mobile, tablet, and desktop

### Backend Services
- **Firebase Firestore**: NoSQL document database
- **Firebase Authentication**: User authentication and authorization
- **Firebase Cloud Messaging**: Push notifications (planned)
- **Firebase Hosting**: Web application deployment
- **Firebase Functions**: Server-side logic (planned)

### State Management & Navigation
- **Riverpod 2.6+**: Reactive state management
- **GoRouter 14.6+**: Declarative routing and navigation
- **Provider Pattern**: Dependency injection

### Development Tools
- **JSON Serialization**: Automated model generation
- **Flutter Localization**: Multi-language support
- **Material Design Icons**: Comprehensive icon library
- **Build Runner**: Code generation automation

### Key Dependencies
```yaml
dependencies:
  flutter: sdk
  firebase_core: ^3.8.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  flutter_riverpod: ^2.6.1
  go_router: ^14.6.2
  flutter_localization: ^0.2.2
  intl: ^0.20.2
  json_annotation: ^4.9.0
```

---

## Data Models & Firestore Schema

### Core Data Models

#### UserModel
```dart
class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String phone;
  final String role; // 'superadmin', 'admin', 'guard'
  final String? companyId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;
}
```

#### EventModel
```dart
class EventModel {
  final String id;
  final String title;
  final String description;
  final String location;
  final DateTime startDateTime;
  final DateTime endDateTime;
  final String status; // 'pending', 'confirmed', 'completed', 'cancelled'
  final String createdBy;
  final String companyId;
  final List<String> assignedGuardIds;
  final int expectedDurationMinutes;
  final double? hourlyRate;
  final DateTime createdAt;
}
```

#### AttendanceModel
```dart
class AttendanceModel {
  final String id;
  final String eventId;
  final String guardId;
  final String status; // 'pending', 'accepted', 'declined', 'clocked_in', 'clocked_out'
  final DateTime? clockInTime;
  final DateTime? clockOutTime;
  final String? notes;
  final double? actualHours;
  final double? totalPay;
}
```

### Firestore Database Schema

```mermaid
erDiagram
    USERS {
        string id PK
        string email
        string firstName
        string lastName
        string phone
        string role
        string companyId FK
        boolean isActive
        timestamp createdAt
        timestamp updatedAt
    }

    EVENTS {
        string id PK
        string title
        string description
        string location
        timestamp startDateTime
        timestamp endDateTime
        string status
        string createdBy FK
        string companyId FK
        array assignedGuardIds
        number expectedDurationMinutes
        number hourlyRate
        timestamp createdAt
        timestamp updatedAt
    }

    ATTENDANCE {
        string id PK
        string eventId FK
        string guardId FK
        string status
        timestamp clockInTime
        timestamp clockOutTime
        string notes
        number actualHours
        number totalPay
        timestamp createdAt
        timestamp updatedAt
    }

    COMPANIES {
        string id PK
        string name
        string email
        string phone
        string address
        boolean isActive
        timestamp createdAt
        timestamp updatedAt
    }

    USERS ||--o{ EVENTS : creates
    USERS ||--o{ ATTENDANCE : participates
    EVENTS ||--o{ ATTENDANCE : tracks
    COMPANIES ||--o{ USERS : employs
    COMPANIES ||--o{ EVENTS : owns
```

### Data Relationships
- **Users** belong to **Companies** (many-to-one)
- **Events** are created by **Users** (admin/superadmin)
- **Events** can have multiple assigned **Guards** (many-to-many)
- **Attendance** records track **Guard** participation in **Events**
- **Companies** own **Events** and employ **Users**

---

## Core Features

### 1. Authentication & Authorization

#### Firebase Authentication Integration
- **Email/Password Authentication**: Secure login system
- **Role-Based Access Control**: Three-tier permission system
- **Session Management**: Persistent authentication state
- **Password Reset**: Email-based password recovery

#### Security Features
- **Controlled Registration**: Admin-only user creation
- **Custom Claims**: Firebase role-based permissions
- **Session Validation**: Automatic token refresh
- **Secure Logout**: Complete session cleanup

### 2. Event Management System

#### Event Creation & Editing
- **Comprehensive Event Forms**: Title, description, location, timing
- **Smart Defaults**: Automatic end time calculation
- **Guard Assignment**: Multi-select guard assignment interface
- **Financial Tracking**: Hourly rate and cost calculations
- **Status Management**: Pending → Confirmed → Completed workflow

#### Event Features
- **Real-time Updates**: Live synchronization across devices
- **Event Filtering**: Status-based filtering system
- **Event Details**: Comprehensive event information display
- **Guard Management**: Add/remove guards from events
- **Financial Breakdown**: Cost calculations and revenue tracking

### 3. User Management

#### User Creation & Management
- **Role-Based User Creation**: Admin can create guards and other admins
- **User Profile Management**: Complete user information editing
- **User Status Control**: Activate/deactivate user accounts
- **Search & Filtering**: Find users by role, status, or search query

#### User Roles & Permissions
- **Superadmin**: Full system access, manage all companies
- **Admin**: Company-level management, create users, manage events
- **Guard**: View assigned events, clock in/out, personal reports

### 4. Reports & Analytics

#### Financial Reporting
- **Revenue Tracking**: Total revenue calculations with Euro (€) currency
- **Cost Analysis**: Event-based cost breakdowns
- **Hourly Rate Analytics**: Average rate calculations
- **Date Range Filtering**: Customizable reporting periods

#### Operational Reports
- **Event Status Distribution**: Visual status breakdowns
- **Guard Performance**: Individual guard analytics (planned)
- **Attendance Reports**: Clock in/out tracking
- **Export Functionality**: CSV export capabilities (planned)

### 5. Dashboard & Navigation

#### Responsive Dashboard
- **Welcome Interface**: Personalized greeting and overview
- **Quick Statistics**: Key metrics at a glance
- **Quick Actions**: Fast access to common tasks
- **Real-time Data**: Live updates from Firestore

#### Navigation System
- **Adaptive Layout**: Desktop sidebar, mobile bottom navigation
- **Role-Based Menus**: Different navigation for different roles
- **Breadcrumb Navigation**: Clear navigation hierarchy
- **Deep Linking**: Direct access to specific features

---

## User Roles & Access Control

### Role Hierarchy

```mermaid
graph TD
    A[Superadmin] --> B[Admin]
    B --> C[Guard]

    A --> D[Manage All Companies]
    A --> E[Create Admin Accounts]
    A --> F[System Configuration]

    B --> G[Manage Company Users]
    B --> H[Create/Edit Events]
    B --> I[View Reports]
    B --> J[Assign Guards]

    C --> K[View Assigned Events]
    C --> L[Accept/Decline Events]
    C --> M[Clock In/Out]
    C --> N[Personal Reports]
```

### Permission Matrix

| Feature | Superadmin | Admin | Guard |
|---------|------------|-------|-------|
| Create Users | ✅ | ✅ (Guards & Admins) | ❌ |
| Manage Events | ✅ | ✅ | ❌ |
| View All Reports | ✅ | ✅ | ❌ |
| Assign Guards | ✅ | ✅ | ❌ |
| View Personal Events | ✅ | ✅ | ✅ |
| Clock In/Out | ❌ | ❌ | ✅ |
| Accept/Decline Events | ❌ | ❌ | ✅ |
| Company Management | ✅ | ❌ | ❌ |

### Access Control Implementation
```dart
// Role-based access control in UserModel
bool get canManageUsers => isSuperAdmin || isAdmin;
bool get canCreateEvents => isSuperAdmin || isAdmin;
bool get canViewReports => isSuperAdmin || isAdmin;

// Service-level permission checks
Future<bool> get isAdmin async {
  final userRole = await getUserRole();
  return userRole == 'admin' || userRole == 'superadmin';
}
```

---

## Application Flow

### Admin Workflow

```mermaid
flowchart TD
    A[Admin Login] --> B[Dashboard]
    B --> C{Choose Action}

    C --> D[Create Event]
    C --> E[Manage Users]
    C --> F[View Reports]
    C --> G[Manage Events]

    D --> D1[Event Form]
    D1 --> D2[Assign Guards]
    D2 --> D3[Save Event]
    D3 --> H[Event Created]

    E --> E1[User List]
    E1 --> E2[Create/Edit User]
    E2 --> E3[Save User]
    E3 --> I[User Updated]

    F --> F1[Select Report Type]
    F1 --> F2[Set Date Range]
    F2 --> F3[Generate Report]
    F3 --> J[View Analytics]

    G --> G1[Event List]
    G1 --> G2[Select Event]
    G2 --> G3[Event Details]
    G3 --> G4[Update Status/Guards]
    G4 --> K[Event Updated]
```

### Guard Workflow (Planned)

```mermaid
flowchart TD
    A[Guard Login] --> B[Guard Dashboard]
    B --> C{View Options}

    C --> D[Assigned Events]
    C --> E[Personal Reports]
    C --> F[Profile]

    D --> D1[Event List]
    D1 --> D2[Select Event]
    D2 --> D3[Event Details]
    D3 --> D4{Event Action}

    D4 --> D5[Accept Event]
    D4 --> D6[Decline Event]
    D4 --> D7[Clock In]
    D4 --> D8[Clock Out]

    D5 --> G[Event Accepted]
    D6 --> H[Event Declined]
    D7 --> I[Clocked In]
    D8 --> J[Clocked Out]
```

### Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant FA as Firebase Auth
    participant FS as Firestore

    U->>A: Enter Credentials
    A->>FA: signInWithEmailAndPassword()
    FA->>A: UserCredential
    A->>FS: Get User Document
    FS->>A: UserModel
    A->>A: Set User State
    A->>U: Navigate to Dashboard

    Note over U,FS: Role-based navigation
    alt Admin/Superadmin
        A->>U: Admin Dashboard
    else Guard
        A->>U: Guard Dashboard
    end
```

### Event Management Flow

```mermaid
stateDiagram-v2
    [*] --> Pending
    Pending --> Confirmed : Admin confirms
    Pending --> Cancelled : Admin cancels
    Confirmed --> InProgress : Event starts
    InProgress --> Completed : Event ends
    Completed --> [*]
    Cancelled --> [*]

    note right of Pending : Guards can be assigned
    note right of Confirmed : Guards receive notifications
    note right of InProgress : Guards can clock in/out
    note right of Completed : Reports generated
```

---

## UI/UX Design System

### Material Design 3 Implementation

#### Color Palette
```dart
// Primary Colors
static const Color primaryColor = Color(0xFF1976D2);      // Blue
static const Color secondaryColor = Color(0xFF03DAC6);    // Teal
static const Color backgroundColor = Color(0xFFF5F5F5);   // Light Gray

// Status Colors
static const Color successColor = Color(0xFF4CAF50);      // Green
static const Color warningColor = Color(0xFFFF9800);      // Orange
static const Color errorColor = Color(0xFFB00020);        // Red
```

#### Typography System
- **Headlines**: Roboto Bold for section headers
- **Body Text**: Roboto Regular for content
- **Captions**: Roboto Light for secondary information
- **Buttons**: Roboto Medium for action elements

#### Component Library
- **Cards**: Elevated cards with 12px border radius
- **Buttons**: Rounded corners with consistent padding
- **Forms**: Outlined input fields with focus states
- **Navigation**: Adaptive sidebar/bottom navigation

### Responsive Design Strategy

#### Breakpoints
- **Mobile**: ≤ 768px (Bottom navigation, single column)
- **Tablet**: 769px - 1024px (Constrained width, simplified layout)
- **Desktop**: > 1024px (Sidebar navigation, multi-column layout)

#### Layout Adaptation
```dart
Widget build(BuildContext context) {
  final screenSize = MediaQuery.of(context).size;
  final isDesktop = screenSize.width > 1024;
  final isTablet = screenSize.width > 768 && screenSize.width <= 1024;

  if (isDesktop) {
    return _buildDesktopLayout();
  } else if (isTablet) {
    return _buildTabletLayout();
  } else {
    return _buildMobileLayout();
  }
}
```

### Dark Theme Support
- **Automatic Detection**: System theme preference
- **High Contrast**: Improved text readability
- **Consistent Colors**: Maintained brand identity
- **Accessibility**: WCAG 2.1 AA compliance

---

## Localization

### Multi-language Support

#### Supported Languages
- **Slovenian (sl)**: Primary language (default)
- **English (en)**: Secondary language

#### Implementation
```dart
// Localization setup in main.dart
localization.init(
  mapLocales: AppLocale.locales,
  initLanguageCode: 'sl', // Default to Slovenian
);

// Usage in widgets
Text(AppLocale.dashboard.getString(context))
```

#### Translation Files
- **app_sl.arb**: Slovenian translations
- **app_en.arb**: English translations
- **Generated Files**: Automatic code generation

#### Key Localization Features
- **Dynamic Language Switching**: Runtime language changes
- **Parameterized Strings**: Dynamic content insertion
- **Pluralization Support**: Proper plural forms
- **Date/Time Formatting**: Locale-specific formatting

### Sample Translations
```json
{
  "@@locale": "sl",
  "appTitle": "Upravljanje Varnostnikov",
  "dashboard": "Nadzorna plošča",
  "events": "Dogodki",
  "guards": "Varnostniki",
  "reports": "Poročila",
  "createEvent": "Ustvari dogodek",
  "welcomeBack": "Dobrodošli nazaj!"
}
```

---

## Firebase Configuration

### Project Setup
- **Project ID**: `security-guard-managemen-c62ef`
- **Authentication**: Email/Password enabled
- **Firestore**: Native mode with security rules
- **Hosting**: Configured for web deployment

### Security Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Temporary permissive rules for testing
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### Test Credentials
```
Admin Account:
  Email: <EMAIL>
  Password: TestAdmin123!
  Role: superadmin

Guard Account:
  Email: <EMAIL>
  Password: TestGuard123!
  Role: guard
```

### Data Initialization
The `setup_firestore.js` script creates:
- Default company document
- Admin and guard user documents
- Sample event with guard assignment
- Sample attendance record

---

## Development Setup

### Prerequisites
- **Flutter SDK**: 3.8.0 or higher
- **Dart SDK**: Included with Flutter
- **Firebase CLI**: For deployment and configuration
- **Node.js**: For Firebase Functions (future)

### Installation Steps
1. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd security_guard_app
   ```

2. **Install Dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Configuration**
   ```bash
   firebase login
   flutterfire configure
   ```

4. **Generate Code**
   ```bash
   flutter packages pub run build_runner build
   ```

5. **Run Application**
   ```bash
   flutter run -d chrome --web-port 8080
   ```

### Development Environment
- **IDE**: VS Code with Flutter extension
- **Browser**: Brave (preferred for testing)
- **Port**: 8080 for web development
- **Hot Reload**: Enabled for rapid development

### Project Structure Commands
```bash
# Generate localization files
flutter gen-l10n

# Build for production
flutter build web

# Run tests
flutter test

# Analyze code
flutter analyze
```

---

## Testing & Quality Assurance

### Testing Strategy
- **Unit Tests**: Core business logic validation
- **Widget Tests**: UI component testing
- **Integration Tests**: End-to-end user flows
- **Firebase Rules Testing**: Security rule validation

### Quality Assurance Tools
- **Flutter Lints**: Code quality enforcement
- **Analysis Options**: Custom linting rules
- **Code Coverage**: Test coverage reporting
- **Performance Monitoring**: Firebase Performance (planned)

### Test Structure
```
test/
├── unit/              # Unit tests for services and models
├── widget/            # Widget and UI component tests
└── integration/       # End-to-end integration tests
```

### Continuous Integration (Planned)
- **GitHub Actions**: Automated testing and deployment
- **Code Quality Gates**: Lint and test requirements
- **Automated Deployment**: Firebase hosting deployment

---

## Deployment

### Web Deployment
- **Firebase Hosting**: Primary web deployment platform
- **Custom Domain**: Configurable for production
- **SSL Certificates**: Automatic HTTPS encryption
- **CDN**: Global content delivery network

### Mobile Deployment (Planned)
- **iOS App Store**: Apple App Store distribution
- **Google Play Store**: Android app distribution
- **Enterprise Distribution**: Internal company deployment

### Environment Configuration
- **Development**: Local Firebase emulator
- **Staging**: Firebase staging project
- **Production**: Firebase production project

### Deployment Commands
```bash
# Build for web
flutter build web

# Deploy to Firebase
firebase deploy --only hosting

# Deploy with custom target
firebase use production
firebase deploy
```

---

## Future Enhancements

### Phase 5: Guard Mobile App Development
- **Guard Dashboard**: Mobile-optimized interface
- **Event Calendar**: Visual event scheduling
- **Push Notifications**: Real-time event updates
- **Offline Capability**: Basic offline functionality

### Phase 6: Advanced Features
- **Geolocation Tracking**: Location-based clock in/out
- **QR Code Scanning**: Location verification
- **Chat System**: Guard-admin communication
- **Advanced Analytics**: Detailed performance metrics

### Phase 7: Enterprise Features
- **Multi-company Support**: SaaS model implementation
- **API Integration**: Third-party system integration
- **Advanced Reporting**: Custom report builder
- **Payroll Integration**: Automated payroll processing

### Technical Improvements
- **Performance Optimization**: Code splitting and lazy loading
- **Security Enhancements**: Advanced security rules
- **Accessibility**: Full WCAG 2.1 AA compliance
- **Internationalization**: Additional language support

### Roadmap Timeline

```mermaid
gantt
    title Security Guard Management App Roadmap
    dateFormat  YYYY-MM-DD
    section Phase 4
    Admin Panel Complete    :done, phase4, 2024-01-01, 2024-12-01
    Event Detail System     :done, 2024-11-01, 2024-12-01
    section Phase 5
    Guard Mobile App        :active, phase5, 2024-12-01, 2025-03-01
    Push Notifications      :2025-01-01, 2025-02-01
    section Phase 6
    Advanced Features       :phase6, 2025-03-01, 2025-06-01
    Geolocation            :2025-03-01, 2025-04-01
    Chat System            :2025-04-01, 2025-05-01
    section Phase 7
    Enterprise Features     :phase7, 2025-06-01, 2025-09-01
    Multi-company Support  :2025-06-01, 2025-07-01
    API Integration        :2025-07-01, 2025-08-01
```

---

## Conclusion

The Security Guard Management App represents a comprehensive solution for security company operations, built with modern technologies and best practices. The current implementation provides a solid foundation for event management, user administration, and reporting, with a clear roadmap for future enhancements.

### Key Achievements
- **80% Complete**: Phase 4 development with robust admin panel
- **Real-time Synchronization**: Firebase integration with live updates
- **Responsive Design**: Optimized for mobile, tablet, and desktop
- **Role-based Security**: Three-tier access control system
- **Multi-language Support**: Slovenian and English localization
- **Professional UI**: Material Design 3 implementation

### Technical Excellence
- **Clean Architecture**: Feature-based organization with clear separation
- **Scalable Infrastructure**: Firebase backend with automatic scaling
- **Modern Development**: Flutter 3.8+ with latest dependencies
- **Quality Assurance**: Comprehensive testing and code quality tools
- **Documentation**: Detailed technical and user documentation

### Business Value
- **Operational Efficiency**: Streamlined guard scheduling and management
- **Compliance**: Digital time tracking for labor regulations
- **Cost Reduction**: Automated processes and reduced manual work
- **Scalability**: Support for growing security companies
- **User Experience**: Intuitive interfaces for all user types

The application's architecture supports scalability, maintainability, and extensibility, making it suitable for both small security companies and large enterprise deployments. With Firebase as the backend infrastructure, the application benefits from real-time synchronization, automatic scaling, and enterprise-grade security, positioning it as a reliable solution for security guard management operations.

---

*Last Updated: December 2024*
*Version: 1.0.0*
*Status: Phase 4 Development - 80% Complete*
*Live Demo: http://localhost:8080*
