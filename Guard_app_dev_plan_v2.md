# Security Guard Management App - Guard Mobile Interface Development Plan v2.0

## 🎯 Goal

Develop a comprehensive mobile-first interface for security guards that integrates seamlessly with our existing admin system to:
- View assigned shifts with custom working hours support
- Respond to shift invitations with interested/unavailable status
- Access detailed event and shift information
- Submit post-shift notes and feedback
- Manage personal profile and license information
- Track shift history and performance

## 📊 Current Status
**Phase**: Guard Mobile App Development (Phase 4 of overall project)
**Progress**: 65% Complete (Phase 1 ✅ Complete, Phase 2 ✅ Complete, Phase 3 🚧 In Progress)
**Branch**: `feature/guard-mobile-app`
**Last Updated**: January 2025

### ✅ Completed Features
- **Phase 1**: Foundation & Authentication (100% Complete)
  - Role-based routing for guard users
  - GuardAppShell with bottom navigation
  - Basic guard pages (Home, Invitations, Profile)
  - Guard-specific localization (30+ keys)
  - Mobile-first responsive design
  - Integration with existing services and theming

- **Phase 2**: Core Guard Interface (100% Complete)
  - Real-time Firestore integration for shifts and invitations
  - Functional invitation response system (interested/unavailable)
  - Guard statistics calculation and display
  - Enhanced UI components (ShiftCard, StatsCard, ShiftDetailPage)
  - Working guard dashboard with live data
  - Pull-to-refresh functionality and error handling
  - Proper status management and user feedback
  - Fixed user provider loading and data display issues
  - Fully functional guard home and profile pages

## 🏗️ Architecture Integration
- **Existing Models**: Leverage EventModel, EventShift, ShiftAssignment, UserModel
- **Existing Services**: Extend EventService, UserService for guard-specific operations
- **Existing Localization**: Use AppLocale system (Slovenian/English)
- **Existing Theme**: Integrate with AppTheme and dark/light mode support
- **Custom Hours**: Full support for admin-set custom working hours

---

## 🚀 Development Phases

### Phase 1: Foundation & Authentication (Week 1)
**Goal**: Set up guard-specific routing and basic structure

#### 1.1 Authentication & Role-based Routing ✅ COMPLETE
- [x] Update main.dart to detect guard role and route to GuardAppShell
- [x] Create GuardAppShell with bottom navigation (Home, Invitations, Profile)
- [x] Implement role-based authentication guard
- [x] Add guard-specific route protection
- [x] Test role-based routing with existing user accounts

#### 1.2 Basic Guard Services ✅ COMPLETE
- [x] Create GuardService extending existing EventService
- [x] Implement getAssignedShifts() method for guard-specific queries
- [x] Implement getPendingInvitations() method
- [x] Add updateShiftResponse() method for invitation responses
- [x] Create guard-specific error handling

#### 1.3 Guard App Shell Structure ✅ COMPLETE
- [x] Design GuardAppShell with responsive bottom navigation
- [x] Implement guard-specific app bar with profile avatar
- [x] Add notification badge for pending invitations
- [x] Integrate with existing AppTheme and localization
- [x] Create guard-specific loading states

### Phase 2: Core Guard Interface ✅ COMPLETE (Week 2)
**Goal**: Implement main guard screens and basic functionality

#### 2.1 Guard Home Screen (Assigned Shifts) ✅ COMPLETE
- [x] Create GuardHomePage with shift list view
- [x] Display upcoming confirmed shifts (status: 'confirmed')
- [x] Show event name, location, and shift times
- [x] Display custom working hours when set by admin
- [x] Add shift status indicators (upcoming, ongoing, completed)
- [x] Implement pull-to-refresh functionality
- [x] Add empty state for no assigned shifts
- [x] Create shift card component with proper styling

#### 2.2 Shift Invitations Screen ✅ COMPLETE
- [x] Create GuardInvitationsPage for pending invitations
- [x] Display shifts with status: 'pending' or null
- [x] Implement "Interested" and "Unavailable" response buttons
- [x] Add confirmation dialogs for responses
- [x] Update ShiftAssignment.status in Firestore
- [x] Show response feedback and success messages
- [x] Add invitation expiry handling
- [x] Implement invitation filtering and sorting

#### 2.3 Shift Detail View ✅ COMPLETE
- [x] Create comprehensive ShiftDetailPage
- [x] Display full event information (title, description, location)
- [x] Show shift-specific details (time, required guards, licenses)
- [x] Display admin notes and special instructions
- [x] Show custom working hours if set
- [x] Add response buttons for pending invitations
- [x] Implement post-shift note submission
- [x] Add shift navigation (previous/next shifts)

### Phase 3: Enhanced Features (Week 3)
**Goal**: Add advanced guard functionality and user experience improvements

#### 3.1 Guard Profile Management
- [ ] Create GuardProfilePage with personal information
- [ ] Display name, email, phone, employment status
- [ ] Show license information (NPK, DELO NA OBJEKTU)
- [ ] Add license expiry warnings
- [ ] Display employment type (FULL TIME, CUSTOM hours)
- [ ] Show EMŠO field (Slovenian citizen ID)
- [ ] Add profile editing capabilities (limited fields)
- [ ] Implement profile photo upload

#### 3.2 Shift History & Analytics
- [ ] Create shift history view with past assignments
- [ ] Display completed shifts with hours worked
- [ ] Show custom hours vs. scheduled hours comparison
- [ ] Add monthly/weekly hour summaries
- [ ] Implement shift performance metrics
- [ ] Create shift search and filtering
- [ ] Add export functionality for personal records
- [ ] Display shift completion statistics

#### 3.3 Enhanced Shift Management
- [ ] Add shift calendar view with monthly/weekly layouts
- [ ] Implement shift conflict detection and warnings
- [ ] Add shift modification request functionality
- [ ] Create shift swap request system (future)
- [ ] Implement shift reminder notifications
- [ ] Add location-based check-in preparation
- [ ] Create shift preparation checklist
- [ ] Add emergency contact information display

### Phase 4: Mobile Optimization & PWA (Week 4)
**Goal**: Optimize for mobile devices and add progressive web app features

#### 4.1 Mobile-First Optimizations
- [ ] Optimize touch targets for mobile devices
- [ ] Implement swipe gestures for navigation
- [ ] Add haptic feedback for important actions
- [ ] Optimize loading performance for mobile networks
- [ ] Implement efficient image loading and caching
- [ ] Add offline capability for essential features
- [ ] Create mobile-specific animations and transitions
- [ ] Test across different mobile screen sizes

#### 4.2 Progressive Web App Features
- [ ] Configure PWA manifest for guard app
- [ ] Implement service worker for offline functionality
- [ ] Add install prompts for mobile browsers
- [ ] Create app-like navigation experience
- [ ] Implement background sync for shift updates
- [ ] Add push notification support
- [ ] Create offline data caching strategy
- [ ] Test PWA installation and functionality

#### 4.3 Notifications & Real-time Updates
- [ ] Implement real-time shift assignment notifications
- [ ] Add shift reminder notifications
- [ ] Create invitation expiry warnings
- [ ] Implement shift change notifications
- [ ] Add emergency notification system
- [ ] Create notification preferences management
- [ ] Test notification delivery across devices
- [ ] Implement notification history

---

## 🔧 Technical Implementation Details

### Data Architecture Integration
```dart
// Leverage existing models
EventModel -> Guard can view event details
EventShift -> Guard can see shift requirements
ShiftAssignment -> Guard's assignment with custom hours
UserModel -> Guard profile with licenses and employment
```

### Guard-Specific Firestore Queries
```dart
// Get assigned shifts for guard
Stream<List<ShiftAssignment>> getAssignedShifts(String guardId)

// Get pending invitations
Stream<List<ShiftAssignment>> getPendingInvitations(String guardId)

// Update shift response
Future<void> updateShiftResponse(String eventId, String shiftId, 
    String guardId, AssignmentStatus status)
```

### Mobile-First UI Components
- **GuardAppShell**: Bottom navigation with Home, Invitations, Profile
- **ShiftCard**: Reusable component for displaying shift information
- **InvitationCard**: Interactive card for responding to invitations
- **ShiftDetailView**: Comprehensive shift information display
- **GuardProfile**: Profile management with license information

### Integration Points
- **Existing Services**: Extend EventService and UserService
- **Existing Models**: Use all current data models without changes
- **Existing Localization**: Leverage AppLocale for Slovenian/English
- **Existing Theme**: Use AppTheme with dark/light mode support
- **Custom Hours**: Display admin-set custom working hours

---

## 📱 Key Features Summary

### Core Functionality
- **Shift Management**: View assigned shifts with custom hours
- **Invitation System**: Respond to shift invitations
- **Profile Management**: View and edit guard profile
- **Shift History**: Track completed shifts and hours

### Advanced Features
- **Real-time Updates**: Live shift assignment notifications
- **Offline Support**: Essential features work without internet
- **PWA Support**: Install as mobile app
- **Calendar View**: Visual shift scheduling
- **Performance Analytics**: Personal shift statistics

### Mobile Optimizations
- **Touch-Friendly**: Large buttons and touch targets
- **Responsive Design**: Works on all mobile screen sizes
- **Fast Loading**: Optimized for mobile networks
- **Offline Capable**: Core features work offline

---

## 🎯 Success Metrics

### User Experience
- Guards can view and respond to shifts within 30 seconds
- 95% of guards successfully install and use the mobile interface
- Reduced response time for shift invitations by 50%

### Technical Performance
- App loads in under 3 seconds on mobile networks
- 99% uptime for real-time notifications
- Offline functionality works for 24+ hours

### Business Value
- Improved guard satisfaction with mobile-first interface
- Faster shift assignment and response times
- Better communication between admins and guards

---

*Prepared: January 2025*
*Version: 2.0 - Integrated Guard Mobile Interface*
