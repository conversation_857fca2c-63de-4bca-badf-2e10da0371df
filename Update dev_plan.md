Development Plan: Event & Shift System for Security Guard App

Goal

Adapt the security guard management app to support complex, multi-day events with multiple shifts and per-guard scheduling, while maintaining simplicity and usability.

⸻

Features

1. Event Types
	•	Each event has a type:
	•	venue (e.g. building security)
	•	event (e.g. concert, festival)
	•	local (e.g. bar/club – requires special license)

2. Event Structure
	•	Events include:
	•	Title, location, date(s), type
	•	Optional notes/instructions for guards
	•	List of shifts (see below)

3. Shift Structure
	•	Each Event contains one or more Shifts
	•	Shift data model:

class EventShift {
  String id;
  DateTime startTime;
  DateTime endTime;
  int requiredGuards;
  List<String> requiredLicenses;
  List<ShiftAssignment> assignments;
}

class ShiftAssignment {
  String guardId;
  DateTime startTime;
  DateTime endTime;
  String status; // interested, unavailable, confirmed
  String? note;  // post-event note from guard
}

4. Guard Interaction
	•	Guards receive notification for each shift they qualify for
	•	They can select:
	•	✅ “Interested”
	•	❌ “Unavailable”
	•	Once confirmed by admin, guard sees full assignment info
	•	Post-event, guard can add note (e.g. extra hours)

5. Admin Workflow
	•	Create Event with multiple Shifts
	•	Set general time range and guard count for each shift
	•	By default, all assigned guards inherit the same time range
	•	<PERSON><PERSON> can override individual guard times inside the shift if needed
	•	Filter guards by:
	•	Employment status (prefer full-time)
	•	License requirements
	•	Availability

6. UI/UX Notes
	•	Event creation page allows adding multiple shifts
	•	Guard list filtered by license and availability
	•	Drag-and-drop or manual input to override guard working time per shift
	•	Mobile-friendly for guards, admin dashboard optionally desktop-first

⸻

Optional Enhancements (Future)
	•	Templates for recurring events
	•	Export to payroll/timesheet system
	•	Analytics: guard utilization, event coverage

⸻

Firebase Data Structure (Simplified)

/events/{eventId}
  - title
  - type
  - location
  - dateRange
  - shifts: [
      shiftId: {
        startTime, endTime, requiredGuards,
        requiredLicenses: [],
        assignments: [
          {
            guardId, startTime, endTime,
            status, note
          }
        ]
      }
    ]


⸻

Implementation Order
	1.	Add event type field and restructure existing events
	2.	Implement shift sub-structure inside event
	3.	Add filtering of guards (by license, availability, status)
	4.	Implement interested/unavailable response system
	5.	Add assignment confirmation logic
	6.	Enable per-guard time overrides per shift
	7.	Build admin UI for shift editing
	8.	Deploy guard-side UI changes (shift view, note entry)
	9.	Testing: long events, overlapping shifts, license restrictions
	10.	Optional: optimize for mobile and desktop UX

⸻

Notes
	•	Goal: avoid complexity in UI while supporting real-world flexibility
	•	No clock-in/out required, just note-based corrections
	•	All communication happens within the app (no SMS/emails needed)

⸻

Prepared: June 2025