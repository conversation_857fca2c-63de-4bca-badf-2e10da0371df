# Security Guard Management App - Guard Mobile Interface Development Plan

## 🎯 Goal

Develop a comprehensive mobile-first interface for security guards that integrates seamlessly with our existing admin system to:
- View assigned shifts with custom working hours support
- Respond to shift invitations with interested/unavailable status
- Access detailed event and shift information
- Submit post-shift notes and feedback
- Manage personal profile and license information
- Track shift history and performance

## 📊 Current Status
**Phase**: Guard Mobile App Development (Phase 4 of overall project)
**Progress**: 0% Complete
**Branch**: `feature/guard-mobile-app`

## 🏗️ Architecture Integration
- **Existing Models**: Leverage EventModel, EventShift, ShiftAssignment, UserModel
- **Existing Services**: Extend EventService, UserService for guard-specific operations
- **Existing Localization**: Use AppLocale system (Slovenian/English)
- **Existing Theme**: Integrate with AppTheme and dark/light mode support
- **Custom Hours**: Full support for admin-set custom working hours

---

## 🚀 Development Phases

### Phase 1: Foundation & Authentication (Week 1)
**Goal**: Set up guard-specific routing and basic structure

1. Authentication & Role-based Routing
	•	On login, check the user’s role from Firestore (users/{uid}/role)
	•	Route guards to GuardAppShell

2. Home Screen (Assigned Shifts)
	•	Display list of upcoming confirmed shifts
	•	Include:
	•	Event name and location
	•	Shift time (start–end)
	•	Optional assigned working hours (custom if set)
	•	Button to open Shift Detail View

3. Invitations Screen
	•	Display all pending shift invitations (status: 'invited' or null)
	•	Allow guard to respond with:
	•	✅ “Interested”
	•	❌ “Unavailable”
	•	Store response in ShiftAssignment.status

4. Shift Detail View
	•	Show full event and shift info:
	•	Location, description, date/time
	•	Admin notes (if any)
	•	Response buttons (if still pending)
	•	Post-event: show optional note input field

5. Profile Screen (Static)
	•	Show name, email, license status, employment type
	•	(Optional future: edit phone number or language preference)

⸻

Firestore Queries Needed
	•	Load confirmed assignments where guardId == currentUser and status == 'confirmed'
	•	Load invitations where guardId == currentUser and status == null || 'invited'
	•	Update ShiftAssignment status and optional custom note

⸻

Firebase Structure (Simplified)

/events/{eventId}/shifts/{shiftId}/assignments/{guardId}:
{
  "guardId": "uid",
  "status": "confirmed" | "invited" | "unavailable",
  "customHours": 6.5,
  "note": "Arrived 30 minutes earlier"
}


⸻

Flutter Components
	•	GuardAppShell (base structure with bottom nav or drawer)
	•	GuardHomePage (confirmed shifts)
	•	GuardInvitationsPage (pending invites)
	•	ShiftDetailPage
	•	GuardProfilePage

⸻

Styling
	•	Mobile-first responsive design
	•	Large buttons for responses (CTA style)
	•	Color coding:
	•	Green: confirmed
	•	Orange: pending response
	•	Red: unavailable

⸻

Optional (Future)
	•	Notifications (via FCM or in-app)
	•	Calendar view
	•	Localization based on user settings

⸻

Prepared: June 2025