# Security Guard Management App

A comprehensive Flutter application for managing security guard shifts, events, and operations. Built with Firebase backend and designed for both web and mobile platforms.

## 🎯 Project Overview

This application provides a complete solution for security companies to manage their guard operations, including event scheduling, shift management, attendance tracking, and comprehensive reporting.

## ✨ Features

### Admin Panel
- **Event Management**: Create, edit, and manage security events with real-time updates
- **User Management**: Manage guards and admin users with role-based access control
- **Reports & Analytics**: Comprehensive reporting with financial insights and performance metrics
- **Settings**: Theme switcher (light/dark/system) and language selection (Slovenian/English)

### Guard Interface
- **Event Dashboard**: View assigned events and shifts
- **Response System**: Accept or decline event assignments
- **Time Tracking**: Clock in/out functionality with attendance tracking
- **Personal Analytics**: View work statistics and earnings

### Technical Features
- **Multi-platform**: iOS, Android, and Web support
- **Real-time Updates**: Live data synchronization across all devices
- **Responsive Design**: Optimized for desktop, tablet, and mobile
- **Internationalization**: Full support for Slovenian and English
- **Theme Support**: Light, dark, and system theme modes
- **Offline Capability**: Basic offline functionality for essential features

## 🛠️ Technology Stack

### Frontend
- **Flutter** - Cross-platform mobile and web development
- **Material Design 3** - Modern UI components and design system
- **Riverpod** - State management and dependency injection
- **GoRouter** - Declarative routing and navigation

### Backend
- **Firebase Firestore** - NoSQL database for real-time data
- **Firebase Authentication** - User authentication and authorization
- **Firebase Functions** - Server-side logic and triggers
- **Firebase Cloud Messaging** - Push notifications
- **Firebase Hosting** - Web application hosting

### Development Tools
- **Flutter Localization** - Internationalization support
- **JSON Serialization** - Type-safe data models
- **Flutter Test** - Unit and widget testing
- **Firebase Security Rules** - Database security and access control

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.0+)
- Dart SDK (3.0+)
- Firebase CLI
- Android Studio / VS Code
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/mcultra88/security-guard-management-app.git
   cd security-guard-management-app
   ```

2. **Install dependencies**
   ```bash
   cd security_guard_app
   flutter pub get
   ```

3. **Firebase Setup**
   ```bash
   # Install Firebase CLI if not already installed
   npm install -g firebase-tools
   
   # Login to Firebase
   firebase login
   
   # Configure Firebase for your project
   flutterfire configure
   ```

4. **Run the application**
   ```bash
   # For web development
   flutter run -d web-server --web-port=8080
   
   # For mobile development
   flutter run
   ```

## 📱 Platform Support

- **Web**: Responsive web application for admin panel
- **iOS**: Native iOS app for guards and admins
- **Android**: Native Android app for guards and admins
- **PWA**: Progressive Web App capabilities for mobile browsers

## 🔐 Authentication & Roles

### User Roles
- **Super Admin**: System-wide management and client administration
- **Admin**: Company-level management of guards and events
- **Guard**: Event participation and time tracking

### Access Control
- Role-based access control (RBAC) with Firebase Custom Claims
- Secure authentication with email/password
- Admin-controlled user creation (no open registration)

## 📊 Current Status

**Version**: 1.0 MVP Complete
**Progress**: 85% of planned features implemented

### Completed Features ✅
- Complete authentication system with role-based access
- Admin panel with event and user management
- Responsive design with theme support
- Reports and analytics system
- Event and user detail pages
- Settings page with theme switcher
- Localization support (Slovenian/English)
- Real-time data synchronization

### Upcoming Features 🚧
- Enhanced event & shift system (v2.0)
- Multi-day events with multiple shifts
- Guard response system (interested/unavailable)
- Advanced scheduling and assignment tools
- Mobile app optimization
- Push notifications

## 🗂️ Project Structure

```
security_guard_app/
├── lib/
│   ├── core/                 # Core utilities and constants
│   │   ├── constants/
│   │   ├── theme/
│   │   └── localization/
│   ├── features/             # Feature-based modules
│   │   ├── auth/
│   │   ├── dashboard/
│   │   ├── events/
│   │   ├── users/
│   │   ├── reports/
│   │   └── settings/
│   ├── shared/               # Shared components
│   │   ├── models/
│   │   ├── services/
│   │   ├── widgets/
│   │   └── providers/
│   └── main.dart
├── assets/                   # Static assets
├── test/                     # Test files
└── docs/                     # Documentation
```

## 🌐 Localization

The app supports multiple languages:
- **Slovenian** (sl) - Primary language
- **English** (en) - Secondary language

Language can be switched in the Settings page, and the preference is persisted across sessions.

## 🎨 Theming

Three theme modes are supported:
- **Light Theme** - Light colors and themes
- **Dark Theme** - Dark colors and themes  
- **System Theme** - Follows device system settings

Theme preference is saved and applied automatically on app restart.

## 📈 Development Roadmap

See [dev_plan_v2.md](dev_plan_v2.md) for the detailed development roadmap including the upcoming Enhanced Event & Shift System.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions, please contact:
- **Developer**: mcultra88
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Firebase team for the robust backend services
- Material Design team for the beautiful design system
- Open source community for the excellent packages and tools

---

**Built with ❤️ using Flutter and Firebase**
