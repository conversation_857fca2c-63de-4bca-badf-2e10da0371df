name: security_guard_app
description: "Security Guard Management App - A mobile-first application for managing security guard shifts, built with Flutter and Firebase."
publish_to: 'none' # Private package

version: 1.0.0+1

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_localization: ^0.2.2

  # UI & Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # Firebase
  firebase_core: ^3.8.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  firebase_messaging: ^15.1.5
  firebase_analytics: ^11.3.5
  firebase_crashlytics: ^4.1.5

  # State Management
  provider: ^6.1.2
  riverpod: ^2.6.1
  flutter_riverpod: ^2.6.1

  # Navigation
  go_router: ^14.6.2

  # Utilities
  intl: ^0.20.2
  shared_preferences: ^2.3.3
  package_info_plus: ^8.1.0
  device_info_plus: ^11.1.0
  url_launcher: ^6.3.1

  # Date & Time
  table_calendar: ^3.1.2

  # File handling & Export
  csv: ^6.0.0
  path_provider: ^2.1.5
  permission_handler: ^11.3.1

  # HTTP & Networking
  http: ^1.2.2
  connectivity_plus: ^6.1.0

  # Local Storage
  sqflite: ^2.4.1

  # UI Components
  flutter_spinkit: ^5.2.1
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2

  # Validation
  email_validator: ^3.0.0

  # JSON Serialization
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Testing
  mockito: ^5.4.4
  build_runner: ^2.4.13
  json_serializable: ^6.8.0

flutter:
  generate: true # Enable internationalization
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/logos/

  # Fonts (using system fonts for now)
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Roboto-Light.ttf
  #         weight: 300
