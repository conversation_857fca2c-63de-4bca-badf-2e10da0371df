{"indexes": [{"collectionGroup": "events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "startDateTime", "order": "ASCENDING"}]}, {"collectionGroup": "events", "queryScope": "COLLECTION", "fields": [{"fieldPath": "assignedGuardIds", "arrayConfig": "CONTAINS"}, {"fieldPath": "startDateTime", "order": "ASCENDING"}]}, {"collectionGroup": "attendance", "queryScope": "COLLECTION", "fields": [{"fieldPath": "guardId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}]}], "fieldOverrides": []}