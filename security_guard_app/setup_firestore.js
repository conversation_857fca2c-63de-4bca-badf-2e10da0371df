const admin = require('firebase-admin');

// Initialize Firebase Admin SDK with project ID
admin.initializeApp({
  projectId: 'security-guard-managemen-c62ef'
});

const firestore = admin.firestore();

async function setupFirestoreData() {
  try {
    console.log('🔥 Setting up Firestore data...');

    // Create admin user document
    const adminUserId = 'SAXlYorWbyUoqB8GwNkqBifiNaL2';
    const adminUserData = {
      email: '<EMAIL>',
      firstName: 'System',
      lastName: 'Administrator',
      phone: '+1234567890',
      role: 'superadmin',
      companyId: 'default-company',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('users').doc(adminUserId).set(adminUserData);
    console.log('✅ Admin user document created');

    // Create guard user document
    const guardUserId = 'sBOFRHIh1UX2DXjLAs9EgD11Jy12';
    const guardUserData = {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Guard',
      phone: '+1234567891',
      role: 'guard',
      companyId: 'default-company',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('users').doc(guardUserId).set(guardUserData);
    console.log('✅ Guard user document created');

    // Create default company
    const companyData = {
      name: 'Default Security Company',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Security Street, Guard City, GC 12345',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('companies').doc('default-company').set(companyData);
    console.log('✅ Default company created');

    // Create a sample event
    const eventData = {
      title: 'Mall Security Shift',
      description: 'Evening security shift at Central Mall',
      location: 'Central Mall, 456 Shopping Ave',
      startDateTime: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // Tomorrow
      endDateTime: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000)), // Tomorrow + 8 hours
      status: 'pending',
      createdBy: adminUserId,
      companyId: 'default-company',
      assignedGuardIds: [guardUserId],
      expectedDurationMinutes: 480, // 8 hours
      hourlyRate: 25.00,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    const eventRef = await firestore.collection('events').add(eventData);
    console.log('✅ Sample event created:', eventRef.id);

    // Create attendance record for the event
    const attendanceData = {
      eventId: eventRef.id,
      guardId: guardUserId,
      status: 'pending',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('attendance').add(attendanceData);
    console.log('✅ Sample attendance record created');

    console.log('\n🎉 Firestore setup completed successfully!');
    console.log('\n📋 Test Credentials:');
    console.log('Admin Login:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: TestAdmin123!');
    console.log('\nGuard Login:');
    console.log('  Email: <EMAIL>');
    console.log('  Password: TestGuard123!');
    console.log('\n🌐 Test your app at: http://localhost:8080');

  } catch (error) {
    console.error('❌ Error setting up Firestore:', error);
  }

  process.exit(0);
}

setupFirestoreData();
