rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload their profile images
    match /users/{userId}/profile.{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Company files - only admins can manage
    match /companies/{companyId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && exists(/databases/(default)/documents/users/$(request.auth.uid))
        && get(/databases/(default)/documents/users/$(request.auth.uid)).data.role in ['admin', 'superadmin']
        && get(/databases/(default)/documents/users/$(request.auth.uid)).data.companyId == companyId;
    }
    
    // Event attachments
    match /events/{eventId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null 
        && exists(/databases/(default)/documents/users/$(request.auth.uid))
        && get(/databases/(default)/documents/users/$(request.auth.uid)).data.role in ['admin', 'superadmin'];
    }
  }
}
