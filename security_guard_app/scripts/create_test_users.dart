import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// Import your firebase options
import '../lib/firebase_options.dart';

Future<void> main() async {
  print('🔥 Setting up Firebase test users...');
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final auth = FirebaseAuth.instance;
  final firestore = FirebaseFirestore.instance;

  try {
    // Create admin user
    print('👤 Creating admin user...');
    
    UserCredential? adminCredential;
    try {
      adminCredential = await auth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'TestAdmin123!',
      );
      print('✅ Admin user created: ${adminCredential.user?.uid}');
    } catch (e) {
      if (e.toString().contains('email-already-in-use')) {
        print('ℹ️  Admin user already exists');
        adminCredential = await auth.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'TestAdmin123!',
        );
      } else {
        print('❌ Error creating admin user: $e');
      }
    }

    if (adminCredential?.user != null) {
      // Create admin user document
      await firestore.collection('users').doc(adminCredential!.user!.uid).set({
        'email': '<EMAIL>',
        'firstName': 'System',
        'lastName': 'Administrator',
        'phone': '+1234567890',
        'role': 'superadmin',
        'companyId': 'default-company',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('✅ Admin user document created in Firestore');
    }

    // Sign out admin
    await auth.signOut();

    // Create guard user
    print('👮 Creating guard user...');
    
    UserCredential? guardCredential;
    try {
      guardCredential = await auth.createUserWithEmailAndPassword(
        email: '<EMAIL>',
        password: 'TestGuard123!',
      );
      print('✅ Guard user created: ${guardCredential.user?.uid}');
    } catch (e) {
      if (e.toString().contains('email-already-in-use')) {
        print('ℹ️  Guard user already exists');
        guardCredential = await auth.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'TestGuard123!',
        );
      } else {
        print('❌ Error creating guard user: $e');
      }
    }

    if (guardCredential?.user != null) {
      // Create guard user document
      await firestore.collection('users').doc(guardCredential!.user!.uid).set({
        'email': '<EMAIL>',
        'firstName': 'John',
        'lastName': 'Guard',
        'phone': '+1234567891',
        'role': 'guard',
        'companyId': 'default-company',
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      print('✅ Guard user document created in Firestore');
    }

    // Sign out guard
    await auth.signOut();

    // Create default company
    print('🏢 Creating default company...');
    await firestore.collection('companies').doc('default-company').set({
      'name': 'Default Security Company',
      'email': '<EMAIL>',
      'phone': '+1234567890',
      'address': '123 Security Street, Guard City, GC 12345',
      'isActive': true,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    print('✅ Default company created');

    print('\n🎉 Setup completed successfully!');
    print('\n📋 Test Credentials:');
    print('Admin Login:');
    print('  Email: <EMAIL>');
    print('  Password: TestAdmin123!');
    print('\nGuard Login:');
    print('  Email: <EMAIL>');
    print('  Password: TestGuard123!');
    print('\n🌐 Test your app at: http://localhost:8080');

  } catch (e) {
    print('❌ Error: $e');
  }

  exit(0);
}
