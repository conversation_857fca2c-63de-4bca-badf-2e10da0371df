const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
const serviceAccount = require('../firebase-service-account.json'); // You'll need to download this

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'security-guard-managemen-c62ef'
});

const auth = admin.auth();
const firestore = admin.firestore();

async function setupFirebase() {
  try {
    console.log('🔥 Setting up Firebase for Security Guard Management App...');

    // Create a test admin user
    const adminEmail = '<EMAIL>';
    const adminPassword = 'TestAdmin123!';
    
    console.log('👤 Creating admin user...');
    
    let adminUser;
    try {
      // Try to create the user
      adminUser = await auth.createUser({
        email: adminEmail,
        password: adminPassword,
        displayName: 'System Administrator',
        emailVerified: true
      });
      console.log('✅ Admin user created:', adminUser.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        console.log('ℹ️  Admin user already exists, getting user...');
        adminUser = await auth.getUserByEmail(adminEmail);
      } else {
        throw error;
      }
    }

    // Create admin user document in Firestore
    console.log('📄 Creating admin user document in Firestore...');
    
    const adminUserData = {
      email: adminEmail,
      firstName: 'System',
      lastName: 'Administrator',
      phone: '+1234567890',
      role: 'superadmin',
      companyId: 'default-company',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('users').doc(adminUser.uid).set(adminUserData);
    console.log('✅ Admin user document created in Firestore');

    // Create default company
    console.log('🏢 Creating default company...');
    
    const companyData = {
      name: 'Default Security Company',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Security Street, Guard City, GC 12345',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('companies').doc('default-company').set(companyData);
    console.log('✅ Default company created');

    // Create a test guard user
    const guardEmail = '<EMAIL>';
    const guardPassword = 'TestGuard123!';
    
    console.log('👮 Creating test guard user...');
    
    let guardUser;
    try {
      guardUser = await auth.createUser({
        email: guardEmail,
        password: guardPassword,
        displayName: 'Test Guard',
        emailVerified: true
      });
      console.log('✅ Guard user created:', guardUser.uid);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        console.log('ℹ️  Guard user already exists, getting user...');
        guardUser = await auth.getUserByEmail(guardEmail);
      } else {
        throw error;
      }
    }

    // Create guard user document in Firestore
    const guardUserData = {
      email: guardEmail,
      firstName: 'John',
      lastName: 'Guard',
      phone: '+1234567891',
      role: 'guard',
      companyId: 'default-company',
      isActive: true,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('users').doc(guardUser.uid).set(guardUserData);
    console.log('✅ Guard user document created in Firestore');

    // Create a sample event
    console.log('📅 Creating sample event...');
    
    const eventData = {
      title: 'Mall Security Shift',
      description: 'Evening security shift at Central Mall',
      location: 'Central Mall, 456 Shopping Ave',
      startDateTime: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000)), // Tomorrow
      endDateTime: admin.firestore.Timestamp.fromDate(new Date(Date.now() + 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000)), // Tomorrow + 8 hours
      status: 'pending',
      createdBy: adminUser.uid,
      companyId: 'default-company',
      assignedGuardIds: [guardUser.uid],
      expectedDurationMinutes: 480, // 8 hours
      hourlyRate: 25.00,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    const eventRef = await firestore.collection('events').add(eventData);
    console.log('✅ Sample event created:', eventRef.id);

    // Create attendance record for the event
    const attendanceData = {
      eventId: eventRef.id,
      guardId: guardUser.uid,
      status: 'pending',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await firestore.collection('attendance').add(attendanceData);
    console.log('✅ Sample attendance record created');

    console.log('\n🎉 Firebase setup completed successfully!');
    console.log('\n📋 Test Credentials:');
    console.log('Admin Login:');
    console.log(`  Email: ${adminEmail}`);
    console.log(`  Password: ${adminPassword}`);
    console.log('\nGuard Login:');
    console.log(`  Email: ${guardEmail}`);
    console.log(`  Password: ${guardPassword}`);
    console.log('\n🌐 Test your app at: http://localhost:8080');

  } catch (error) {
    console.error('❌ Error setting up Firebase:', error);
  }
}

setupFirebase();
