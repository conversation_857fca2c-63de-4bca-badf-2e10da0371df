// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceModel _$AttendanceModelFromJson(Map<String, dynamic> json) =>
    AttendanceModel(
      id: json['id'] as String,
      eventId: json['eventId'] as String,
      guardId: json['guardId'] as String,
      status: json['status'] as String,
      clockInTime: json['clockInTime'] == null
          ? null
          : DateTime.parse(json['clockInTime'] as String),
      clockOutTime: json['clockOutTime'] == null
          ? null
          : DateTime.parse(json['clockOutTime'] as String),
      clockInLocation: json['clockInLocation'] as String?,
      clockOutLocation: json['clockOutLocation'] as String?,
      notes: json['notes'] as String?,
      actualHours: (json['actualHours'] as num?)?.toDouble(),
      totalPay: (json['totalPay'] as num?)?.toDouble(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$AttendanceModelToJson(AttendanceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'eventId': instance.eventId,
      'guardId': instance.guardId,
      'status': instance.status,
      'clockInTime': instance.clockInTime?.toIso8601String(),
      'clockOutTime': instance.clockOutTime?.toIso8601String(),
      'clockInLocation': instance.clockInLocation,
      'clockOutLocation': instance.clockOutLocation,
      'notes': instance.notes,
      'actualHours': instance.actualHours,
      'totalPay': instance.totalPay,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };
