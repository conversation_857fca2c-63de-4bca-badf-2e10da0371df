import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

part 'guard_license_model.g.dart';

enum LicenseType {
  basic,
  venue,
  event,
  local,
  firearms,
  crowd_control,
  first_aid,
  fire_safety,
}

enum LicenseStatus {
  active,
  expired,
  suspended,
  pending_renewal,
}

@JsonSerializable()
class GuardLicense {
  final String id;
  final String guardId;
  final LicenseType type;
  final String licenseNumber;
  final String issuingAuthority;
  final DateTime issueDate;
  final DateTime expiryDate;
  final LicenseStatus status;
  final String? notes;
  final List<String> attachments; // URLs to license documents
  final DateTime createdAt;
  final DateTime updatedAt;

  const GuardLicense({
    required this.id,
    required this.guardId,
    required this.type,
    required this.licenseNumber,
    required this.issuingAuthority,
    required this.issueDate,
    required this.expiryDate,
    required this.status,
    this.notes,
    required this.attachments,
    required this.createdAt,
    required this.updatedAt,
  });

  factory GuardLicense.fromJson(Map<String, dynamic> json) => _$GuardLicenseFromJson(json);
  Map<String, dynamic> toJson() => _$GuardLicenseToJson(this);

  factory GuardLicense.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return GuardLicense(
      id: doc.id,
      guardId: data['guardId'] as String,
      type: LicenseType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => LicenseType.basic,
      ),
      licenseNumber: data['licenseNumber'] as String,
      issuingAuthority: data['issuingAuthority'] as String,
      issueDate: (data['issueDate'] as Timestamp).toDate(),
      expiryDate: (data['expiryDate'] as Timestamp).toDate(),
      status: LicenseStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => LicenseStatus.active,
      ),
      notes: data['notes'] as String?,
      attachments: List<String>.from(data['attachments'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'guardId': guardId,
      'type': type.name,
      'licenseNumber': licenseNumber,
      'issuingAuthority': issuingAuthority,
      'issueDate': Timestamp.fromDate(issueDate),
      'expiryDate': Timestamp.fromDate(expiryDate),
      'status': status.name,
      'notes': notes,
      'attachments': attachments,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  GuardLicense copyWith({
    String? id,
    String? guardId,
    LicenseType? type,
    String? licenseNumber,
    String? issuingAuthority,
    DateTime? issueDate,
    DateTime? expiryDate,
    LicenseStatus? status,
    String? notes,
    List<String>? attachments,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return GuardLicense(
      id: id ?? this.id,
      guardId: guardId ?? this.guardId,
      type: type ?? this.type,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      issuingAuthority: issuingAuthority ?? this.issuingAuthority,
      issueDate: issueDate ?? this.issueDate,
      expiryDate: expiryDate ?? this.expiryDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get isActive => status == LicenseStatus.active;
  
  bool get isExpired => status == LicenseStatus.expired || DateTime.now().isAfter(expiryDate);
  
  bool get isExpiringSoon {
    final now = DateTime.now();
    final thirtyDaysFromNow = now.add(const Duration(days: 30));
    return expiryDate.isBefore(thirtyDaysFromNow) && expiryDate.isAfter(now);
  }
  
  int get daysUntilExpiry => expiryDate.difference(DateTime.now()).inDays;
  
  bool get isValid => isActive && !isExpired;
  
  String get displayName {
    switch (type) {
      case LicenseType.basic:
        return 'Basic Security License';
      case LicenseType.venue:
        return 'Venue Security License';
      case LicenseType.event:
        return 'Event Security License';
      case LicenseType.local:
        return 'Local Security License';
      case LicenseType.firearms:
        return 'Firearms License';
      case LicenseType.crowd_control:
        return 'Crowd Control License';
      case LicenseType.first_aid:
        return 'First Aid Certification';
      case LicenseType.fire_safety:
        return 'Fire Safety Certification';
    }
  }
  
  String get statusDisplayName {
    switch (status) {
      case LicenseStatus.active:
        return 'Active';
      case LicenseStatus.expired:
        return 'Expired';
      case LicenseStatus.suspended:
        return 'Suspended';
      case LicenseStatus.pending_renewal:
        return 'Pending Renewal';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is GuardLicense &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          guardId == other.guardId &&
          type == other.type &&
          licenseNumber == other.licenseNumber;

  @override
  int get hashCode =>
      id.hashCode ^
      guardId.hashCode ^
      type.hashCode ^
      licenseNumber.hashCode;

  @override
  String toString() {
    return 'GuardLicense{id: $id, guardId: $guardId, type: $type, licenseNumber: $licenseNumber, status: $status}';
  }
}
