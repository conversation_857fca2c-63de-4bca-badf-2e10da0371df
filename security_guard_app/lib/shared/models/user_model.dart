import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'guard_license_model.dart';

part 'user_model.g.dart';

enum EmploymentStatus {
  full_time,
  custom,
  inactive,
}

enum SlovenianLicenseType {
  npk_gostinski_lokali, // NPK GOSTINSKI LOKALI (Hospitality Venues License)
  delo_na_objektu,      // DELO NA OBJEKTU (On-Site Work License)
}

@JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String phone;
  final String role; // 'superadmin', 'admin', 'guard'
  final String? companyId;
  final String? profileImageUrl;
  final bool isActive;
  final EmploymentStatus? employmentStatus; // Enhanced: employment status for guards
  final int? customWorkingHours; // New: custom working hours per week (for custom employment status)
  final String? emso; // New: EMŠO (Unique Master Citizen Number) for Slovenia
  final List<SlovenianLicenseType> slovenianLicenses; // New: Slovenian-specific licenses
  final List<String> licenseIds; // Legacy: list of license IDs (for backward compatibility)
  final List<String> availabilityDays; // Available days of week
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;

  UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.role,
    this.companyId,
    this.profileImageUrl,
    this.isActive = true,
    this.employmentStatus,
    this.customWorkingHours,
    this.emso,
    this.slovenianLicenses = const [],
    this.licenseIds = const [],
    this.availabilityDays = const [],
    required this.createdAt,
    this.updatedAt,
    this.metadata,
  });

  String get fullName => '$firstName $lastName';

  bool get isSuperAdmin => role == 'superadmin';
  bool get isAdmin => role == 'admin';
  bool get isGuard => role == 'guard';

  bool get canManageUsers => isSuperAdmin || isAdmin;
  bool get canCreateEvents => isSuperAdmin || isAdmin;
  bool get canViewReports => isSuperAdmin || isAdmin;

  // Slovenian-specific helper methods
  bool get hasNpkLicense => slovenianLicenses.contains(SlovenianLicenseType.npk_gostinski_lokali);
  bool get hasDeloNaObjektuLicense => slovenianLicenses.contains(SlovenianLicenseType.delo_na_objektu);
  bool get hasAllSlovenianLicenses => slovenianLicenses.length == SlovenianLicenseType.values.length;

  /// Get availability days (computed property for backward compatibility)
  List<String> get computedAvailabilityDays {
    if (availabilityDays.isNotEmpty) {
      return availabilityDays;
    }
    // Default to all days if not specified
    return ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
  }

  String get employmentStatusDisplay {
    if (employmentStatus == null) return 'Not specified';
    switch (employmentStatus!) {
      case EmploymentStatus.full_time:
        return 'Full Time (40h/week)';
      case EmploymentStatus.custom:
        return customWorkingHours != null ? 'Custom (${customWorkingHours}h/week)' : 'Custom';
      case EmploymentStatus.inactive:
        return 'Inactive';
    }
  }

  bool get isValidEmso {
    if (emso == null || emso!.length != 13) return false;
    // Basic EMŠO validation - should be 13 digits
    return RegExp(r'^\d{13}$').hasMatch(emso!);
  }

  List<String> get slovenianLicenseDisplayNames {
    return slovenianLicenses.map((license) {
      switch (license) {
        case SlovenianLicenseType.npk_gostinski_lokali:
          return 'NPK GOSTINSKI LOKALI';
        case SlovenianLicenseType.delo_na_objektu:
          return 'DELO NA OBJEKTU';
      }
    }).toList();
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      phone: json['phone'] as String,
      role: json['role'] as String,
      companyId: json['companyId'] as String?,
      profileImageUrl: json['profileImageUrl'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      employmentStatus: json['employmentStatus'] != null
          ? EmploymentStatus.values.firstWhere(
              (e) => e.name == json['employmentStatus'],
              orElse: () => EmploymentStatus.full_time,
            )
          : null,
      customWorkingHours: json['customWorkingHours'] as int?,
      emso: json['emso'] as String?,
      slovenianLicenses: json['slovenianLicenses'] != null
          ? (json['slovenianLicenses'] as List)
              .map((e) => SlovenianLicenseType.values.firstWhere(
                    (license) => license.name == e,
                    orElse: () => SlovenianLicenseType.npk_gostinski_lokali,
                  ))
              .toList()
          : [],
      licenseIds: List<String>.from(json['licenseIds'] ?? []),
      availabilityDays: List<String>.from(json['availabilityDays'] ?? []),
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: _parseDateTime(json['updatedAt']),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();
    if (value is Timestamp) return value.toDate();
    if (value is String) return DateTime.parse(value);
    if (value is DateTime) return value;
    return DateTime.now();
  }

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? role,
    String? companyId,
    String? profileImageUrl,
    bool? isActive,
    EmploymentStatus? employmentStatus,
    int? customWorkingHours,
    String? emso,
    List<SlovenianLicenseType>? slovenianLicenses,
    List<String>? licenseIds,
    List<String>? availabilityDays,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      companyId: companyId ?? this.companyId,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isActive: isActive ?? this.isActive,
      employmentStatus: employmentStatus ?? this.employmentStatus,
      customWorkingHours: customWorkingHours ?? this.customWorkingHours,
      emso: emso ?? this.emso,
      slovenianLicenses: slovenianLicenses ?? this.slovenianLicenses,
      licenseIds: licenseIds ?? this.licenseIds,
      availabilityDays: availabilityDays ?? this.availabilityDays,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, email: $email, fullName: $fullName, role: $role)';
  }
}
