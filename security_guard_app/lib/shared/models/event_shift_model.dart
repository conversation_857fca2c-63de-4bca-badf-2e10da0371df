import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

import 'shift_assignment_model.dart';

part 'event_shift_model.g.dart';

@JsonSerializable()
class EventShift {
  final String id;
  final DateTime startTime;
  final DateTime endTime;
  final int requiredGuards;
  final List<String> requiredLicenses;
  final List<ShiftAssignment> assignments;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EventShift({
    required this.id,
    required this.startTime,
    required this.endTime,
    required this.requiredGuards,
    required this.requiredLicenses,
    required this.assignments,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory EventShift.fromJson(Map<String, dynamic> json) => _$EventShiftFromJson(json);
  Map<String, dynamic> toJson() => _$EventShiftToJson(this);

  factory EventShift.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return EventShift.fromFirestoreData(data, doc.id);
  }

  factory EventShift.fromFirestoreData(Map<String, dynamic> data, [String? id]) {
    return EventShift(
      id: id ?? data['id'] as String? ?? '',
      startTime: _parseDateTime(data['startTime']),
      endTime: _parseDateTime(data['endTime']),
      requiredGuards: (data['requiredGuards'] as num?)?.toInt() ?? 1,
      requiredLicenses: List<String>.from(data['requiredLicenses'] ?? []),
      assignments: (data['assignments'] as List<dynamic>?)
          ?.map((assignment) => ShiftAssignment.fromFirestore(assignment as Map<String, dynamic>))
          .toList() ?? [],
      notes: data['notes'] as String?,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now();
    }
    if (value is Timestamp) {
      return value.toDate();
    }
    if (value is String) {
      return DateTime.parse(value);
    }
    if (value is DateTime) {
      return value;
    }
    return DateTime.now(); // Fallback instead of throwing
  }

  Map<String, dynamic> toFirestore() {
    return {
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'requiredGuards': requiredGuards,
      'requiredLicenses': requiredLicenses,
      'assignments': assignments.map((assignment) => assignment.toFirestore()).toList(),
      'notes': notes,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  EventShift copyWith({
    String? id,
    DateTime? startTime,
    DateTime? endTime,
    int? requiredGuards,
    List<String>? requiredLicenses,
    List<ShiftAssignment>? assignments,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EventShift(
      id: id ?? this.id,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      requiredGuards: requiredGuards ?? this.requiredGuards,
      requiredLicenses: requiredLicenses ?? this.requiredLicenses,
      assignments: assignments ?? this.assignments,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  Duration get duration => endTime.difference(startTime);
  
  int get assignedGuardsCount => assignments.where((a) => a.status == 'confirmed').length;
  
  int get remainingGuardsNeeded => requiredGuards - assignedGuardsCount;
  
  bool get isFullyStaffed => assignedGuardsCount >= requiredGuards;
  
  bool get hasUnfilledPositions => assignedGuardsCount < requiredGuards;
  
  List<ShiftAssignment> get confirmedAssignments => 
      assignments.where((a) => a.status == 'confirmed').toList();
  
  List<ShiftAssignment> get interestedAssignments => 
      assignments.where((a) => a.status == 'interested').toList();
  
  List<ShiftAssignment> get unavailableAssignments => 
      assignments.where((a) => a.status == 'unavailable').toList();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is EventShift &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          startTime == other.startTime &&
          endTime == other.endTime &&
          requiredGuards == other.requiredGuards;

  @override
  int get hashCode =>
      id.hashCode ^
      startTime.hashCode ^
      endTime.hashCode ^
      requiredGuards.hashCode;

  @override
  String toString() {
    return 'EventShift{id: $id, startTime: $startTime, endTime: $endTime, requiredGuards: $requiredGuards, assignedGuards: $assignedGuardsCount}';
  }
}
