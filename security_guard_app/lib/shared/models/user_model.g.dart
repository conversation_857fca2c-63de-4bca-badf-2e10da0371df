// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
  id: json['id'] as String,
  email: json['email'] as String,
  firstName: json['firstName'] as String,
  lastName: json['lastName'] as String,
  phone: json['phone'] as String,
  role: json['role'] as String,
  companyId: json['companyId'] as String?,
  profileImageUrl: json['profileImageUrl'] as String?,
  isActive: json['isActive'] as bool? ?? true,
  employmentStatus: $enumDecodeNullable(
    _$EmploymentStatusEnumMap,
    json['employmentStatus'],
  ),
  customWorkingHours: (json['customWorkingHours'] as num?)?.toInt(),
  emso: json['emso'] as String?,
  slovenianLicenses:
      (json['slovenianLicenses'] as List<dynamic>?)
          ?.map((e) => $enumDecode(_$SlovenianLicenseTypeEnumMap, e))
          .toList() ??
      const [],
  licenseIds:
      (json['licenseIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  availabilityDays:
      (json['availabilityDays'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'id': instance.id,
  'email': instance.email,
  'firstName': instance.firstName,
  'lastName': instance.lastName,
  'phone': instance.phone,
  'role': instance.role,
  'companyId': instance.companyId,
  'profileImageUrl': instance.profileImageUrl,
  'isActive': instance.isActive,
  'employmentStatus': _$EmploymentStatusEnumMap[instance.employmentStatus],
  'customWorkingHours': instance.customWorkingHours,
  'emso': instance.emso,
  'slovenianLicenses': instance.slovenianLicenses
      .map((e) => _$SlovenianLicenseTypeEnumMap[e]!)
      .toList(),
  'licenseIds': instance.licenseIds,
  'availabilityDays': instance.availabilityDays,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'metadata': instance.metadata,
};

const _$EmploymentStatusEnumMap = {
  EmploymentStatus.full_time: 'full_time',
  EmploymentStatus.custom: 'custom',
  EmploymentStatus.inactive: 'inactive',
};

const _$SlovenianLicenseTypeEnumMap = {
  SlovenianLicenseType.npk_gostinski_lokali: 'npk_gostinski_lokali',
  SlovenianLicenseType.delo_na_objektu: 'delo_na_objektu',
};
