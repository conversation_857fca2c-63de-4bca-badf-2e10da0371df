import 'package:json_annotation/json_annotation.dart';

part 'attendance_model.g.dart';

@JsonSerializable()
class AttendanceModel {
  final String id;
  final String eventId;
  final String guardId;
  final String status; // 'pending', 'accepted', 'declined', 'clocked_in', 'clocked_out'
  final DateTime? clockInTime;
  final DateTime? clockOutTime;
  final String? clockInLocation;
  final String? clockOutLocation;
  final String? notes;
  final double? actualHours;
  final double? totalPay;
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  AttendanceModel({
    required this.id,
    required this.eventId,
    required this.guardId,
    required this.status,
    this.clockInTime,
    this.clockOutTime,
    this.clockInLocation,
    this.clockOutLocation,
    this.notes,
    this.actualHours,
    this.totalPay,
    this.metadata,
    required this.createdAt,
    this.updatedAt,
  });

  bool get isPending => status == 'pending';
  bool get isAccepted => status == 'accepted';
  bool get isDeclined => status == 'declined';
  bool get isClockedIn => status == 'clocked_in';
  bool get isClockedOut => status == 'clocked_out';
  
  bool get canClockIn => isAccepted && clockInTime == null;
  bool get canClockOut => isClockedIn && clockOutTime == null;
  
  Duration? get workedDuration {
    if (clockInTime != null && clockOutTime != null) {
      return clockOutTime!.difference(clockInTime!);
    }
    return null;
  }

  factory AttendanceModel.fromJson(Map<String, dynamic> json) => _$AttendanceModelFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceModelToJson(this);

  AttendanceModel copyWith({
    String? id,
    String? eventId,
    String? guardId,
    String? status,
    DateTime? clockInTime,
    DateTime? clockOutTime,
    String? clockInLocation,
    String? clockOutLocation,
    String? notes,
    double? actualHours,
    double? totalPay,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AttendanceModel(
      id: id ?? this.id,
      eventId: eventId ?? this.eventId,
      guardId: guardId ?? this.guardId,
      status: status ?? this.status,
      clockInTime: clockInTime ?? this.clockInTime,
      clockOutTime: clockOutTime ?? this.clockOutTime,
      clockInLocation: clockInLocation ?? this.clockInLocation,
      clockOutLocation: clockOutLocation ?? this.clockOutLocation,
      notes: notes ?? this.notes,
      actualHours: actualHours ?? this.actualHours,
      totalPay: totalPay ?? this.totalPay,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AttendanceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AttendanceModel(id: $id, eventId: $eventId, guardId: $guardId, status: $status)';
  }
}
