import 'package:json_annotation/json_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'event_shift_model.dart';
import 'shift_assignment_model.dart';

part 'event_model.g.dart';

enum EventType {
  venue,
  event,
  local,
}

@JsonSerializable()
class EventModel {
  final String id;
  final String title;
  final String description;
  final String location;
  final DateTime startDateTime;
  final DateTime endDateTime;
  final EventType type; // New: venue, event, local
  final String status; // 'pending', 'confirmed', 'completed', 'cancelled'
  final String createdBy; // Admin user ID
  final String companyId;
  final List<String> assignedGuardIds; // Deprecated: use shifts instead
  final List<EventShift> shifts; // New: shift-based system
  final int expectedDurationMinutes;
  final List<String> requiredLicenses; // New: required licenses for this event
  final Map<String, dynamic>? metadata;
  final DateTime createdAt;
  final DateTime? updatedAt;

  EventModel({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required this.startDateTime,
    required this.endDateTime,
    required this.type,
    required this.status,
    required this.createdBy,
    required this.companyId,
    required this.assignedGuardIds,
    required this.shifts,
    required this.expectedDurationMinutes,
    required this.requiredLicenses,
    this.metadata,
    required this.createdAt,
    this.updatedAt,
  });

  Duration get duration => endDateTime.difference(startDateTime);
  
  bool get isPending => status == 'pending';
  bool get isConfirmed => status == 'confirmed';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  
  bool get isUpcoming => startDateTime.isAfter(DateTime.now());
  bool get isOngoing => DateTime.now().isAfter(startDateTime) && DateTime.now().isBefore(endDateTime);
  bool get isPast => endDateTime.isBefore(DateTime.now());

  factory EventModel.fromJson(Map<String, dynamic> json) {
    return EventModel(
      id: json['id'] as String? ?? '',
      title: json['title'] as String? ?? '',
      description: json['description'] as String? ?? '',
      location: json['location'] as String? ?? '',
      startDateTime: _parseDateTime(json['startDateTime']),
      endDateTime: _parseDateTime(json['endDateTime']),
      type: json['type'] != null
          ? EventType.values.firstWhere(
              (e) => e.name == json['type'],
              orElse: () => EventType.venue,
            )
          : EventType.venue, // Default for backward compatibility
      status: json['status'] as String? ?? 'pending',
      createdBy: json['createdBy'] as String? ?? '',
      companyId: json['companyId'] as String? ?? 'default-company',
      assignedGuardIds: (json['assignedGuardIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      shifts: (json['shifts'] as List<dynamic>?)
          ?.map((shift) => EventShift.fromJson(shift as Map<String, dynamic>))
          .toList() ?? [],
      expectedDurationMinutes: (json['expectedDurationMinutes'] as num?)?.toInt() ?? 480, // Default 8 hours
      requiredLicenses: List<String>.from(json['requiredLicenses'] ?? []),
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: _parseDateTime(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? _parseDateTime(json['updatedAt']) : null,
    );
  }

  factory EventModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return EventModel(
      id: doc.id,
      title: data['title'] as String? ?? '',
      description: data['description'] as String? ?? '',
      location: data['location'] as String? ?? '',
      startDateTime: _parseDateTime(data['startDateTime']),
      endDateTime: _parseDateTime(data['endDateTime']),
      type: data['type'] != null
          ? EventType.values.firstWhere(
              (e) => e.name == data['type'],
              orElse: () => EventType.venue,
            )
          : EventType.venue, // Default for backward compatibility
      status: data['status'] as String? ?? 'pending',
      createdBy: data['createdBy'] as String? ?? '',
      companyId: data['companyId'] as String? ?? 'default-company',
      assignedGuardIds: (data['assignedGuardIds'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      shifts: (data['shifts'] as List<dynamic>?)
          ?.map((shift) => EventShift.fromFirestoreData(shift as Map<String, dynamic>))
          .toList() ?? [],
      expectedDurationMinutes: (data['expectedDurationMinutes'] as num?)?.toInt() ?? 480, // Default 8 hours
      requiredLicenses: List<String>.from(data['requiredLicenses'] ?? []),
      metadata: data['metadata'] as Map<String, dynamic>?,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: data['updatedAt'] != null ? _parseDateTime(data['updatedAt']) : null,
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now();
    }
    if (value is Timestamp) {
      return value.toDate();
    }
    if (value is String) {
      return DateTime.parse(value);
    }
    if (value is DateTime) {
      return value;
    }
    throw FormatException('Cannot parse DateTime from $value');
  }
  Map<String, dynamic> toJson() => _$EventModelToJson(this);

  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'location': location,
      'startDateTime': Timestamp.fromDate(startDateTime),
      'endDateTime': Timestamp.fromDate(endDateTime),
      'type': type.name,
      'status': status,
      'createdBy': createdBy,
      'companyId': companyId,
      'assignedGuardIds': assignedGuardIds,
      'shifts': shifts.map((shift) => shift.toFirestore()).toList(),
      'expectedDurationMinutes': expectedDurationMinutes,
      'requiredLicenses': requiredLicenses,
      'metadata': metadata,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  EventModel copyWith({
    String? id,
    String? title,
    String? description,
    String? location,
    DateTime? startDateTime,
    DateTime? endDateTime,
    EventType? type,
    String? status,
    String? createdBy,
    String? companyId,
    List<String>? assignedGuardIds,
    List<EventShift>? shifts,
    int? expectedDurationMinutes,
    List<String>? requiredLicenses,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EventModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      startDateTime: startDateTime ?? this.startDateTime,
      endDateTime: endDateTime ?? this.endDateTime,
      type: type ?? this.type,
      status: status ?? this.status,
      createdBy: createdBy ?? this.createdBy,
      companyId: companyId ?? this.companyId,
      assignedGuardIds: assignedGuardIds ?? this.assignedGuardIds,
      shifts: shifts ?? this.shifts,
      expectedDurationMinutes: expectedDurationMinutes ?? this.expectedDurationMinutes,
      requiredLicenses: requiredLicenses ?? this.requiredLicenses,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EventModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'EventModel(id: $id, title: $title, location: $location, status: $status)';
  }
}
