// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_shift_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EventShift _$EventShiftFromJson(Map<String, dynamic> json) => EventShift(
  id: json['id'] as String,
  startTime: DateTime.parse(json['startTime'] as String),
  endTime: DateTime.parse(json['endTime'] as String),
  requiredGuards: (json['requiredGuards'] as num).toInt(),
  requiredLicenses: (json['requiredLicenses'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  assignments: (json['assignments'] as List<dynamic>)
      .map((e) => ShiftAssignment.fromJson(e as Map<String, dynamic>))
      .toList(),
  notes: json['notes'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$EventShiftToJson(EventShift instance) =>
    <String, dynamic>{
      'id': instance.id,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'requiredGuards': instance.requiredGuards,
      'requiredLicenses': instance.requiredLicenses,
      'assignments': instance.assignments,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
