import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

part 'shift_assignment_model.g.dart';

enum AssignmentStatus {
  pending,
  interested,
  unavailable,
  confirmed,
  completed,
  cancelled,
}

@JsonSerializable()
class ShiftAssignment {
  final String guardId;
  final DateTime startTime;
  final DateTime endTime;
  final AssignmentStatus status;
  final String? note;
  final DateTime? responseTime;
  final DateTime? clockInTime;
  final DateTime? clockOutTime;
  final double? actualHours;
  final double? customHours; // Custom working hours override (if null, use time difference)
  final DateTime createdAt;
  final DateTime updatedAt;

  const ShiftAssignment({
    required this.guardId,
    required this.startTime,
    required this.endTime,
    required this.status,
    this.note,
    this.responseTime,
    this.clockInTime,
    this.clockOutTime,
    this.actualHours,
    this.customHours,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ShiftAssignment.fromJson(Map<String, dynamic> json) => _$ShiftAssignmentFromJson(json);
  Map<String, dynamic> toJson() => _$ShiftAssignmentToJson(this);

  factory ShiftAssignment.fromFirestore(Map<String, dynamic> data) {
    return ShiftAssignment(
      guardId: data['guardId'] as String? ?? '',
      startTime: _parseDateTime(data['startTime']),
      endTime: _parseDateTime(data['endTime']),
      status: data['status'] != null
          ? AssignmentStatus.values.firstWhere(
              (e) => e.name == data['status'],
              orElse: () => AssignmentStatus.pending,
            )
          : AssignmentStatus.pending,
      note: data['note'] as String?,
      responseTime: data['responseTime'] != null ? _parseDateTime(data['responseTime']) : null,
      clockInTime: data['clockInTime'] != null ? _parseDateTime(data['clockInTime']) : null,
      clockOutTime: data['clockOutTime'] != null ? _parseDateTime(data['clockOutTime']) : null,
      actualHours: data['actualHours'] as double?,
      customHours: data['customHours'] as double?,
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt: _parseDateTime(data['updatedAt']),
    );
  }

  static DateTime _parseDateTime(dynamic value) {
    if (value == null) {
      return DateTime.now();
    }
    if (value is Timestamp) {
      return value.toDate();
    }
    if (value is String) {
      return DateTime.parse(value);
    }
    if (value is DateTime) {
      return value;
    }
    return DateTime.now(); // Fallback instead of throwing
  }

  Map<String, dynamic> toFirestore() {
    return {
      'guardId': guardId,
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'status': status.name,
      'note': note,
      'responseTime': responseTime != null ? Timestamp.fromDate(responseTime!) : null,
      'clockInTime': clockInTime != null ? Timestamp.fromDate(clockInTime!) : null,
      'clockOutTime': clockOutTime != null ? Timestamp.fromDate(clockOutTime!) : null,
      'actualHours': actualHours,
      'customHours': customHours,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  ShiftAssignment copyWith({
    String? guardId,
    DateTime? startTime,
    DateTime? endTime,
    AssignmentStatus? status,
    String? note,
    DateTime? responseTime,
    DateTime? clockInTime,
    DateTime? clockOutTime,
    double? actualHours,
    double? customHours,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ShiftAssignment(
      guardId: guardId ?? this.guardId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      note: note ?? this.note,
      responseTime: responseTime ?? this.responseTime,
      clockInTime: clockInTime ?? this.clockInTime,
      clockOutTime: clockOutTime ?? this.clockOutTime,
      actualHours: actualHours ?? this.actualHours,
      customHours: customHours ?? this.customHours,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get the effective working hours for this assignment
  /// Returns customHours if set, otherwise calculates from time difference
  double get effectiveHours {
    if (customHours != null) {
      return customHours!;
    }
    return endTime.difference(startTime).inMinutes / 60.0;
  }

  /// Check if this assignment has custom hours set
  bool get hasCustomHours => customHours != null;

  // Helper methods
  Duration get scheduledDuration => endTime.difference(startTime);
  
  double get scheduledHours => scheduledDuration.inMinutes / 60.0;
  
  bool get hasResponded => status != AssignmentStatus.pending;
  
  bool get isConfirmed => status == AssignmentStatus.confirmed;
  
  bool get isCompleted => status == AssignmentStatus.completed;
  
  bool get isClockedIn => clockInTime != null && clockOutTime == null;
  
  bool get isClockedOut => clockInTime != null && clockOutTime != null;
  
  Duration? get actualDuration {
    if (clockInTime != null && clockOutTime != null) {
      return clockOutTime!.difference(clockInTime!);
    }
    return null;
  }
  
  double? get calculatedActualHours {
    final duration = actualDuration;
    return duration != null ? duration.inMinutes / 60.0 : null;
  }
  
  bool get hasOvertime {
    final actual = calculatedActualHours ?? actualHours;
    return actual != null && actual > scheduledHours;
  }
  
  double get overtimeHours {
    final actual = calculatedActualHours ?? actualHours;
    if (actual != null && actual > scheduledHours) {
      return actual - scheduledHours;
    }
    return 0.0;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShiftAssignment &&
          runtimeType == other.runtimeType &&
          guardId == other.guardId &&
          startTime == other.startTime &&
          endTime == other.endTime &&
          status == other.status;

  @override
  int get hashCode =>
      guardId.hashCode ^
      startTime.hashCode ^
      endTime.hashCode ^
      status.hashCode;

  @override
  String toString() {
    return 'ShiftAssignment{guardId: $guardId, status: $status, startTime: $startTime, endTime: $endTime}';
  }
}
