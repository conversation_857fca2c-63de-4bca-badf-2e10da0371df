// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'guard_license_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GuardLicense _$GuardLicenseFromJson(Map<String, dynamic> json) => GuardLicense(
  id: json['id'] as String,
  guardId: json['guardId'] as String,
  type: $enumDecode(_$LicenseTypeEnumMap, json['type']),
  licenseNumber: json['licenseNumber'] as String,
  issuingAuthority: json['issuingAuthority'] as String,
  issueDate: DateTime.parse(json['issueDate'] as String),
  expiryDate: DateTime.parse(json['expiryDate'] as String),
  status: $enumDecode(_$LicenseStatusEnumMap, json['status']),
  notes: json['notes'] as String?,
  attachments: (json['attachments'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$GuardLicenseToJson(GuardLicense instance) =>
    <String, dynamic>{
      'id': instance.id,
      'guardId': instance.guardId,
      'type': _$LicenseTypeEnumMap[instance.type]!,
      'licenseNumber': instance.licenseNumber,
      'issuingAuthority': instance.issuingAuthority,
      'issueDate': instance.issueDate.toIso8601String(),
      'expiryDate': instance.expiryDate.toIso8601String(),
      'status': _$LicenseStatusEnumMap[instance.status]!,
      'notes': instance.notes,
      'attachments': instance.attachments,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$LicenseTypeEnumMap = {
  LicenseType.basic: 'basic',
  LicenseType.venue: 'venue',
  LicenseType.event: 'event',
  LicenseType.local: 'local',
  LicenseType.firearms: 'firearms',
  LicenseType.crowd_control: 'crowd_control',
  LicenseType.first_aid: 'first_aid',
  LicenseType.fire_safety: 'fire_safety',
};

const _$LicenseStatusEnumMap = {
  LicenseStatus.active: 'active',
  LicenseStatus.expired: 'expired',
  LicenseStatus.suspended: 'suspended',
  LicenseStatus.pending_renewal: 'pending_renewal',
};
