// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shift_assignment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShiftAssignment _$ShiftAssignmentFromJson(Map<String, dynamic> json) =>
    ShiftAssignment(
      guardId: json['guardId'] as String,
      startTime: DateTime.parse(json['startTime'] as String),
      endTime: DateTime.parse(json['endTime'] as String),
      status: $enumDecode(_$AssignmentStatusEnumMap, json['status']),
      note: json['note'] as String?,
      responseTime: json['responseTime'] == null
          ? null
          : DateTime.parse(json['responseTime'] as String),
      clockInTime: json['clockInTime'] == null
          ? null
          : DateTime.parse(json['clockInTime'] as String),
      clockOutTime: json['clockOutTime'] == null
          ? null
          : DateTime.parse(json['clockOutTime'] as String),
      actualHours: (json['actualHours'] as num?)?.toDouble(),
      customHours: (json['customHours'] as num?)?.toDouble(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$ShiftAssignmentToJson(ShiftAssignment instance) =>
    <String, dynamic>{
      'guardId': instance.guardId,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime.toIso8601String(),
      'status': _$AssignmentStatusEnumMap[instance.status]!,
      'note': instance.note,
      'responseTime': instance.responseTime?.toIso8601String(),
      'clockInTime': instance.clockInTime?.toIso8601String(),
      'clockOutTime': instance.clockOutTime?.toIso8601String(),
      'actualHours': instance.actualHours,
      'customHours': instance.customHours,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

const _$AssignmentStatusEnumMap = {
  AssignmentStatus.pending: 'pending',
  AssignmentStatus.interested: 'interested',
  AssignmentStatus.unavailable: 'unavailable',
  AssignmentStatus.confirmed: 'confirmed',
  AssignmentStatus.completed: 'completed',
  AssignmentStatus.cancelled: 'cancelled',
};
