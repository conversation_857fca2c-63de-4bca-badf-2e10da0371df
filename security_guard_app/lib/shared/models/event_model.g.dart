// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'event_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EventModel _$EventModelFromJson(Map<String, dynamic> json) => EventModel(
  id: json['id'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  location: json['location'] as String,
  startDateTime: DateTime.parse(json['startDateTime'] as String),
  endDateTime: DateTime.parse(json['endDateTime'] as String),
  type: $enumDecode(_$EventTypeEnumMap, json['type']),
  status: json['status'] as String,
  createdBy: json['createdBy'] as String,
  companyId: json['companyId'] as String,
  assignedGuardIds: (json['assignedGuardIds'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  shifts: (json['shifts'] as List<dynamic>)
      .map((e) => EventShift.fromJson(e as Map<String, dynamic>))
      .toList(),
  expectedDurationMinutes: (json['expectedDurationMinutes'] as num).toInt(),
  requiredLicenses: (json['requiredLicenses'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  metadata: json['metadata'] as Map<String, dynamic>?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$EventModelToJson(EventModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'location': instance.location,
      'startDateTime': instance.startDateTime.toIso8601String(),
      'endDateTime': instance.endDateTime.toIso8601String(),
      'type': _$EventTypeEnumMap[instance.type]!,
      'status': instance.status,
      'createdBy': instance.createdBy,
      'companyId': instance.companyId,
      'assignedGuardIds': instance.assignedGuardIds,
      'shifts': instance.shifts,
      'expectedDurationMinutes': instance.expectedDurationMinutes,
      'requiredLicenses': instance.requiredLicenses,
      'metadata': instance.metadata,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$EventTypeEnumMap = {
  EventType.venue: 'venue',
  EventType.event: 'event',
  EventType.local: 'local',
};
