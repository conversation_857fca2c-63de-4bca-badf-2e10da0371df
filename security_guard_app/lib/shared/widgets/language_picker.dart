import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';

class LanguagePicker extends StatelessWidget {
  final bool showAsDialog;
  final VoidCallback? onLanguageChanged;

  const LanguagePicker({
    super.key,
    this.showAsDialog = false,
    this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    if (showAsDialog) {
      return _buildLanguageDialog(context);
    } else {
      return _buildLanguageButton(context);
    }
  }

  Widget _buildLanguageButton(BuildContext context) {
    final localization = FlutterLocalization.instance;
    final currentLanguage = localization.currentLocale?.languageCode == 'sl'
        ? '🇸🇮 Slovenščina'
        : '🇬🇧 English';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withOpacity(0.2)),
      ),
      child: InkWell(
        onTap: () => _showLanguageDialog(context),
        borderRadius: BorderRadius.circular(12),
        child: Row(
          children: [
            Icon(
              Icons.language,
              color: Colors.white.withOpacity(0.8),
              size: 18,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                currentLanguage,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: Colors.white.withOpacity(0.8),
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageDialog(BuildContext context) {
    final localization = FlutterLocalization.instance;
    
    return AlertDialog(
      title: const Text('Select Language / Izberi jezik'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Text('🇸🇮'),
            title: const Text('Slovenščina'),
            trailing: localization.currentLocale?.languageCode == 'sl'
                ? const Icon(Icons.check, color: Colors.green)
                : null,
            onTap: () {
              _changeLanguage(context, 'sl');
            },
          ),
          ListTile(
            leading: const Text('🇬🇧'),
            title: const Text('English'),
            trailing: localization.currentLocale?.languageCode == 'en'
                ? const Icon(Icons.check, color: Colors.green)
                : null,
            onTap: () {
              _changeLanguage(context, 'en');
            },
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel / Prekliči'),
        ),
      ],
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => LanguagePicker(
        showAsDialog: true,
        onLanguageChanged: onLanguageChanged,
      ),
    );
  }

  void _changeLanguage(BuildContext context, String languageCode) {
    final localization = FlutterLocalization.instance;
    localization.translate(languageCode);
    Navigator.of(context).pop();
    onLanguageChanged?.call();
    
    // Show a snackbar to confirm the change
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          languageCode == 'sl' 
            ? 'Jezik spremenjen v slovenščino'
            : 'Language changed to English',
        ),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

class LanguagePickerTile extends StatelessWidget {
  const LanguagePickerTile({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = FlutterLocalization.instance;
    final currentLanguage = localization.currentLocale?.languageCode == 'sl' 
        ? 'Slovenščina' 
        : 'English';

    return ListTile(
      leading: const Icon(Icons.language),
      title: const Text('Language / Jezik'),
      subtitle: Text(currentLanguage),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: () {
        showDialog(
          context: context,
          builder: (context) => const LanguagePicker(showAsDialog: true),
        );
      },
    );
  }
}
