import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/event_model.dart';

// Provider for EventService
final eventServiceProvider = Provider<EventService>((ref) {
  return EventService();
});

class EventService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all events
  Stream<List<EventModel>> getEvents() {
    return _firestore
        .collection('events')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          return EventModel.fromFirestore(doc);
        } catch (e) {
          debugPrint('Error parsing event ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    }).handleError((error) {
      debugPrint('Error in getEvents stream: $error');
      throw Exception('Failed to load events: ${error.toString()}');
    });
  }

  // Get events for a specific guard
  Stream<List<EventModel>> getEventsForGuard(String guardId) {
    return _firestore
        .collection('events')
        .where('assignedGuardIds', arrayContains: guardId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          return EventModel.fromFirestore(doc);
        } catch (e) {
          debugPrint('Error parsing event ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    }).handleError((error) {
      debugPrint('Error in getEventsForGuard stream: $error');
      throw Exception('Failed to load guard events: ${error.toString()}');
    });
  }

  // Get events by status
  Stream<List<EventModel>> getEventsByStatus(String status) {
    return _firestore
        .collection('events')
        .where('status', isEqualTo: status)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          return EventModel.fromFirestore(doc);
        } catch (e) {
          debugPrint('Error parsing event ${doc.id}: $e');
          rethrow;
        }
      }).toList();
    }).handleError((error) {
      debugPrint('Error in getEventsByStatus stream: $error');
      throw Exception('Failed to load events by status: ${error.toString()}');
    });
  }

  // Create a new event
  Future<String> createEvent(EventModel event) async {
    try {
      final eventData = event.toFirestore();
      eventData.remove('id'); // Remove id for creation
      eventData['createdAt'] = FieldValue.serverTimestamp();
      eventData['updatedAt'] = FieldValue.serverTimestamp();

      final docRef = await _firestore.collection('events').add(eventData);
      return docRef.id;
    } catch (e) {
      debugPrint('Error creating event: $e');
      throw Exception('Failed to create event: ${e.toString()}');
    }
  }

  // Update an event
  Future<void> updateEvent(EventModel event) async {
    try {
      final eventData = event.toFirestore();
      eventData['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore.collection('events').doc(event.id).update(eventData);
    } catch (e) {
      debugPrint('Error updating event: $e');
      throw Exception('Failed to update event: ${e.toString()}');
    }
  }

  // Delete an event
  Future<void> deleteEvent(String eventId) async {
    try {
      await _firestore.collection('events').doc(eventId).delete();
    } catch (e) {
      debugPrint('Error deleting event: $e');
      throw Exception('Failed to delete event: ${e.toString()}');
    }
  }

  // Assign guard to event
  Future<void> assignGuardToEvent(String eventId, String guardId) async {
    try {
      await _firestore.collection('events').doc(eventId).update({
        'assignedGuardIds': FieldValue.arrayUnion([guardId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error assigning guard to event: $e');
      throw Exception('Failed to assign guard: ${e.toString()}');
    }
  }

  // Remove guard from event
  Future<void> removeGuardFromEvent(String eventId, String guardId) async {
    try {
      await _firestore.collection('events').doc(eventId).update({
        'assignedGuardIds': FieldValue.arrayRemove([guardId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error removing guard from event: $e');
      throw Exception('Failed to remove guard: ${e.toString()}');
    }
  }

  // Update event status
  Future<void> updateEventStatus(String eventId, String status) async {
    try {
      await _firestore.collection('events').doc(eventId).update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating event status: $e');
      throw Exception('Failed to update event status: ${e.toString()}');
    }
  }

  // Get event by ID
  Future<EventModel?> getEventById(String eventId) async {
    try {
      final doc = await _firestore.collection('events').doc(eventId).get();
      if (doc.exists) {
        return EventModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting event: $e');
      throw Exception('Failed to get event: ${e.toString()}');
    }
  }

  /// Get events assigned to a specific guard
  Future<List<EventModel>> getEventsByGuard(String guardId) async {
    try {
      final querySnapshot = await _firestore
          .collection('events')
          .where('assignedGuardIds', arrayContains: guardId)
          .orderBy('startDateTime', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        return EventModel.fromFirestore(doc);
      }).toList();
    } catch (e) {
      debugPrint('Error getting events by guard: $e');
      throw Exception('Failed to get events by guard: ${e.toString()}');
    }
  }

  /// Get events for a guard within a specific date range
  Future<List<EventModel>> getEventsByGuardInDateRange(
    String guardId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final querySnapshot = await _firestore
          .collection('events')
          .where('assignedGuardIds', arrayContains: guardId)
          .where('startDateTime', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('startDateTime', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('startDateTime')
          .get();

      return querySnapshot.docs.map((doc) {
        return EventModel.fromFirestore(doc);
      }).toList();
    } catch (e) {
      debugPrint('Error getting events by guard in date range: $e');
      throw Exception('Failed to get events by guard in date range: ${e.toString()}');
    }
  }

  /// Get overlapping events for a guard during a specific time period
  Future<List<EventModel>> getOverlappingEventsForGuard(
    String guardId,
    DateTime startTime,
    DateTime endTime,
  ) async {
    try {
      // Get all events for the guard
      final allEvents = await getEventsByGuard(guardId);

      // Filter for overlapping events
      final overlappingEvents = allEvents.where((event) {
        // Check if event time overlaps with the given time period
        return event.startDateTime.isBefore(endTime) &&
               event.endDateTime.isAfter(startTime);
      }).toList();

      return overlappingEvents;
    } catch (e) {
      debugPrint('Error getting overlapping events: $e');
      throw Exception('Failed to get overlapping events: ${e.toString()}');
    }
  }
}
