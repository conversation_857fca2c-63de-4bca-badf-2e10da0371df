import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/user_model.dart';

// Provider for AuthService
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

// Provider for current user
final currentUserProvider = StateProvider<UserModel?>((ref) {
  return null;
});

// Provider for authentication state
final authStateProvider = StateProvider<AuthState>((ref) {
  return AuthState.unauthenticated;
});

enum AuthState {
  loading,
  authenticated,
  unauthenticated,
}

class AuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentFirebaseUser => _firebaseAuth.currentUser;

  // Get current user data from Firestore
  Future<UserModel?> getCurrentUser() async {
    final user = _firebaseAuth.currentUser;
    if (user == null) return null;

    try {
      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (doc.exists) {
        return UserModel.fromJson({...doc.data()!, 'id': doc.id});
      }
    } catch (e) {
      // Log error in production, you might want to use a proper logging service
      debugPrint('Error getting current user: $e');
    }
    return null;
  }

  // Sign in with email and password
  Future<UserModel?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      UserCredential result = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        return await getCurrentUser();
      }
      return null;
    } catch (e) {
      throw Exception('Failed to sign in: ${e.toString()}');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: ${e.toString()}');
    }
  }

  // Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('Failed to reset password: ${e.toString()}');
    }
  }

  // Check if user is authenticated
  bool get isAuthenticated {
    return _firebaseAuth.currentUser != null;
  }

  // Get user role from Firestore
  Future<String?> getUserRole() async {
    final user = await getCurrentUser();
    return user?.role;
  }

  // Check if user has specific role
  Future<bool> hasRole(String role) async {
    final userRole = await getUserRole();
    return userRole == role;
  }

  // Check if user is admin
  Future<bool> get isAdmin async {
    final userRole = await getUserRole();
    return userRole == 'admin' || userRole == 'superadmin';
  }

  // Check if user is guard
  Future<bool> get isGuard async {
    final userRole = await getUserRole();
    return userRole == 'guard';
  }

  // Check if user is super admin
  Future<bool> get isSuperAdmin async {
    final userRole = await getUserRole();
    return userRole == 'superadmin';
  }

  // Create user in Firestore
  Future<void> createUserDocument({
    required String uid,
    required String email,
    required String firstName,
    required String lastName,
    required String phone,
    required String role,
    String? companyId,
  }) async {
    try {
      final userData = {
        'email': email,
        'firstName': firstName,
        'lastName': lastName,
        'phone': phone,
        'role': role,
        'companyId': companyId,
        'isActive': true,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore.collection('users').doc(uid).set(userData);
    } catch (e) {
      throw Exception('Failed to create user document: ${e.toString()}');
    }
  }

  // Auth state stream
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();
}
