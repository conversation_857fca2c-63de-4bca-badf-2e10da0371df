import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/event_model.dart';
import '../models/event_shift_model.dart';
import '../models/shift_assignment_model.dart';

// Provider for MigrationService
final migrationServiceProvider = Provider<MigrationService>((ref) {
  return MigrationService();
});

class MigrationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Migrates existing events to the new shift-based structure
  Future<void> migrateEventsToShiftSystem() async {
    try {
      debugPrint('Starting event migration to shift system...');
      
      // Get all existing events
      final eventsSnapshot = await _firestore.collection('events').get();
      
      int migratedCount = 0;
      int errorCount = 0;
      
      for (final eventDoc in eventsSnapshot.docs) {
        try {
          await _migrateEvent(eventDoc);
          migratedCount++;
          debugPrint('Migrated event: ${eventDoc.id}');
        } catch (e) {
          errorCount++;
          debugPrint('Error migrating event ${eventDoc.id}: $e');
        }
      }
      
      debugPrint('Migration completed: $migratedCount migrated, $errorCount errors');
    } catch (e) {
      debugPrint('Migration failed: $e');
      rethrow;
    }
  }

  /// Migrates a single event document
  Future<void> _migrateEvent(DocumentSnapshot eventDoc) async {
    final data = eventDoc.data() as Map<String, dynamic>;
    
    // Check if already migrated (has shifts field)
    if (data.containsKey('shifts')) {
      debugPrint('Event ${eventDoc.id} already migrated, skipping');
      return;
    }
    
    // Create default shift from existing event data
    final shift = _createDefaultShift(data);
    
    // Update event with new fields
    final updatedData = {
      ...data,
      'type': _determineEventType(data),
      'shifts': [shift.toFirestore()],
      'requiredLicenses': _extractRequiredLicenses(data),
      'updatedAt': FieldValue.serverTimestamp(),
    };
    
    // Update the document
    await eventDoc.reference.update(updatedData);
  }

  /// Creates a default shift from existing event data
  EventShift _createDefaultShift(Map<String, dynamic> eventData) {
    final startTime = (eventData['startDateTime'] as Timestamp).toDate();
    final endTime = (eventData['endDateTime'] as Timestamp).toDate();
    final assignedGuardIds = List<String>.from(eventData['assignedGuardIds'] ?? []);
    
    // Create shift assignments from existing guard assignments
    final assignments = assignedGuardIds.map((guardId) {
      return ShiftAssignment(
        guardId: guardId,
        startTime: startTime,
        endTime: endTime,
        status: AssignmentStatus.confirmed, // Assume existing assignments are confirmed
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }).toList();
    
    return EventShift(
      id: _generateShiftId(),
      startTime: startTime,
      endTime: endTime,
      requiredGuards: assignedGuardIds.length > 0 ? assignedGuardIds.length : 1,
      requiredLicenses: _extractRequiredLicenses(eventData),
      assignments: assignments,
      notes: 'Migrated from legacy event structure',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Determines event type based on existing data
  String _determineEventType(Map<String, dynamic> eventData) {
    final title = (eventData['title'] as String? ?? '').toLowerCase();
    final description = (eventData['description'] as String? ?? '').toLowerCase();
    final location = (eventData['location'] as String? ?? '').toLowerCase();
    
    // Simple heuristics to determine event type
    if (title.contains('venue') || location.contains('building') || location.contains('office')) {
      return EventType.venue.name;
    } else if (title.contains('event') || title.contains('concert') || title.contains('festival')) {
      return EventType.event.name;
    } else if (title.contains('bar') || title.contains('club') || title.contains('pub')) {
      return EventType.local.name;
    }
    
    // Default to venue
    return EventType.venue.name;
  }

  /// Extracts required licenses based on event type and data
  List<String> _extractRequiredLicenses(Map<String, dynamic> eventData) {
    final eventType = _determineEventType(eventData);
    
    switch (eventType) {
      case 'venue':
        return ['basic', 'venue'];
      case 'event':
        return ['basic', 'event', 'crowd_control'];
      case 'local':
        return ['basic', 'local'];
      default:
        return ['basic'];
    }
  }

  /// Generates a unique shift ID
  String _generateShiftId() {
    return _firestore.collection('shifts').doc().id;
  }

  /// Migrates user data to include new fields
  Future<void> migrateUsersToEnhancedModel() async {
    try {
      debugPrint('Starting user migration to enhanced model...');
      
      final usersSnapshot = await _firestore.collection('users').get();
      
      int migratedCount = 0;
      int errorCount = 0;
      
      for (final userDoc in usersSnapshot.docs) {
        try {
          await _migrateUser(userDoc);
          migratedCount++;
          debugPrint('Migrated user: ${userDoc.id}');
        } catch (e) {
          errorCount++;
          debugPrint('Error migrating user ${userDoc.id}: $e');
        }
      }
      
      debugPrint('User migration completed: $migratedCount migrated, $errorCount errors');
    } catch (e) {
      debugPrint('User migration failed: $e');
      rethrow;
    }
  }

  /// Migrates a single user document
  Future<void> _migrateUser(DocumentSnapshot userDoc) async {
    final data = userDoc.data() as Map<String, dynamic>;
    
    // Check if already migrated (has employmentStatus field)
    if (data.containsKey('employmentStatus')) {
      debugPrint('User ${userDoc.id} already migrated, skipping');
      return;
    }
    
    // Add new fields with defaults
    final updatedData = {
      ...data,
      'employmentStatus': _determineEmploymentStatus(data),
      'licenseIds': <String>[], // Empty initially, to be populated later
      'availabilityDays': _getDefaultAvailabilityDays(),
      'hourlyRate': _getDefaultHourlyRate(data),
      'updatedAt': FieldValue.serverTimestamp(),
    };
    
    // Update the document
    await userDoc.reference.update(updatedData);
  }

  /// Determines employment status based on existing data
  String? _determineEmploymentStatus(Map<String, dynamic> userData) {
    final role = userData['role'] as String?;
    
    // Only set employment status for guards
    if (role == 'guard') {
      return 'full_time'; // Default to full-time
    }
    
    return null; // Admins and superadmins don't have employment status
  }

  /// Gets default availability days (all days)
  List<String> _getDefaultAvailabilityDays() {
    return ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  }

  /// Gets default hourly rate based on user data
  double? _getDefaultHourlyRate(Map<String, dynamic> userData) {
    final role = userData['role'] as String?;
    
    // Only set hourly rate for guards
    if (role == 'guard') {
      return 15.0; // Default hourly rate in EUR
    }
    
    return null;
  }

  /// Runs all migrations
  Future<void> runAllMigrations() async {
    debugPrint('Starting complete migration process...');
    
    try {
      // Migrate events first
      await migrateEventsToShiftSystem();
      
      // Then migrate users
      await migrateUsersToEnhancedModel();
      
      debugPrint('All migrations completed successfully!');
    } catch (e) {
      debugPrint('Migration process failed: $e');
      rethrow;
    }
  }

  /// Checks if migrations are needed
  Future<bool> isMigrationNeeded() async {
    try {
      // Check if any events lack the new structure
      final eventsSnapshot = await _firestore
          .collection('events')
          .where('shifts', isNull: true)
          .limit(1)
          .get();
      
      if (eventsSnapshot.docs.isNotEmpty) {
        return true;
      }
      
      // Check if any users lack the new structure
      final usersSnapshot = await _firestore
          .collection('users')
          .where('role', isEqualTo: 'guard')
          .where('employmentStatus', isNull: true)
          .limit(1)
          .get();
      
      return usersSnapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking migration status: $e');
      return false;
    }
  }

  /// Creates backup of existing data before migration
  Future<void> createBackup() async {
    try {
      debugPrint('Creating backup before migration...');
      
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      
      // Backup events
      final eventsSnapshot = await _firestore.collection('events').get();
      final batch = _firestore.batch();
      
      for (final doc in eventsSnapshot.docs) {
        final backupRef = _firestore
            .collection('backups')
            .doc('events_$timestamp')
            .collection('events')
            .doc(doc.id);
        batch.set(backupRef, doc.data());
      }
      
      // Backup users
      final usersSnapshot = await _firestore.collection('users').get();
      
      for (final doc in usersSnapshot.docs) {
        final backupRef = _firestore
            .collection('backups')
            .doc('users_$timestamp')
            .collection('users')
            .doc(doc.id);
        batch.set(backupRef, doc.data());
      }
      
      await batch.commit();
      debugPrint('Backup created successfully with timestamp: $timestamp');
    } catch (e) {
      debugPrint('Backup creation failed: $e');
      rethrow;
    }
  }
}
