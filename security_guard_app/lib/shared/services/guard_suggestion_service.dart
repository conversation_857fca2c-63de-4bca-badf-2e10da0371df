import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/user_model.dart';
import '../models/event_model.dart';
import '../models/event_shift_model.dart';
import '../models/shift_assignment_model.dart';
import 'user_service.dart';
import 'event_service.dart';

// Provider for the guard suggestion service
final guardSuggestionServiceProvider = Provider<GuardSuggestionService>((ref) {
  return GuardSuggestionService(
    userService: ref.read(userServiceProvider),
    eventService: ref.read(eventServiceProvider),
  );
});

/// Service for intelligent guard suggestions and matching
class GuardSuggestionService {
  final UserService _userService;
  final EventService _eventService;

  GuardSuggestionService({
    required UserService userService,
    required EventService eventService,
  }) : _userService = userService,
       _eventService = eventService;

  /// Get intelligent guard suggestions for a specific shift
  Future<List<GuardSuggestion>> getGuardSuggestionsForShift({
    required EventShift shift,
    required EventModel event,
    List<UserModel>? availableGuards,
  }) async {
    // Get all guards if not provided
    if (availableGuards == null) {
      final guardsStream = _userService.getGuards();
      availableGuards = await guardsStream.first;
    }

    final suggestions = <GuardSuggestion>[];

    for (final guard in availableGuards) {
      if (!guard.isActive || !guard.isGuard) continue;
      
      final score = await _calculateMatchScore(
        guard: guard,
        shift: shift,
        event: event,
      );
      
      final availability = await _checkAvailability(guard, shift);
      final conflicts = await _getScheduleConflicts(guard, shift);
      
      suggestions.add(GuardSuggestion(
        guard: guard,
        matchScore: score,
        availability: availability,
        conflicts: conflicts,
        reasons: _generateSuggestionReasons(guard, shift, event, score),
      ));
    }
    
    // Sort by match score (highest first)
    suggestions.sort((a, b) => b.matchScore.compareTo(a.matchScore));
    
    return suggestions;
  }

  /// Calculate match score for a guard-shift combination
  Future<double> _calculateMatchScore({
    required UserModel guard,
    required EventShift shift,
    required EventModel event,
  }) async {
    double score = 0.0;
    
    // License compatibility (40% of score)
    score += _calculateLicenseScore(guard, shift) * 0.4;
    
    // Employment status compatibility (20% of score)
    score += _calculateEmploymentScore(guard, shift) * 0.2;
    
    // Experience/performance score (20% of score)
    score += await _calculateExperienceScore(guard) * 0.2;
    
    // Availability score (15% of score)
    score += await _calculateAvailabilityScore(guard, shift) * 0.15;
    
    // Workload balance (5% of score)
    score += await _calculateWorkloadScore(guard, shift) * 0.05;
    
    return score.clamp(0.0, 1.0);
  }

  /// Calculate license compatibility score
  double _calculateLicenseScore(UserModel guard, EventShift shift) {
    if (shift.requiredLicenses.isEmpty) return 1.0;
    
    double score = 0.0;
    int totalRequired = shift.requiredLicenses.length;
    int matched = 0;
    
    for (final requiredLicense in shift.requiredLicenses) {
      // Check Slovenian licenses
      if (requiredLicense == 'NPK_GOSTINSKI_LOKALI' && guard.hasNpkLicense) {
        matched++;
      } else if (requiredLicense == 'DELO_NA_OBJEKTU' && guard.hasDeloNaObjektuLicense) {
        matched++;
      } else if (guard.licenseIds.contains(requiredLicense)) {
        // Check legacy license IDs
        matched++;
      }
    }
    
    score = matched / totalRequired;
    
    // Bonus for having additional relevant licenses
    if (guard.hasAllSlovenianLicenses && totalRequired > 0) {
      score += 0.1; // 10% bonus for having all licenses
    }
    
    return score.clamp(0.0, 1.0);
  }

  /// Calculate employment status compatibility score
  double _calculateEmploymentScore(UserModel guard, EventShift shift) {
    if (guard.employmentStatus == null) return 0.5; // Neutral for unknown status
    
    final shiftDuration = shift.endTime.difference(shift.startTime).inHours;
    
    switch (guard.employmentStatus!) {
      case EmploymentStatus.full_time:
        // Full-time guards are ideal for any shift
        return 1.0;
        
      case EmploymentStatus.custom:
        if (guard.customWorkingHours == null) return 0.5;
        
        // Check if shift fits within their weekly hours capacity
        final weeklyCapacity = guard.customWorkingHours! / 7; // Daily average
        if (shiftDuration <= weeklyCapacity) {
          return 0.9; // High score if shift fits their capacity
        } else {
          return 0.3; // Lower score if shift exceeds their typical capacity
        }
        
      case EmploymentStatus.inactive:
        return 0.0; // Inactive guards shouldn't be suggested
    }
  }

  /// Calculate experience/performance score based on past assignments
  Future<double> _calculateExperienceScore(UserModel guard) async {
    try {
      // Get guard's past events
      final events = await _eventService.getEventsByGuard(guard.id);
      
      if (events.isEmpty) return 0.5; // Neutral for new guards
      
      double score = 0.5; // Base score
      
      // Experience bonus (more events = higher score)
      final experienceBonus = (events.length * 0.02).clamp(0.0, 0.3);
      score += experienceBonus;
      
      // Performance bonus based on completed vs cancelled assignments
      final completedEvents = events.where((e) => e.status == 'completed').length;
      if (events.isNotEmpty) {
        final completionRate = completedEvents / events.length;
        score += completionRate * 0.2; // Up to 20% bonus for good completion rate
      }
      
      return score.clamp(0.0, 1.0);
    } catch (e) {
      return 0.5; // Neutral score if we can't calculate experience
    }
  }

  /// Calculate availability score for the shift time
  Future<double> _calculateAvailabilityScore(UserModel guard, EventShift shift) async {
    // Check for schedule conflicts
    final conflicts = await _getScheduleConflicts(guard, shift);
    
    if (conflicts.isNotEmpty) {
      return 0.0; // No availability if there are conflicts
    }
    
    // Check day of week availability (if implemented)
    final dayOfWeek = shift.startTime.weekday;
    final dayName = _getDayName(dayOfWeek);
    
    if (guard.computedAvailabilityDays.isNotEmpty) {
      if (guard.computedAvailabilityDays.contains(dayName)) {
        return 1.0; // Perfect availability
      } else {
        return 0.2; // Low availability for non-preferred days
      }
    }
    
    return 0.8; // Good default availability
  }

  /// Calculate workload balance score
  Future<double> _calculateWorkloadScore(UserModel guard, EventShift shift) async {
    try {
      // Get guard's upcoming assignments in the same week
      final weekStart = _getWeekStart(shift.startTime);
      final weekEnd = weekStart.add(const Duration(days: 7));
      
      final upcomingEvents = await _eventService.getEventsByGuardInDateRange(
        guard.id,
        weekStart,
        weekEnd,
      );
      
      // Calculate total hours for the week
      double totalHours = 0.0;
      for (final event in upcomingEvents) {
        for (final eventShift in event.shifts) {
          for (final assignment in eventShift.assignments) {
            if (assignment.guardId == guard.id) {
              totalHours += assignment.scheduledHours;
            }
          }
        }
      }
      
      // Add this shift's hours
      final shiftHours = shift.endTime.difference(shift.startTime).inMinutes / 60.0;
      totalHours += shiftHours;
      
      // Score based on workload balance
      if (totalHours <= 20) return 1.0; // Light workload
      if (totalHours <= 35) return 0.8; // Moderate workload
      if (totalHours <= 45) return 0.5; // Heavy workload
      return 0.2; // Overloaded
      
    } catch (e) {
      return 0.8; // Default good score if calculation fails
    }
  }

  /// Check guard availability for a specific shift
  Future<AvailabilityStatus> _checkAvailability(UserModel guard, EventShift shift) async {
    if (!guard.isActive) return AvailabilityStatus.unavailable;
    
    final conflicts = await _getScheduleConflicts(guard, shift);
    if (conflicts.isNotEmpty) return AvailabilityStatus.conflict;
    
    // Check employment status
    if (guard.employmentStatus == EmploymentStatus.inactive) {
      return AvailabilityStatus.unavailable;
    }
    
    return AvailabilityStatus.available;
  }

  /// Get schedule conflicts for a guard during a shift time
  Future<List<ScheduleConflict>> _getScheduleConflicts(UserModel guard, EventShift shift) async {
    try {
      final conflicts = <ScheduleConflict>[];
      
      // Get overlapping events
      final overlappingEvents = await _eventService.getOverlappingEventsForGuard(
        guard.id,
        shift.startTime,
        shift.endTime,
      );
      
      for (final event in overlappingEvents) {
        for (final eventShift in event.shifts) {
          for (final assignment in eventShift.assignments) {
            if (assignment.guardId == guard.id) {
              // Check for time overlap
              if (_hasTimeOverlap(shift, assignment)) {
                conflicts.add(ScheduleConflict(
                  type: ConflictType.existingAssignment,
                  eventTitle: event.title,
                  conflictTime: assignment.startTime,
                  description: 'Already assigned to ${event.title}',
                ));
              }
            }
          }
        }
      }
      
      return conflicts;
    } catch (e) {
      return []; // Return empty list if we can't check conflicts
    }
  }

  /// Generate human-readable reasons for the suggestion
  List<String> _generateSuggestionReasons(
    UserModel guard,
    EventShift shift,
    EventModel event,
    double score,
  ) {
    final reasons = <String>[];
    
    // License reasons
    if (shift.requiredLicenses.isNotEmpty) {
      final hasAllRequired = shift.requiredLicenses.every((license) {
        return (license == 'NPK_GOSTINSKI_LOKALI' && guard.hasNpkLicense) ||
               (license == 'DELO_NA_OBJEKTU' && guard.hasDeloNaObjektuLicense) ||
               guard.licenseIds.contains(license);
      });
      
      if (hasAllRequired) {
        reasons.add('Has all required licenses');
      } else {
        reasons.add('Missing some required licenses');
      }
    }
    
    // Employment status reasons
    if (guard.employmentStatus == EmploymentStatus.full_time) {
      reasons.add('Full-time availability');
    } else if (guard.employmentStatus == EmploymentStatus.custom && guard.customWorkingHours != null) {
      reasons.add('Custom schedule (${guard.customWorkingHours}h/week)');
    }
    
    // Experience reasons
    if (score > 0.8) {
      reasons.add('Highly recommended');
    } else if (score > 0.6) {
      reasons.add('Good match');
    } else if (score > 0.4) {
      reasons.add('Suitable candidate');
    } else {
      reasons.add('Limited compatibility');
    }
    
    return reasons;
  }

  // Helper methods
  bool _hasTimeOverlap(EventShift shift, ShiftAssignment assignment) {
    return shift.startTime.isBefore(assignment.endTime) &&
           shift.endTime.isAfter(assignment.startTime);
  }

  String _getDayName(int weekday) {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[weekday - 1];
  }

  DateTime _getWeekStart(DateTime date) {
    final daysFromMonday = date.weekday - 1;
    return DateTime(date.year, date.month, date.day).subtract(Duration(days: daysFromMonday));
  }
}

/// Guard suggestion with match score and details
class GuardSuggestion {
  final UserModel guard;
  final double matchScore;
  final AvailabilityStatus availability;
  final List<ScheduleConflict> conflicts;
  final List<String> reasons;

  const GuardSuggestion({
    required this.guard,
    required this.matchScore,
    required this.availability,
    required this.conflicts,
    required this.reasons,
  });

  bool get isRecommended => matchScore >= 0.7 && availability == AvailabilityStatus.available;
  bool get isAvailable => availability == AvailabilityStatus.available;
  bool get hasConflicts => conflicts.isNotEmpty;
}

/// Availability status for guards
enum AvailabilityStatus {
  available,
  conflict,
  unavailable,
}

/// Schedule conflict information
class ScheduleConflict {
  final ConflictType type;
  final String eventTitle;
  final DateTime conflictTime;
  final String description;

  const ScheduleConflict({
    required this.type,
    required this.eventTitle,
    required this.conflictTime,
    required this.description,
  });
}

/// Types of schedule conflicts
enum ConflictType {
  existingAssignment,
  personalTime,
  unavailable,
}
