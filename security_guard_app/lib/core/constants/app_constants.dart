class AppConstants {
  // App Information
  static const String appName = 'Security Guard Management';
  static const String appVersion = '1.0.0';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String eventsCollection = 'events';
  static const String attendanceCollection = 'attendance';
  static const String companiesCollection = 'companies';
  static const String notificationsCollection = 'notifications';
  
  // User Roles
  static const String roleSuperAdmin = 'superadmin';
  static const String roleAdmin = 'admin';
  static const String roleGuard = 'guard';
  
  // Event Status
  static const String eventStatusPending = 'pending';
  static const String eventStatusConfirmed = 'confirmed';
  static const String eventStatusCompleted = 'completed';
  static const String eventStatusCancelled = 'cancelled';
  
  // Attendance Status
  static const String attendanceStatusPending = 'pending';
  static const String attendanceStatusAccepted = 'accepted';
  static const String attendanceStatusDeclined = 'declined';
  static const String attendanceStatusClockedIn = 'clocked_in';
  static const String attendanceStatusClockedOut = 'clocked_out';
  
  // Shared Preferences Keys
  static const String keyUserId = 'user_id';
  static const String keyUserRole = 'user_role';
  static const String keyCompanyId = 'company_id';
  static const String keyLanguage = 'language';
  static const String keyThemeMode = 'theme_mode';
  
  // API Timeouts
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Date Formats
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';
  
  // Validation
  static const int minPasswordLength = 6;
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 500;
}
