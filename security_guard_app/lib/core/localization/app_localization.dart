import 'package:flutter_localization/flutter_localization.dart';

class AppLocale {
  static const String title = 'title';
  static const String appTitle = 'appTitle';
  
  // Navigation
  static const String dashboard = 'dashboard';
  static const String events = 'events';
  static const String guards = 'guards';
  static const String companies = 'companies';
  static const String reports = 'reports';
  static const String settings = 'settings';
  static const String profile = 'profile';
  
  // User
  static const String adminPanel = 'adminPanel';
  static const String administrator = 'administrator';
  static const String signOut = 'signOut';
  static const String login = 'login';
  static const String logout = 'logout';
  static const String email = 'email';
  static const String password = 'password';
  static const String forgotPassword = 'forgotPassword';
  
  // Dashboard
  static const String welcomeBack = 'welcomeBack';
  static const String todaysOverview = 'todaysOverview';
  static const String todaysEvents = 'todaysEvents';
  static const String hoursWorked = 'hoursWorked';
  static const String pending = 'pending';
  static const String completed = 'completed';
  static const String quickActions = 'quickActions';
  static const String clockIn = 'clockIn';
  static const String clockOut = 'clockOut';
  
  // Events
  static const String eventsManagement = 'eventsManagement';
  static const String all = 'all';
  static const String active = 'active';
  static const String confirmed = 'confirmed';
  static const String cancelled = 'cancelled';
  static const String confirm = 'confirm';
  static const String complete = 'complete';
  static const String errorLoadingEvents = 'errorLoadingEvents';
  static const String noEventsFound = 'noEventsFound';
  static const String createFirstEvent = 'createFirstEvent';
  static const String noFilteredEvents = 'noFilteredEvents';
  static const String createEvent = 'createEvent';
  static const String createEventDialog = 'createEventDialog';
  static const String eventDetailsFor = 'eventDetailsFor';
  static const String location = 'location';
  static const String duration = 'duration';
  static const String guardsAssigned = 'guardsAssigned';
  
  // Common actions
  static const String accept = 'accept';
  static const String decline = 'decline';
  static const String save = 'save';
  static const String cancel = 'cancel';
  static const String delete = 'delete';
  static const String edit = 'edit';
  static const String add = 'add';
  static const String create = 'create';
  static const String update = 'update';
  static const String filter = 'filter';
  static const String loading = 'loading';
  static const String error = 'error';
  static const String success = 'success';
  static const String noData = 'noData';
  static const String retry = 'retry';
  
  // Event templates
  static const String importEvents = 'importEvents';
  static const String exportEvents = 'exportEvents';
  static const String eventTemplates = 'eventTemplates';
  static const String eventsOverview = 'eventsOverview';
  static const String totalEvents = 'totalEvents';

  // Event creation dialog
  static const String createEventTitle = 'createEventTitle';
  static const String editEventTitle = 'editEventTitle';
  static const String eventTitle = 'eventTitle';
  static const String eventDescription = 'eventDescription';
  static const String startDate = 'startDate';
  static const String startTime = 'startTime';
  static const String endDate = 'endDate';
  static const String endTime = 'endTime';
  static const String selectDate = 'selectDate';
  static const String selectTime = 'selectTime';
  static const String assignGuards = 'assignGuards';
  static const String selectGuards = 'selectGuards';
  static const String noGuardsSelected = 'noGuardsSelected';
  static const String guardsSelected = 'guardsSelected';
  static const String eventTitleRequired = 'eventTitleRequired';
  static const String eventDescriptionRequired = 'eventDescriptionRequired';
  static const String eventLocationRequired = 'eventLocationRequired';
  static const String startDateRequired = 'startDateRequired';
  static const String endDateRequired = 'endDateRequired';
  static const String endDateAfterStart = 'endDateAfterStart';
  static const String eventCreatedSuccess = 'eventCreatedSuccess';
  static const String eventUpdatedSuccess = 'eventUpdatedSuccess';
  static const String eventCreationFailed = 'eventCreationFailed';

  // Enhanced event creation
  static const String eventType = 'eventType';
  static const String venueSecurityType = 'venueSecurityType';
  static const String eventSecurityType = 'eventSecurityType';
  static const String localSecurityType = 'localSecurityType';
  static const String venueSecurityDesc = 'venueSecurityDesc';
  static const String eventSecurityDesc = 'eventSecurityDesc';
  static const String localSecurityDesc = 'localSecurityDesc';
  static const String shifts = 'shifts';
  static const String eventShifts = 'eventShifts';
  static const String addShift = 'addShift';
  static const String addFirstShift = 'addFirstShift';
  static const String noShiftsAdded = 'noShiftsAdded';
  static const String shift = 'shift';
  static const String editShift = 'editShift';
  static const String deleteShift = 'deleteShift';
  static const String shiftEditingComingSoon = 'shiftEditingComingSoon';
  static const String requiredGuards = 'requiredGuards';
  static const String licenses = 'licenses';
  static const String none = 'none';
  static const String notes = 'notes';
  static const String legacyGuardAssignment = 'legacyGuardAssignment';
  static const String legacyGuardAssignmentDesc = 'legacyGuardAssignmentDesc';
  static const String pleaseAddAtLeastOneShift = 'pleaseAddAtLeastOneShift';
  static const String invalidShiftTimes = 'invalidShiftTimes';
  static const String analytics = 'analytics';
  static const String guardAssignment = 'guardAssignment';
  static const String hours = 'hours';
  static const String suggestedGuards = 'suggestedGuards';
  static const String loadingSuggestions = 'loadingSuggestions';
  static const String noSuitableGuards = 'noSuitableGuards';
  static const String matchScore = 'matchScore';
  static const String suitableCandidate = 'suitableCandidate';
  static const String addedToEvent = 'addedToEvent';
  static const String guardsAssignedOf = 'guardsAssignedOf';
  static const String guardAssignmentsUpdated = 'guardAssignmentsUpdated';
  static const String failedToUpdateAssignments = 'failedToUpdateAssignments';
  static const String dragGuardsHere = 'dragGuardsHere';
  static const String guardDoesNotMeetRequirements = 'guardDoesNotMeetRequirements';
  static const String assign = 'assign';

  // User management
  static const String userManagement = 'userManagement';
  static const String users = 'users';
  static const String createUser = 'createUser';
  static const String editUser = 'editUser';
  static const String deleteUser = 'deleteUser';
  static const String firstName = 'firstName';
  static const String lastName = 'lastName';
  static const String phone = 'phone';
  static const String role = 'role';
  static const String company = 'company';
  static const String status = 'status';
  static const String inactive = 'inactive';
  static const String superAdmin = 'superAdmin';
  static const String admin = 'admin';
  static const String guard = 'guard';
  static const String selectRole = 'selectRole';
  static const String userCreatedSuccess = 'userCreatedSuccess';
  static const String userUpdatedSuccess = 'userUpdatedSuccess';
  static const String userDeletedSuccess = 'userDeletedSuccess';
  static const String userCreationFailed = 'userCreationFailed';
  static const String confirmDeleteUser = 'confirmDeleteUser';
  static const String deleteUserMessage = 'deleteUserMessage';
  static const String noUsersFound = 'noUsersFound';
  static const String searchUsers = 'searchUsers';
  static const String filterByRole = 'filterByRole';
  static const String allRoles = 'allRoles';
  static const String firstNameRequired = 'firstNameRequired';
  static const String lastNameRequired = 'lastNameRequired';
  static const String emailRequired = 'emailRequired';
  static const String phoneRequired = 'phoneRequired';
  static const String roleRequired = 'roleRequired';
  static const String invalidEmail = 'invalidEmail';
  static const String invalidPhone = 'invalidPhone';
  static const String userDetails = 'userDetails';
  static const String lastLogin = 'lastLogin';
  static const String createdDate = 'createdDate';
  static const String permissions = 'permissions';
  static const String canManageUsers = 'canManageUsers';
  static const String canCreateEvents = 'canCreateEvents';
  static const String canViewReports = 'canViewReports';

  // Additional strings
  static const String optional = 'optional';
  static const String endDateOptionalHint = 'endDateOptionalHint';

  // Reports and Analytics
  static const String reportsAnalytics = 'reportsAnalytics';
  static const String exportReport = 'exportReport';
  static const String reportFilters = 'reportFilters';
  static const String reportType = 'reportType';
  static const String overviewReport = 'overviewReport';
  static const String financialReport = 'financialReport';
  static const String attendanceReport = 'attendanceReport';
  static const String guardPerformance = 'guardPerformance';
  static const String dateRange = 'dateRange';
  static const String refresh = 'refresh';
  static const String completedEvents = 'completedEvents';
  static const String totalHours = 'totalHours';
  static const String quickStats = 'quickStats';
  static const String comingSoon = 'comingSoon';

  // Event Details
  static const String eventDetails = 'eventDetails';
  static const String eventNotFound = 'eventNotFound';
  static const String backToEvents = 'backToEvents';
  static const String editEvent = 'editEvent';
  static const String deleteEvent = 'deleteEvent';
  static const String deleteEventConfirmation = 'deleteEventConfirmation';
  static const String assignedGuards = 'assignedGuards';
  static const String noGuardsAssigned = 'noGuardsAssigned';
  static const String financial = 'financial';
  static const String availableGuards = 'availableGuards';
  static const String searchGuards = 'searchGuards';
  static const String fullTimeOnly = 'fullTimeOnly';
  static const String npkLicense = 'npkLicense';
  static const String objectLicense = 'objectLicense';
  static const String noGuardsMatchFilters = 'noGuardsMatchFilters';
  static const String callGuard = 'callGuard';
  static const String smsGuard = 'smsGuard';

  // Conflict detection
  static const String scheduleConflict = 'scheduleConflict';
  static const String conflictWarning = 'conflictWarning';
  static const String conflictDetails = 'conflictDetails';
  static const String assignAnyway = 'assignAnyway';
  static const String showAllGuards = 'showAllGuards';
  static const String showAvailableOnly = 'showAvailableOnly';

  // Event Editing
  static const String editTitle = 'editTitle';
  static const String editDescription = 'editDescription';
  static const String editLocation = 'editLocation';
  static const String editDateTime = 'editDateTime';
  static const String tapToEdit = 'tapToEdit';
  static const String saveChanges = 'saveChanges';
  static const String discardChanges = 'discardChanges';
  static const String eventUpdated = 'eventUpdated';
  static const String errorUpdatingEvent = 'errorUpdatingEvent';
  static const String totalCost = 'totalCost';
  static const String description = 'description';

  // User Details (additional keys)
  static const String userNotFound = 'userNotFound';
  static const String backToUsers = 'backToUsers';
  static const String deleteUserConfirmation = 'deleteUserConfirmation';
  static const String memberSince = 'memberSince';
  static const String accountStatus = 'accountStatus';
  static const String contactInformation = 'contactInformation';
  static const String address = 'address';
  static const String notProvided = 'notProvided';
  static const String workStatistics = 'workStatistics';
  static const String recentEvents = 'recentEvents';
  static const String noEventsAssigned = 'noEventsAssigned';
  static const String viewAllEvents = 'viewAllEvents';

  // Settings
  static const String appearance = 'appearance';
  static const String language = 'language';
  static const String account = 'account';
  static const String about = 'about';
  static const String themeMode = 'themeMode';
  static const String lightTheme = 'lightTheme';
  static const String darkTheme = 'darkTheme';
  static const String systemTheme = 'systemTheme';
  static const String lightThemeDesc = 'lightThemeDesc';
  static const String darkThemeDesc = 'darkThemeDesc';
  static const String systemThemeDesc = 'systemThemeDesc';
  static const String selectLanguage = 'selectLanguage';
  static const String accountSettings = 'accountSettings';
  static const String changePassword = 'changePassword';
  static const String privacy = 'privacy';
  static const String appInformation = 'appInformation';
  static const String version = 'version';
  static const String help = 'help';
  static const String support = 'support';
  static const String comingSoonDesc = 'comingSoonDesc';
  static const String ok = 'ok';

  // Slovenian-specific user profile fields
  static const String emso = 'emso';
  static const String emsoLabel = 'emsoLabel';
  static const String emsoHint = 'emsoHint';
  static const String emsoValidation = 'emsoValidation';
  static const String emsoInvalid = 'emsoInvalid';

  static const String slovenianLicenses = 'slovenianLicenses';
  static const String npkGostinskiLokali = 'npkGostinskiLokali';
  static const String npkGostinskiLokaliDesc = 'npkGostinskiLokaliDesc';
  static const String deloNaObjektu = 'deloNaObjektu';
  static const String deloNaObjektuDesc = 'deloNaObjektuDesc';
  static const String selectLicenses = 'selectLicenses';
  static const String noLicensesSelected = 'noLicensesSelected';

  static const String employmentStatusEnhanced = 'employmentStatusEnhanced';
  static const String fullTime40h = 'fullTime40h';
  static const String customHours = 'customHours';
  static const String customHoursLabel = 'customHoursLabel';
  static const String customHoursHint = 'customHoursHint';
  static const String customHoursValidation = 'customHoursValidation';
  static const String hoursPerWeek = 'hoursPerWeek';

  static const String licenseManagement = 'licenseManagement';
  static const String userProfile = 'userProfile';
  static const String personalInformation = 'personalInformation';
  static const String employmentInformation = 'employmentInformation';
  static const String licenseInformation = 'licenseInformation';

  // Guard App specific
  static const String guardApp = 'guardApp';
  static const String home = 'home';
  static const String invitations = 'invitations';
  static const String myShifts = 'myShifts';
  static const String welcome = 'welcome';
  static const String guardWelcomeMessage = 'guardWelcomeMessage';
  static const String upcomingShifts = 'upcomingShifts';
  static const String pendingInvitations = 'pendingInvitations';
  static const String viewAll = 'viewAll';
  static const String noUpcomingShifts = 'noUpcomingShifts';
  static const String noUpcomingShiftsMessage = 'noUpcomingShiftsMessage';
  static const String recentActivity = 'recentActivity';
  static const String noRecentActivity = 'noRecentActivity';
  static const String noRecentActivityMessage = 'noRecentActivityMessage';
  static const String shiftInvitations = 'shiftInvitations';
  static const String invitationsDescription = 'invitationsDescription';
  static const String noPendingInvitations = 'noPendingInvitations';
  static const String noPendingInvitationsMessage = 'noPendingInvitationsMessage';
  static const String recentResponses = 'recentResponses';
  static const String noRecentResponses = 'noRecentResponses';
  static const String noRecentResponsesMessage = 'noRecentResponsesMessage';
  static const String interested = 'interested';
  static const String unavailable = 'unavailable';
  static const String securityGuard = 'securityGuard';
  static const String employmentStatus = 'employmentStatus';
  static const String slovenian = 'slovenian';
  static const String notifications = 'notifications';
  static const String manageNotifications = 'manageNotifications';
  static const String logoutConfirmation = 'logoutConfirmation';

  // Additional guard app keys (only new ones)
  static const String declined = 'declined';

  static const List<MapLocale> locales = [
    MapLocale(
      'en',
      {
        title: 'Security Guard Management',
        appTitle: 'Security Guard Management',
        
        // Navigation
        dashboard: 'Dashboard',
        events: 'Events',
        guards: 'Guards',
        companies: 'Companies',
        reports: 'Reports',
        settings: 'Settings',
        profile: 'Profile',
        
        // User
        adminPanel: 'Admin Panel',
        administrator: 'Administrator',
        signOut: 'Sign Out',
        login: 'Login',
        logout: 'Logout',
        email: 'Email',
        password: 'Password',
        forgotPassword: 'Forgot Password?',
        
        // Dashboard
        welcomeBack: 'Welcome Back!',
        todaysOverview: 'Here\'s your overview for today',
        todaysEvents: 'Today\'s Events',
        hoursWorked: 'Hours Worked',
        pending: 'Pending',
        completed: 'Completed',
        quickActions: 'Quick Actions',
        clockIn: 'Clock In',
        clockOut: 'Clock Out',
        
        // Events
        eventsManagement: 'Events Management',
        all: 'All',
        active: 'Active',
        confirmed: 'Confirmed',
        cancelled: 'Cancelled',
        confirm: 'Confirm',
        complete: 'Complete',
        errorLoadingEvents: 'Error loading events',
        noEventsFound: 'No events found',
        createFirstEvent: 'Create your first event to get started',
        noFilteredEvents: 'No {status} events found',
        createEvent: 'Create Event',
        createEventDialog: 'Create event dialog coming soon...',
        eventDetailsFor: 'Event details for: {title}',
        location: 'Location',
        duration: 'Duration',
        guardsAssigned: '{count} guard(s) assigned',
        
        // Common actions
        accept: 'Accept',
        decline: 'Decline',
        save: 'Save',
        cancel: 'Cancel',
        delete: 'Delete',
        edit: 'Edit',
        add: 'Add',
        create: 'Create',
        update: 'Update',
        filter: 'Filter',
        loading: 'Loading...',
        error: 'Error',
        success: 'Success',
        noData: 'No Data',
        retry: 'Retry',
        
        // Event templates
        importEvents: 'Import Events',
        exportEvents: 'Export Events',
        eventTemplates: 'Event Templates',
        eventsOverview: 'Events Overview',
        totalEvents: 'Total Events',

        // Event creation dialog
        createEventTitle: 'Create New Event',
        editEventTitle: 'Edit Event',
        eventTitle: 'Event Title',
        eventDescription: 'Description',
        startDate: 'Start Date',
        startTime: 'Start Time',
        endDate: 'End Date',
        endTime: 'End Time',
        selectDate: 'Select Date',
        selectTime: 'Select Time',
        assignGuards: 'Assign Guards',
        selectGuards: 'Select Guards',
        noGuardsSelected: 'No guards selected',
        guardsSelected: '{count} guard(s) selected',
        eventTitleRequired: 'Event title is required',
        eventDescriptionRequired: 'Description is required',
        eventLocationRequired: 'Location is required',
        startDateRequired: 'Start date is required',
        endDateRequired: 'End date is required',
        endDateAfterStart: 'End date must be after start date',
        eventCreatedSuccess: 'Event created successfully',
        eventUpdatedSuccess: 'Event updated successfully',
        eventCreationFailed: 'Failed to create event',

        // Enhanced event creation
        eventType: 'Event Type',
        venueSecurityType: 'Venue Security',
        eventSecurityType: 'Event Security',
        localSecurityType: 'Local Security',
        venueSecurityDesc: 'Security for venues, clubs, bars',
        eventSecurityDesc: 'Security for events, concerts, festivals',
        localSecurityDesc: 'Local security services',
        shifts: 'Shifts',
        eventShifts: 'Event Shifts',
        addShift: 'Add Shift',
        addFirstShift: 'Add First Shift',
        noShiftsAdded: 'No shifts added yet',
        shift: 'Shift',
        editShift: 'Edit Shift',
        deleteShift: 'Delete Shift',
        shiftEditingComingSoon: 'Shift editing coming soon!',
        requiredGuards: 'Required Guards',
        licenses: 'Licenses',
        none: 'None',
        notes: 'Notes',
        legacyGuardAssignment: 'Legacy Guard Assignment',
        legacyGuardAssignmentDesc: 'This is the legacy guard assignment system. Use the Shifts tab for the new shift-based assignment.',
        pleaseAddAtLeastOneShift: 'Please add at least one shift',
        invalidShiftTimes: 'Invalid shift times',
        analytics: 'Analytics',
        guardAssignment: 'Guard Assignment',
        hours: 'hours',
        suggestedGuards: 'Suggested Guards',
        loadingSuggestions: 'Loading suggestions...',
        noSuitableGuards: 'No suitable guards found for this shift',
        matchScore: 'match',
        suitableCandidate: 'Suitable candidate',
        addedToEvent: '{name} added to event',
        guardsAssignedOf: '{assigned} of {required} guards assigned',
        guardAssignmentsUpdated: 'Guard assignments updated successfully',
        failedToUpdateAssignments: 'Failed to update assignments',
        dragGuardsHere: 'Drag guards here to assign',
        guardDoesNotMeetRequirements: 'Guard does not meet license requirements for this shift',
        assign: 'Assign',

        // User management
        userManagement: 'User Management',
        users: 'Users',
        createUser: 'Create User',
        editUser: 'Edit User',
        deleteUser: 'Delete User',
        firstName: 'First Name',
        lastName: 'Last Name',
        phone: 'Phone',
        role: 'Role',
        company: 'Company',
        status: 'Status',
        inactive: 'Inactive',
        superAdmin: 'Super Admin',
        admin: 'Admin',
        guard: 'Guard',
        selectRole: 'Select Role',
        userCreatedSuccess: 'User created successfully',
        userUpdatedSuccess: 'User updated successfully',
        userDeletedSuccess: 'User deleted successfully',
        userCreationFailed: 'Failed to create user',
        confirmDeleteUser: 'Confirm Delete',
        deleteUserMessage: 'Are you sure you want to delete this user? This action cannot be undone.',
        noUsersFound: 'No users found',
        searchUsers: 'Search users...',
        filterByRole: 'Filter by role',
        allRoles: 'All Roles',
        firstNameRequired: 'First name is required',
        lastNameRequired: 'Last name is required',
        emailRequired: 'Email is required',
        phoneRequired: 'Phone number is required',
        roleRequired: 'Role is required',
        invalidEmail: 'Please enter a valid email address',
        invalidPhone: 'Please enter a valid phone number',
        userDetails: 'User Details',
        lastLogin: 'Last Login',
        createdDate: 'Created Date',
        permissions: 'Permissions',
        canManageUsers: 'Can manage users',
        canCreateEvents: 'Can create events',
        canViewReports: 'Can view reports',

        // Additional strings
        optional: 'optional',
        endDateOptionalHint: 'If not specified, defaults to same day 1 hour after start time',

        // Reports and Analytics
        reportsAnalytics: 'Reports & Analytics',
        exportReport: 'Export Report',
        reportFilters: 'Report Filters',
        reportType: 'Report Type',
        overviewReport: 'Overview Report',
        financialReport: 'Financial Report',
        attendanceReport: 'Attendance Report',
        guardPerformance: 'Guard Performance',
        dateRange: 'Date Range',
        refresh: 'Refresh',
        completedEvents: 'Completed Events',
        totalHours: 'Total Hours',
        quickStats: 'Quick Statistics',
        comingSoon: 'Coming Soon',

        // Event Details
        eventDetails: 'Event Details',
        eventNotFound: 'Event not found',
        backToEvents: 'Back to Events',
        editEvent: 'Edit Event',
        deleteEvent: 'Delete Event',
        deleteEventConfirmation: 'Are you sure you want to delete this event? This action cannot be undone.',
        assignedGuards: 'Assigned Guards',
        noGuardsAssigned: 'No guards assigned to this event',
        financial: 'Financial Information',
        availableGuards: 'Available Guards',
        searchGuards: 'Search guards...',
        fullTimeOnly: 'Full-time only',
        npkLicense: 'NPK License',
        objectLicense: 'Object License',
        noGuardsMatchFilters: 'No guards match the current filters',
        callGuard: 'Call Guard',
        smsGuard: 'Send SMS',

        // Conflict detection
        scheduleConflict: 'Schedule Conflict',
        conflictWarning: '{guardName} has a schedule conflict for this time',
        conflictDetails: 'Conflict Details',
        assignAnyway: 'Assign Anyway',
        showAllGuards: 'Show All Guards',
        showAvailableOnly: 'Show Available Only',

        // Event Editing
        editTitle: 'Edit Title',
        editDescription: 'Edit Description',
        editLocation: 'Edit Location',
        editDateTime: 'Edit Date & Time',
        tapToEdit: 'Tap to edit',
        saveChanges: 'Save Changes',
        discardChanges: 'Discard Changes',
        eventUpdated: 'Event updated successfully',
        errorUpdatingEvent: 'Error updating event',
        totalCost: 'Total Cost',
        description: 'Description',

        // User Details (additional keys)
        userNotFound: 'User not found',
        backToUsers: 'Back to Users',
        deleteUserConfirmation: 'Are you sure you want to delete this user? This action cannot be undone.',
        memberSince: 'Member Since',
        accountStatus: 'Account Status',
        contactInformation: 'Contact Information',
        address: 'Address',
        notProvided: 'Not provided',
        workStatistics: 'Work Statistics',
        recentEvents: 'Recent Events',
        noEventsAssigned: 'No events assigned to this user',
        viewAllEvents: 'View All Events',

        // Settings
        appearance: 'Appearance',
        language: 'Language',
        account: 'Account',
        about: 'About',
        themeMode: 'Theme Mode',
        lightTheme: 'Light Theme',
        darkTheme: 'Dark Theme',
        systemTheme: 'System Theme',
        lightThemeDesc: 'Use light colors and themes',
        darkThemeDesc: 'Use dark colors and themes',
        systemThemeDesc: 'Follow system theme settings',
        selectLanguage: 'Select Language',
        accountSettings: 'Account Settings',
        changePassword: 'Change Password',
        privacy: 'Privacy Settings',
        appInformation: 'App Information',
        version: 'Version',
        help: 'Help & Support',
        support: 'Contact Support',
        comingSoonDesc: 'feature is coming soon!',
        ok: 'OK',

        // Slovenian-specific user profile fields
        emso: 'EMŠO',
        emsoLabel: 'EMŠO (Unique Master Citizen Number)',
        emsoHint: 'Enter 13-digit EMŠO number',
        emsoValidation: 'EMŠO must be exactly 13 digits',
        emsoInvalid: 'Invalid EMŠO format',

        slovenianLicenses: 'Slovenian Licenses',
        npkGostinskiLokali: 'NPK GOSTINSKI LOKALI',
        npkGostinskiLokaliDesc: 'Hospitality Venues License',
        deloNaObjektu: 'DELO NA OBJEKTU',
        deloNaObjektuDesc: 'On-Site Work License',
        selectLicenses: 'Select applicable licenses',
        noLicensesSelected: 'No licenses selected',

        employmentStatusEnhanced: 'Employment Status',
        fullTime40h: 'Full Time (40h/week)',
        customHours: 'Custom Hours',
        customHoursLabel: 'Custom working hours per week',
        customHoursHint: 'Enter hours per week (e.g., 20, 30)',
        customHoursValidation: 'Hours must be between 1 and 60',
        hoursPerWeek: 'h/week',

        licenseManagement: 'License Management',
        userProfile: 'User Profile',
        personalInformation: 'Personal Information',
        employmentInformation: 'Employment Information',
        licenseInformation: 'License Information',

        // Guard App specific
        guardApp: 'Guard App',
        home: 'Home',
        invitations: 'Invitations',
        myShifts: 'My Shifts',
        welcome: 'Welcome',
        guardWelcomeMessage: 'Here you can manage your shifts and invitations',
        upcomingShifts: 'Upcoming Shifts',
        pendingInvitations: 'Pending Invitations',
        viewAll: 'View All',
        noUpcomingShifts: 'No Upcoming Shifts',
        noUpcomingShiftsMessage: 'You currently have no upcoming shifts assigned',
        recentActivity: 'Recent Activity',
        noRecentActivity: 'No Recent Activity',
        noRecentActivityMessage: 'No recent activities to display',
        shiftInvitations: 'Shift Invitations',
        invitationsDescription: 'Respond to invitations for new shifts',
        noPendingInvitations: 'No Pending Invitations',
        noPendingInvitationsMessage: 'You currently have no pending shift invitations',
        recentResponses: 'Recent Responses',
        noRecentResponses: 'No Recent Responses',
        noRecentResponsesMessage: 'No recent responses to invitations',
        interested: 'Interested',
        unavailable: 'Unavailable',
        securityGuard: 'Security Guard',
        employmentStatus: 'Employment Status',
        slovenian: 'Slovenian',
        notifications: 'Notifications',
        manageNotifications: 'Manage Notifications',
        logoutConfirmation: 'Are you sure you want to logout?',

        // Additional guard app keys (only new ones)
        declined: 'Declined',
      },
    ),
    MapLocale(
      'sl',
      {
        title: 'Upravljanje Varnostnikov',
        appTitle: 'Upravljanje Varnostnikov',
        
        // Navigation
        dashboard: 'Nadzorna plošča',
        events: 'Dogodki',
        guards: 'Varnostniki',
        companies: 'Podjetja',
        reports: 'Poročila',
        settings: 'Nastavitve',
        profile: 'Profil',
        
        // User
        adminPanel: 'Skrbniška plošča',
        administrator: 'Administrator',
        signOut: 'Odjavi se',
        login: 'Prijava',
        logout: 'Odjava',
        email: 'E-pošta',
        password: 'Geslo',
        forgotPassword: 'Pozabljeno geslo?',
        
        // Dashboard
        welcomeBack: 'Dobrodošli nazaj!',
        todaysOverview: 'Tukaj je vaš pregled za danes',
        todaysEvents: 'Današnji dogodki',
        hoursWorked: 'Opravljene ure',
        pending: 'V obravnavi',
        completed: 'Končano',
        quickActions: 'Hitre akcije',
        clockIn: 'Prijava na delo',
        clockOut: 'Odjava z dela',
        
        // Events
        eventsManagement: 'Upravljanje dogodkov',
        all: 'Vse',
        active: 'Aktivno',
        confirmed: 'Potrjeno',
        cancelled: 'Preklicano',
        confirm: 'Potrdi',
        complete: 'Zaključi',
        errorLoadingEvents: 'Napaka pri nalaganju dogodkov',
        noEventsFound: 'Ni najdenih dogodkov',
        createFirstEvent: 'Ustvarite svoj prvi dogodek za začetek',
        noFilteredEvents: 'Ni najdenih {status} dogodkov',
        createEvent: 'Ustvari dogodek',
        createEventDialog: 'Dialog za ustvarjanje dogodka kmalu na voljo...',
        eventDetailsFor: 'Podrobnosti dogodka za: {title}',
        location: 'Lokacija',
        duration: 'Trajanje',
        guardsAssigned: '{count} varnostnik(ov)',
        
        // Common actions
        accept: 'Sprejmi',
        decline: 'Zavrni',
        save: 'Shrani',
        cancel: 'Prekliči',
        delete: 'Izbriši',
        edit: 'Uredi',
        add: 'Dodaj',
        create: 'Ustvari',
        update: 'Posodobi',
        filter: 'Filter',
        loading: 'Nalaganje...',
        error: 'Napaka',
        success: 'Uspeh',
        noData: 'Ni podatkov',
        retry: 'Poskusi znova',
        
        // Event templates
        importEvents: 'Uvozi dogodke',
        exportEvents: 'Izvozi dogodke',
        eventTemplates: 'Predloge dogodkov',
        eventsOverview: 'Pregled dogodkov',
        totalEvents: 'Skupaj dogodkov',

        // Event creation dialog
        createEventTitle: 'Ustvari nov dogodek',
        editEventTitle: 'Uredi dogodek',
        eventTitle: 'Naslov dogodka',
        eventDescription: 'Opis',
        startDate: 'Datum začetka',
        startTime: 'Čas začetka',
        endDate: 'Datum konca',
        endTime: 'Čas konca',
        selectDate: 'Izberi datum',
        selectTime: 'Izberi čas',
        assignGuards: 'Dodeli varnostnike',
        selectGuards: 'Izberi varnostnike',
        noGuardsSelected: 'Ni izbranih varnostnikov',
        guardsSelected: '{count} varnostnik(ov) izbranih',
        eventTitleRequired: 'Naslov dogodka je obvezen',
        eventDescriptionRequired: 'Opis je obvezen',
        eventLocationRequired: 'Lokacija je obvezna',
        startDateRequired: 'Datum začetka je obvezen',
        endDateRequired: 'Datum konca je obvezen',
        endDateAfterStart: 'Datum konca mora biti po datumu začetka',
        eventCreatedSuccess: 'Dogodek je bil uspešno ustvarjen',
        eventUpdatedSuccess: 'Dogodek je bil uspešno posodobljen',
        eventCreationFailed: 'Ustvarjanje dogodka ni uspelo',

        // Enhanced event creation
        eventType: 'Vrsta dogodka',
        venueSecurityType: 'Varovanje lokala',
        eventSecurityType: 'Varovanje dogodka',
        localSecurityType: 'Lokalno varovanje',
        venueSecurityDesc: 'Varovanje lokalov, klubov, barov',
        eventSecurityDesc: 'Varovanje dogodkov, koncertov, festivalov',
        localSecurityDesc: 'Lokalne varnostne storitve',
        shifts: 'Izmene',
        eventShifts: 'Izmene dogodka',
        addShift: 'Dodaj izmeno',
        addFirstShift: 'Dodaj prvo izmeno',
        noShiftsAdded: 'Še ni dodanih izmen',
        shift: 'Izmena',
        editShift: 'Uredi izmeno',
        deleteShift: 'Izbriši izmeno',
        shiftEditingComingSoon: 'Urejanje izmen kmalu na voljo!',
        requiredGuards: 'Potrebni varnostniki',
        licenses: 'Licence',
        none: 'Brez',
        notes: 'Opombe',
        legacyGuardAssignment: 'Staro dodeljevanje varnostnikov',
        legacyGuardAssignmentDesc: 'To je stari sistem dodeljevanja varnostnikov. Uporabite zavihek Izmene za novi sistem na osnovi izmen.',
        pleaseAddAtLeastOneShift: 'Prosimo, dodajte vsaj eno izmeno',
        invalidShiftTimes: 'Neveljavni časi izmen',
        analytics: 'Analitika',
        guardAssignment: 'Dodeljevanje varnostnikov',
        hours: 'ur',
        suggestedGuards: 'Predlagani varnostniki',
        loadingSuggestions: 'Nalaganje predlogov...',
        noSuitableGuards: 'Za to izmeno ni najdenih primernih varnostnikov',
        matchScore: 'ujemanje',
        suitableCandidate: 'Primeren kandidat',
        addedToEvent: '{name} dodan k dogodku',
        guardsAssignedOf: '{assigned} od {required} varnostnikov dodeljenih',
        guardAssignmentsUpdated: 'Dodelitve varnostnikov uspešno posodobljene',
        failedToUpdateAssignments: 'Posodabljanje dodelitev ni uspelo',
        dragGuardsHere: 'Povlecite varnostnike sem za dodelitev',
        guardDoesNotMeetRequirements: 'Varnostnik ne izpolnjuje licenčnih zahtev za to izmeno',
        assign: 'Dodeli',

        // User management
        userManagement: 'Upravljanje uporabnikov',
        users: 'Uporabniki',
        createUser: 'Ustvari uporabnika',
        editUser: 'Uredi uporabnika',
        deleteUser: 'Izbriši uporabnika',
        firstName: 'Ime',
        lastName: 'Priimek',
        phone: 'Telefon',
        role: 'Vloga',
        company: 'Podjetje',
        status: 'Status',
        inactive: 'Neaktiven',
        superAdmin: 'Super administrator',
        admin: 'Administrator',
        guard: 'Varnostnik',
        selectRole: 'Izberi vlogo',
        userCreatedSuccess: 'Uporabnik je bil uspešno ustvarjen',
        userUpdatedSuccess: 'Uporabnik je bil uspešno posodobljen',
        userDeletedSuccess: 'Uporabnik je bil uspešno izbrisan',
        userCreationFailed: 'Ustvarjanje uporabnika ni uspelo',
        confirmDeleteUser: 'Potrdi brisanje',
        deleteUserMessage: 'Ali ste prepričani, da želite izbrisati tega uporabnika? Tega dejanja ni mogoče razveljaviti.',
        noUsersFound: 'Ni najdenih uporabnikov',
        searchUsers: 'Išči uporabnike...',
        filterByRole: 'Filtriraj po vlogi',
        allRoles: 'Vse vloge',
        firstNameRequired: 'Ime je obvezno',
        lastNameRequired: 'Priimek je obvezen',
        emailRequired: 'E-pošta je obvezna',
        phoneRequired: 'Telefonska številka je obvezna',
        roleRequired: 'Vloga je obvezna',
        invalidEmail: 'Vnesite veljaven e-poštni naslov',
        invalidPhone: 'Vnesite veljavno telefonsko številko',
        userDetails: 'Podrobnosti uporabnika',
        lastLogin: 'Zadnja prijava',
        createdDate: 'Datum ustvarjanja',
        permissions: 'Dovoljenja',
        canManageUsers: 'Lahko upravlja uporabnike',
        canCreateEvents: 'Lahko ustvarja dogodke',
        canViewReports: 'Lahko pregleduje poročila',

        // Additional strings
        optional: 'neobvezno',
        endDateOptionalHint: 'Če ni določeno, se privzeto nastavi na isti dan 1 uro po času začetka',

        // Reports and Analytics
        reportsAnalytics: 'Poročila in analitika',
        exportReport: 'Izvozi poročilo',
        reportFilters: 'Filtri poročil',
        reportType: 'Vrsta poročila',
        overviewReport: 'Pregled poročila',
        financialReport: 'Finančno poročilo',
        attendanceReport: 'Poročilo o prisotnosti',
        guardPerformance: 'Uspešnost varnostnikov',
        dateRange: 'Časovno obdobje',
        refresh: 'Osveži',
        completedEvents: 'Končani dogodki',
        totalHours: 'Skupaj ur',
        quickStats: 'Hitre statistike',
        comingSoon: 'Kmalu na voljo',

        // Event Details
        eventDetails: 'Podrobnosti dogodka',
        eventNotFound: 'Dogodek ni najden',
        backToEvents: 'Nazaj na dogodke',
        editEvent: 'Uredi dogodek',
        deleteEvent: 'Izbriši dogodek',
        deleteEventConfirmation: 'Ali ste prepričani, da želite izbrisati ta dogodek? Tega dejanja ni mogoče razveljaviti.',
        assignedGuards: 'Dodeljeni varnostniki',
        noGuardsAssigned: 'Temu dogodku ni dodeljen noben varnostnik',
        financial: 'Finančne informacije',
        availableGuards: 'Razpoložljivi varnostniki',
        searchGuards: 'Išči varnostnike...',
        fullTimeOnly: 'Samo polni delovni čas',
        npkLicense: 'NPK licenca',
        objectLicense: 'Licenca za objekt',
        noGuardsMatchFilters: 'Noben varnostnik ne ustreza trenutnim filtrom',
        callGuard: 'Pokliči varnostnika',
        smsGuard: 'Pošlji SMS',

        // Conflict detection
        scheduleConflict: 'Konflikt urnika',
        conflictWarning: '{guardName} ima konflikt urnika za ta čas',
        conflictDetails: 'Podrobnosti konflikta',
        assignAnyway: 'Vseeno dodeli',
        showAllGuards: 'Prikaži vse varnostnike',
        showAvailableOnly: 'Prikaži samo razpoložljive',

        // Event Editing
        editTitle: 'Uredi naslov',
        editDescription: 'Uredi opis',
        editLocation: 'Uredi lokacijo',
        editDateTime: 'Uredi datum in čas',
        tapToEdit: 'Tapnite za urejanje',
        saveChanges: 'Shrani spremembe',
        discardChanges: 'Zavrzi spremembe',
        eventUpdated: 'Dogodek je bil uspešno posodobljen',
        errorUpdatingEvent: 'Napaka pri posodabljanju dogodka',
        totalCost: 'Skupni strošek',
        description: 'Opis',

        // User Details (additional keys)
        userNotFound: 'Uporabnik ni najden',
        backToUsers: 'Nazaj na uporabnike',
        deleteUserConfirmation: 'Ali ste prepričani, da želite izbrisati tega uporabnika? Tega dejanja ni mogoče razveljaviti.',
        memberSince: 'Član od',
        accountStatus: 'Status računa',
        contactInformation: 'Kontaktni podatki',
        address: 'Naslov',
        notProvided: 'Ni podano',
        workStatistics: 'Statistike dela',
        recentEvents: 'Nedavni dogodki',
        noEventsAssigned: 'Temu uporabniku ni dodeljen noben dogodek',
        viewAllEvents: 'Prikaži vse dogodke',

        // Settings
        appearance: 'Videz',
        language: 'Jezik',
        account: 'Račun',
        about: 'O aplikaciji',
        themeMode: 'Način teme',
        lightTheme: 'Svetla tema',
        darkTheme: 'Temna tema',
        systemTheme: 'Sistemska tema',
        lightThemeDesc: 'Uporabi svetle barve in teme',
        darkThemeDesc: 'Uporabi temne barve in teme',
        systemThemeDesc: 'Sledi nastavitvam sistemske teme',
        selectLanguage: 'Izberi jezik',
        accountSettings: 'Nastavitve računa',
        changePassword: 'Spremeni geslo',
        privacy: 'Nastavitve zasebnosti',
        appInformation: 'Informacije o aplikaciji',
        version: 'Različica',
        help: 'Pomoč in podpora',
        support: 'Kontaktiraj podporo',
        comingSoonDesc: 'funkcija kmalu na voljo!',
        ok: 'V redu',

        // Slovenian-specific user profile fields
        emso: 'EMŠO',
        emsoLabel: 'EMŠO (Enotna matična številka občana)',
        emsoHint: 'Vnesite 13-mestno EMŠO številko',
        emsoValidation: 'EMŠO mora imeti natanko 13 številk',
        emsoInvalid: 'Neveljavna oblika EMŠO',

        slovenianLicenses: 'Slovenske licence',
        npkGostinskiLokali: 'NPK GOSTINSKI LOKALI',
        npkGostinskiLokaliDesc: 'Licenca za gostinske lokale',
        deloNaObjektu: 'DELO NA OBJEKTU',
        deloNaObjektuDesc: 'Licenca za delo na objektu',
        selectLicenses: 'Izberite ustrezne licence',
        noLicensesSelected: 'Ni izbranih licenc',

        employmentStatusEnhanced: 'Status zaposlitve',
        fullTime40h: 'Polni delovni čas (40h/teden)',
        customHours: 'Prilagojene ure',
        customHoursLabel: 'Prilagojene delovne ure na teden',
        customHoursHint: 'Vnesite ure na teden (npr. 20, 30)',
        customHoursValidation: 'Ure morajo biti med 1 in 60',
        hoursPerWeek: 'h/teden',

        licenseManagement: 'Upravljanje licenc',
        userProfile: 'Uporabniški profil',
        personalInformation: 'Osebni podatki',
        employmentInformation: 'Podatki o zaposlitvi',
        licenseInformation: 'Podatki o licencah',

        // Guard App specific
        guardApp: 'Varnostniška aplikacija',
        home: 'Domov',
        invitations: 'Povabila',
        myShifts: 'Moje izmene',
        welcome: 'Dobrodošli',
        guardWelcomeMessage: 'Tukaj lahko upravljate svoje izmene in povabila',
        upcomingShifts: 'Prihajajoče izmene',
        pendingInvitations: 'Čakajoča povabila',
        viewAll: 'Prikaži vse',
        noUpcomingShifts: 'Ni prihajajočih izmen',
        noUpcomingShiftsMessage: 'Trenutno nimate dodeljenih prihajajočih izmen',
        recentActivity: 'Nedavna aktivnost',
        noRecentActivity: 'Ni nedavne aktivnosti',
        noRecentActivityMessage: 'Ni nedavnih aktivnosti za prikaz',
        shiftInvitations: 'Povabila za izmene',
        invitationsDescription: 'Odgovorite na povabila za nove izmene',
        noPendingInvitations: 'Ni čakajočih povabil',
        noPendingInvitationsMessage: 'Trenutno nimate čakajočih povabil za izmene',
        recentResponses: 'Nedavni odgovori',
        noRecentResponses: 'Ni nedavnih odgovorov',
        noRecentResponsesMessage: 'Ni nedavnih odgovorov na povabila',
        interested: 'Zainteresiran',
        unavailable: 'Nedostopen',
        securityGuard: 'Varnostnik',
        employmentStatus: 'Status zaposlitve',
        slovenian: 'Slovenščina',
        notifications: 'Obvestila',
        manageNotifications: 'Upravljaj obvestila',
        logoutConfirmation: 'Ali se res želite odjaviti?',

        // Additional guard app keys (only new ones)
        declined: 'Zavrnjeno',
      },
    ),
  ];
}
