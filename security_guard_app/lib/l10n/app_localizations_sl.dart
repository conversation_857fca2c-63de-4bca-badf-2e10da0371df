// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Slovenian (`sl`).
class AppLocalizationsSl extends AppLocalizations {
  AppLocalizationsSl([String locale = 'sl']) : super(locale);

  @override
  String get appTitle => 'Upravljanje Varnostnikov';

  @override
  String get login => 'Prijava';

  @override
  String get logout => 'Odjava';

  @override
  String get signOut => 'Odjavi se';

  @override
  String get email => 'E-pošta';

  @override
  String get password => 'Geslo';

  @override
  String get forgotPassword => 'Pozabljeno geslo?';

  @override
  String get dashboard => 'Nadzorna plošča';

  @override
  String get events => 'Dogodki';

  @override
  String get eventsManagement => 'Upravljanje dogodkov';

  @override
  String get reports => 'Poročila';

  @override
  String get profile => 'Profil';

  @override
  String get admin => 'Skrbnik';

  @override
  String get guards => 'Varnostniki';

  @override
  String get companies => 'Podjetja';

  @override
  String get settings => 'Nastavitve';

  @override
  String get clockIn => 'Prijava na delo';

  @override
  String get clockOut => 'Odjava z dela';

  @override
  String get accept => 'Sprejmi';

  @override
  String get decline => 'Zavrni';

  @override
  String get pending => 'V obravnavi';

  @override
  String get active => 'Aktivno';

  @override
  String get confirmed => 'Potrjeno';

  @override
  String get completed => 'Končano';

  @override
  String get cancelled => 'Preklicano';

  @override
  String get save => 'Shrani';

  @override
  String get cancel => 'Prekliči';

  @override
  String get delete => 'Izbriši';

  @override
  String get edit => 'Uredi';

  @override
  String get add => 'Dodaj';

  @override
  String get create => 'Ustvari';

  @override
  String get update => 'Posodobi';

  @override
  String get filter => 'Filter';

  @override
  String get all => 'Vse';

  @override
  String get loading => 'Nalaganje...';

  @override
  String get error => 'Napaka';

  @override
  String get success => 'Uspeh';

  @override
  String get noData => 'Ni podatkov';

  @override
  String get retry => 'Poskusi znova';

  @override
  String get welcomeBack => 'Dobrodošli nazaj!';

  @override
  String get todaysOverview => 'Tukaj je vaš pregled za danes';

  @override
  String get quickActions => 'Hitre akcije';

  @override
  String get eventsOverview => 'Pregled dogodkov';

  @override
  String get totalEvents => 'Skupaj dogodkov';

  @override
  String get todaysEvents => 'Današnji dogodki';

  @override
  String get hoursWorked => 'Opravljene ure';

  @override
  String get administrator => 'Administrator';

  @override
  String get adminPanel => 'Skrbniška plošča';

  @override
  String get createEvent => 'Ustvari dogodek';

  @override
  String get importEvents => 'Uvozi dogodke';

  @override
  String get exportEvents => 'Izvozi dogodke';

  @override
  String get eventTemplates => 'Predloge dogodkov';

  @override
  String get noEventsFound => 'Ni najdenih dogodkov';

  @override
  String get createFirstEvent => 'Ustvarite svoj prvi dogodek za začetek';

  @override
  String noFilteredEvents(String status) {
    return 'Ni najdenih $status dogodkov';
  }

  @override
  String get errorLoadingEvents => 'Napaka pri nalaganju dogodkov';

  @override
  String get createEventDialog =>
      'Dialog za ustvarjanje dogodka kmalu na voljo...';

  @override
  String eventDetailsFor(String title) {
    return 'Podrobnosti dogodka za: $title';
  }

  @override
  String get location => 'Lokacija';

  @override
  String get duration => 'Trajanje';

  @override
  String get hourlyRate => 'Urna postavka';

  @override
  String guardsAssigned(int count) {
    return '$count varnostnik(ov)';
  }

  @override
  String get emso => 'EMŠO';

  @override
  String get emsoLabel => 'EMŠO (Enotna matična številka občana)';

  @override
  String get emsoHint => 'Vnesite 13-mestno EMŠO številko';

  @override
  String get emsoValidation => 'EMŠO mora imeti natanko 13 številk';

  @override
  String get emsoInvalid => 'Neveljavna oblika EMŠO';

  @override
  String get slovenianLicenses => 'Slovenske licence';

  @override
  String get npkGostinskiLokali => 'NPK GOSTINSKI LOKALI';

  @override
  String get npkGostinskiLokaliDesc => 'Licenca za gostinske lokale';

  @override
  String get deloNaObjektu => 'DELO NA OBJEKTU';

  @override
  String get deloNaObjektuDesc => 'Licenca za delo na objektu';

  @override
  String get selectLicenses => 'Izberite ustrezne licence';

  @override
  String get noLicensesSelected => 'Ni izbranih licenc';

  @override
  String get employmentStatusEnhanced => 'Status zaposlitve';

  @override
  String get fullTime40h => 'Polni delovni čas (40h/teden)';

  @override
  String get customHours => 'Prilagojene ure';

  @override
  String get customHoursLabel => 'Prilagojene delovne ure na teden';

  @override
  String get customHoursHint => 'Vnesite ure na teden (npr. 20, 30)';

  @override
  String get customHoursValidation => 'Ure morajo biti med 1 in 60';

  @override
  String get hoursPerWeek => 'h/teden';

  @override
  String get licenseManagement => 'Upravljanje licenc';

  @override
  String get userProfile => 'Uporabniški profil';

  @override
  String get personalInformation => 'Osebni podatki';

  @override
  String get employmentInformation => 'Podatki o zaposlitvi';

  @override
  String get licenseInformation => 'Podatki o licencah';

  @override
  String get comingSoon => 'Kmalu na voljo';

  @override
  String get none => 'Brez';
}
