{"@@locale": "en", "appTitle": "Security Guard Management", "@appTitle": {"description": "The title of the application"}, "login": "<PERSON><PERSON>", "logout": "Logout", "signOut": "Sign Out", "email": "Email", "password": "Password", "forgotPassword": "Forgot Password?", "dashboard": "Dashboard", "events": "Events", "eventsManagement": "Events Management", "reports": "Reports", "profile": "Profile", "admin": "Admin", "guards": "Guards", "companies": "Companies", "settings": "Settings", "clockIn": "Clock In", "clockOut": "Clock Out", "accept": "Accept", "decline": "Decline", "pending": "Pending", "active": "Active", "confirmed": "Confirmed", "completed": "Completed", "cancelled": "Cancelled", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "create": "Create", "update": "Update", "filter": "Filter", "all": "All", "loading": "Loading...", "error": "Error", "success": "Success", "noData": "No data available", "retry": "Retry", "welcomeBack": "Welcome back!", "todaysOverview": "Here's your overview for today", "quickActions": "Quick Actions", "eventsOverview": "Events Overview", "totalEvents": "Total Events", "todaysEvents": "Today's Events", "hoursWorked": "Hours Worked", "administrator": "Administrator", "adminPanel": "Admin Panel", "createEvent": "Create Event", "importEvents": "Import Events", "exportEvents": "Export Events", "eventTemplates": "Event Templates", "noEventsFound": "No events found", "createFirstEvent": "Create your first event to get started", "noFilteredEvents": "No {status} events found", "@noFilteredEvents": {"placeholders": {"status": {"type": "String"}}}, "errorLoadingEvents": "Error loading events", "createEventDialog": "Create Event dialog coming soon...", "eventDetailsFor": "Event Details for: {title}", "@eventDetailsFor": {"placeholders": {"title": {"type": "String"}}}, "location": "Location", "duration": "Duration", "hourlyRate": "Hourly Rate", "guardsAssigned": "{count} guard(s)", "@guardsAssigned": {"placeholders": {"count": {"type": "int"}}}, "emso": "EMŠO", "emsoLabel": "EMŠO (Unique Master Citizen Number)", "emsoHint": "Enter 13-digit EMŠO number", "emsoValidation": "EMŠO must be exactly 13 digits", "emsoInvalid": "Invalid EMŠO format", "slovenianLicenses": "Slovenian Licenses", "npkGostinskiLokali": "NPK GOSTINSKI LOKALI", "npkGostinskiLokaliDesc": "Hospitality Venues License", "deloNaObjektu": "DELO NA OBJEKTU", "deloNaObjektuDesc": "On-Site Work License", "selectLicenses": "Select applicable licenses", "noLicensesSelected": "No licenses selected", "employmentStatusEnhanced": "Employment Status", "fullTime40h": "Full Time (40h/week)", "customHours": "Custom Hours", "customHoursLabel": "Custom working hours per week", "customHoursHint": "Enter hours per week (e.g., 20, 30)", "customHoursValidation": "Hours must be between 1 and 60", "hoursPerWeek": "h/week", "licenseManagement": "License Management", "userProfile": "User Profile", "personalInformation": "Personal Information", "employmentInformation": "Employment Information", "licenseInformation": "License Information", "comingSoon": "Coming Soon", "none": "None"}