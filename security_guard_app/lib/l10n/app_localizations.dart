import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_sl.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('sl'),
  ];

  /// No description provided for @appTitle.
  ///
  /// In sl, this message translates to:
  /// **'Upravljanje Varnostnikov'**
  String get appTitle;

  /// No description provided for @login.
  ///
  /// In sl, this message translates to:
  /// **'Prijava'**
  String get login;

  /// No description provided for @logout.
  ///
  /// In sl, this message translates to:
  /// **'Odjava'**
  String get logout;

  /// No description provided for @signOut.
  ///
  /// In sl, this message translates to:
  /// **'Odjavi se'**
  String get signOut;

  /// No description provided for @email.
  ///
  /// In sl, this message translates to:
  /// **'E-pošta'**
  String get email;

  /// No description provided for @password.
  ///
  /// In sl, this message translates to:
  /// **'Geslo'**
  String get password;

  /// No description provided for @forgotPassword.
  ///
  /// In sl, this message translates to:
  /// **'Pozabljeno geslo?'**
  String get forgotPassword;

  /// No description provided for @dashboard.
  ///
  /// In sl, this message translates to:
  /// **'Nadzorna plošča'**
  String get dashboard;

  /// No description provided for @events.
  ///
  /// In sl, this message translates to:
  /// **'Dogodki'**
  String get events;

  /// No description provided for @eventsManagement.
  ///
  /// In sl, this message translates to:
  /// **'Upravljanje dogodkov'**
  String get eventsManagement;

  /// No description provided for @reports.
  ///
  /// In sl, this message translates to:
  /// **'Poročila'**
  String get reports;

  /// No description provided for @profile.
  ///
  /// In sl, this message translates to:
  /// **'Profil'**
  String get profile;

  /// No description provided for @admin.
  ///
  /// In sl, this message translates to:
  /// **'Skrbnik'**
  String get admin;

  /// No description provided for @guards.
  ///
  /// In sl, this message translates to:
  /// **'Varnostniki'**
  String get guards;

  /// No description provided for @companies.
  ///
  /// In sl, this message translates to:
  /// **'Podjetja'**
  String get companies;

  /// No description provided for @settings.
  ///
  /// In sl, this message translates to:
  /// **'Nastavitve'**
  String get settings;

  /// No description provided for @clockIn.
  ///
  /// In sl, this message translates to:
  /// **'Prijava na delo'**
  String get clockIn;

  /// No description provided for @clockOut.
  ///
  /// In sl, this message translates to:
  /// **'Odjava z dela'**
  String get clockOut;

  /// No description provided for @accept.
  ///
  /// In sl, this message translates to:
  /// **'Sprejmi'**
  String get accept;

  /// No description provided for @decline.
  ///
  /// In sl, this message translates to:
  /// **'Zavrni'**
  String get decline;

  /// No description provided for @pending.
  ///
  /// In sl, this message translates to:
  /// **'V obravnavi'**
  String get pending;

  /// No description provided for @active.
  ///
  /// In sl, this message translates to:
  /// **'Aktivno'**
  String get active;

  /// No description provided for @confirmed.
  ///
  /// In sl, this message translates to:
  /// **'Potrjeno'**
  String get confirmed;

  /// No description provided for @completed.
  ///
  /// In sl, this message translates to:
  /// **'Končano'**
  String get completed;

  /// No description provided for @cancelled.
  ///
  /// In sl, this message translates to:
  /// **'Preklicano'**
  String get cancelled;

  /// No description provided for @save.
  ///
  /// In sl, this message translates to:
  /// **'Shrani'**
  String get save;

  /// No description provided for @cancel.
  ///
  /// In sl, this message translates to:
  /// **'Prekliči'**
  String get cancel;

  /// No description provided for @delete.
  ///
  /// In sl, this message translates to:
  /// **'Izbriši'**
  String get delete;

  /// No description provided for @edit.
  ///
  /// In sl, this message translates to:
  /// **'Uredi'**
  String get edit;

  /// No description provided for @add.
  ///
  /// In sl, this message translates to:
  /// **'Dodaj'**
  String get add;

  /// No description provided for @create.
  ///
  /// In sl, this message translates to:
  /// **'Ustvari'**
  String get create;

  /// No description provided for @update.
  ///
  /// In sl, this message translates to:
  /// **'Posodobi'**
  String get update;

  /// No description provided for @filter.
  ///
  /// In sl, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @all.
  ///
  /// In sl, this message translates to:
  /// **'Vse'**
  String get all;

  /// No description provided for @loading.
  ///
  /// In sl, this message translates to:
  /// **'Nalaganje...'**
  String get loading;

  /// No description provided for @error.
  ///
  /// In sl, this message translates to:
  /// **'Napaka'**
  String get error;

  /// No description provided for @success.
  ///
  /// In sl, this message translates to:
  /// **'Uspeh'**
  String get success;

  /// No description provided for @noData.
  ///
  /// In sl, this message translates to:
  /// **'Ni podatkov'**
  String get noData;

  /// No description provided for @retry.
  ///
  /// In sl, this message translates to:
  /// **'Poskusi znova'**
  String get retry;

  /// No description provided for @welcomeBack.
  ///
  /// In sl, this message translates to:
  /// **'Dobrodošli nazaj!'**
  String get welcomeBack;

  /// No description provided for @todaysOverview.
  ///
  /// In sl, this message translates to:
  /// **'Tukaj je vaš pregled za danes'**
  String get todaysOverview;

  /// No description provided for @quickActions.
  ///
  /// In sl, this message translates to:
  /// **'Hitre akcije'**
  String get quickActions;

  /// No description provided for @eventsOverview.
  ///
  /// In sl, this message translates to:
  /// **'Pregled dogodkov'**
  String get eventsOverview;

  /// No description provided for @totalEvents.
  ///
  /// In sl, this message translates to:
  /// **'Skupaj dogodkov'**
  String get totalEvents;

  /// No description provided for @todaysEvents.
  ///
  /// In sl, this message translates to:
  /// **'Današnji dogodki'**
  String get todaysEvents;

  /// No description provided for @hoursWorked.
  ///
  /// In sl, this message translates to:
  /// **'Opravljene ure'**
  String get hoursWorked;

  /// No description provided for @administrator.
  ///
  /// In sl, this message translates to:
  /// **'Administrator'**
  String get administrator;

  /// No description provided for @adminPanel.
  ///
  /// In sl, this message translates to:
  /// **'Skrbniška plošča'**
  String get adminPanel;

  /// No description provided for @createEvent.
  ///
  /// In sl, this message translates to:
  /// **'Ustvari dogodek'**
  String get createEvent;

  /// No description provided for @importEvents.
  ///
  /// In sl, this message translates to:
  /// **'Uvozi dogodke'**
  String get importEvents;

  /// No description provided for @exportEvents.
  ///
  /// In sl, this message translates to:
  /// **'Izvozi dogodke'**
  String get exportEvents;

  /// No description provided for @eventTemplates.
  ///
  /// In sl, this message translates to:
  /// **'Predloge dogodkov'**
  String get eventTemplates;

  /// No description provided for @noEventsFound.
  ///
  /// In sl, this message translates to:
  /// **'Ni najdenih dogodkov'**
  String get noEventsFound;

  /// No description provided for @createFirstEvent.
  ///
  /// In sl, this message translates to:
  /// **'Ustvarite svoj prvi dogodek za začetek'**
  String get createFirstEvent;

  /// No description provided for @noFilteredEvents.
  ///
  /// In sl, this message translates to:
  /// **'Ni najdenih {status} dogodkov'**
  String noFilteredEvents(String status);

  /// No description provided for @errorLoadingEvents.
  ///
  /// In sl, this message translates to:
  /// **'Napaka pri nalaganju dogodkov'**
  String get errorLoadingEvents;

  /// No description provided for @createEventDialog.
  ///
  /// In sl, this message translates to:
  /// **'Dialog za ustvarjanje dogodka kmalu na voljo...'**
  String get createEventDialog;

  /// No description provided for @eventDetailsFor.
  ///
  /// In sl, this message translates to:
  /// **'Podrobnosti dogodka za: {title}'**
  String eventDetailsFor(String title);

  /// No description provided for @location.
  ///
  /// In sl, this message translates to:
  /// **'Lokacija'**
  String get location;

  /// No description provided for @duration.
  ///
  /// In sl, this message translates to:
  /// **'Trajanje'**
  String get duration;

  /// No description provided for @hourlyRate.
  ///
  /// In sl, this message translates to:
  /// **'Urna postavka'**
  String get hourlyRate;

  /// No description provided for @guardsAssigned.
  ///
  /// In sl, this message translates to:
  /// **'{count} varnostnik(ov)'**
  String guardsAssigned(int count);

  /// No description provided for @emso.
  ///
  /// In sl, this message translates to:
  /// **'EMŠO'**
  String get emso;

  /// No description provided for @emsoLabel.
  ///
  /// In sl, this message translates to:
  /// **'EMŠO (Enotna matična številka občana)'**
  String get emsoLabel;

  /// No description provided for @emsoHint.
  ///
  /// In sl, this message translates to:
  /// **'Vnesite 13-mestno EMŠO številko'**
  String get emsoHint;

  /// No description provided for @emsoValidation.
  ///
  /// In sl, this message translates to:
  /// **'EMŠO mora imeti natanko 13 številk'**
  String get emsoValidation;

  /// No description provided for @emsoInvalid.
  ///
  /// In sl, this message translates to:
  /// **'Neveljavna oblika EMŠO'**
  String get emsoInvalid;

  /// No description provided for @slovenianLicenses.
  ///
  /// In sl, this message translates to:
  /// **'Slovenske licence'**
  String get slovenianLicenses;

  /// No description provided for @npkGostinskiLokali.
  ///
  /// In sl, this message translates to:
  /// **'NPK GOSTINSKI LOKALI'**
  String get npkGostinskiLokali;

  /// No description provided for @npkGostinskiLokaliDesc.
  ///
  /// In sl, this message translates to:
  /// **'Licenca za gostinske lokale'**
  String get npkGostinskiLokaliDesc;

  /// No description provided for @deloNaObjektu.
  ///
  /// In sl, this message translates to:
  /// **'DELO NA OBJEKTU'**
  String get deloNaObjektu;

  /// No description provided for @deloNaObjektuDesc.
  ///
  /// In sl, this message translates to:
  /// **'Licenca za delo na objektu'**
  String get deloNaObjektuDesc;

  /// No description provided for @selectLicenses.
  ///
  /// In sl, this message translates to:
  /// **'Izberite ustrezne licence'**
  String get selectLicenses;

  /// No description provided for @noLicensesSelected.
  ///
  /// In sl, this message translates to:
  /// **'Ni izbranih licenc'**
  String get noLicensesSelected;

  /// No description provided for @employmentStatusEnhanced.
  ///
  /// In sl, this message translates to:
  /// **'Status zaposlitve'**
  String get employmentStatusEnhanced;

  /// No description provided for @fullTime40h.
  ///
  /// In sl, this message translates to:
  /// **'Polni delovni čas (40h/teden)'**
  String get fullTime40h;

  /// No description provided for @customHours.
  ///
  /// In sl, this message translates to:
  /// **'Prilagojene ure'**
  String get customHours;

  /// No description provided for @customHoursLabel.
  ///
  /// In sl, this message translates to:
  /// **'Prilagojene delovne ure na teden'**
  String get customHoursLabel;

  /// No description provided for @customHoursHint.
  ///
  /// In sl, this message translates to:
  /// **'Vnesite ure na teden (npr. 20, 30)'**
  String get customHoursHint;

  /// No description provided for @customHoursValidation.
  ///
  /// In sl, this message translates to:
  /// **'Ure morajo biti med 1 in 60'**
  String get customHoursValidation;

  /// No description provided for @hoursPerWeek.
  ///
  /// In sl, this message translates to:
  /// **'h/teden'**
  String get hoursPerWeek;

  /// No description provided for @licenseManagement.
  ///
  /// In sl, this message translates to:
  /// **'Upravljanje licenc'**
  String get licenseManagement;

  /// No description provided for @userProfile.
  ///
  /// In sl, this message translates to:
  /// **'Uporabniški profil'**
  String get userProfile;

  /// No description provided for @personalInformation.
  ///
  /// In sl, this message translates to:
  /// **'Osebni podatki'**
  String get personalInformation;

  /// No description provided for @employmentInformation.
  ///
  /// In sl, this message translates to:
  /// **'Podatki o zaposlitvi'**
  String get employmentInformation;

  /// No description provided for @licenseInformation.
  ///
  /// In sl, this message translates to:
  /// **'Podatki o licencah'**
  String get licenseInformation;

  /// No description provided for @comingSoon.
  ///
  /// In sl, this message translates to:
  /// **'Kmalu na voljo'**
  String get comingSoon;

  /// No description provided for @none.
  ///
  /// In sl, this message translates to:
  /// **'Brez'**
  String get none;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'sl'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'sl':
      return AppLocalizationsSl();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
