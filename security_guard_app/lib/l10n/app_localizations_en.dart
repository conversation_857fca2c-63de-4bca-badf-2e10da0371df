// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Security Guard Management';

  @override
  String get login => 'Login';

  @override
  String get logout => 'Logout';

  @override
  String get signOut => 'Sign Out';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get events => 'Events';

  @override
  String get eventsManagement => 'Events Management';

  @override
  String get reports => 'Reports';

  @override
  String get profile => 'Profile';

  @override
  String get admin => 'Admin';

  @override
  String get guards => 'Guards';

  @override
  String get companies => 'Companies';

  @override
  String get settings => 'Settings';

  @override
  String get clockIn => 'Clock In';

  @override
  String get clockOut => 'Clock Out';

  @override
  String get accept => 'Accept';

  @override
  String get decline => 'Decline';

  @override
  String get pending => 'Pending';

  @override
  String get active => 'Active';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get edit => 'Edit';

  @override
  String get add => 'Add';

  @override
  String get create => 'Create';

  @override
  String get update => 'Update';

  @override
  String get filter => 'Filter';

  @override
  String get all => 'All';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get noData => 'No data available';

  @override
  String get retry => 'Retry';

  @override
  String get welcomeBack => 'Welcome back!';

  @override
  String get todaysOverview => 'Here\'s your overview for today';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get eventsOverview => 'Events Overview';

  @override
  String get totalEvents => 'Total Events';

  @override
  String get todaysEvents => 'Today\'s Events';

  @override
  String get hoursWorked => 'Hours Worked';

  @override
  String get administrator => 'Administrator';

  @override
  String get adminPanel => 'Admin Panel';

  @override
  String get createEvent => 'Create Event';

  @override
  String get importEvents => 'Import Events';

  @override
  String get exportEvents => 'Export Events';

  @override
  String get eventTemplates => 'Event Templates';

  @override
  String get noEventsFound => 'No events found';

  @override
  String get createFirstEvent => 'Create your first event to get started';

  @override
  String noFilteredEvents(String status) {
    return 'No $status events found';
  }

  @override
  String get errorLoadingEvents => 'Error loading events';

  @override
  String get createEventDialog => 'Create Event dialog coming soon...';

  @override
  String eventDetailsFor(String title) {
    return 'Event Details for: $title';
  }

  @override
  String get location => 'Location';

  @override
  String get duration => 'Duration';

  @override
  String get hourlyRate => 'Hourly Rate';

  @override
  String guardsAssigned(int count) {
    return '$count guard(s)';
  }

  @override
  String get emso => 'EMŠO';

  @override
  String get emsoLabel => 'EMŠO (Unique Master Citizen Number)';

  @override
  String get emsoHint => 'Enter 13-digit EMŠO number';

  @override
  String get emsoValidation => 'EMŠO must be exactly 13 digits';

  @override
  String get emsoInvalid => 'Invalid EMŠO format';

  @override
  String get slovenianLicenses => 'Slovenian Licenses';

  @override
  String get npkGostinskiLokali => 'NPK GOSTINSKI LOKALI';

  @override
  String get npkGostinskiLokaliDesc => 'Hospitality Venues License';

  @override
  String get deloNaObjektu => 'DELO NA OBJEKTU';

  @override
  String get deloNaObjektuDesc => 'On-Site Work License';

  @override
  String get selectLicenses => 'Select applicable licenses';

  @override
  String get noLicensesSelected => 'No licenses selected';

  @override
  String get employmentStatusEnhanced => 'Employment Status';

  @override
  String get fullTime40h => 'Full Time (40h/week)';

  @override
  String get customHours => 'Custom Hours';

  @override
  String get customHoursLabel => 'Custom working hours per week';

  @override
  String get customHoursHint => 'Enter hours per week (e.g., 20, 30)';

  @override
  String get customHoursValidation => 'Hours must be between 1 and 60';

  @override
  String get hoursPerWeek => 'h/week';

  @override
  String get licenseManagement => 'License Management';

  @override
  String get userProfile => 'User Profile';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get employmentInformation => 'Employment Information';

  @override
  String get licenseInformation => 'License Information';

  @override
  String get comingSoon => 'Coming Soon';

  @override
  String get none => 'None';
}
