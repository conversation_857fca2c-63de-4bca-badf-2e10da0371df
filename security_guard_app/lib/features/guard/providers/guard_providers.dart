import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../shared/services/auth_service.dart';
import '../../../shared/models/shift_assignment_model.dart';
import '../services/guard_service.dart';

/// Provider for GuardService
final guardServiceProvider = Provider<GuardService>((ref) {
  return GuardService();
});

/// Provider for current guard's assigned shifts
final assignedShiftsProvider = StreamProvider.autoDispose<List<GuardShiftInfo>>((ref) {
  final guardService = ref.watch(guardServiceProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  if (currentUser == null || !currentUser.isGuard) {
    return Stream.value([]);
  }
  
  return guardService.getAssignedShifts(currentUser.id);
});

/// Provider for current guard's pending invitations
final pendingInvitationsProvider = StreamProvider.autoDispose<List<GuardShiftInfo>>((ref) {
  final guardService = ref.watch(guardServiceProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  if (currentUser == null || !currentUser.isGuard) {
    return Stream.value([]);
  }
  
  return guardService.getPendingInvitations(currentUser.id);
});

/// Provider for current guard's upcoming shifts
final upcomingShiftsProvider = StreamProvider.autoDispose<List<GuardShiftInfo>>((ref) {
  final guardService = ref.watch(guardServiceProvider);
  final currentUser = ref.watch(currentUserProvider);

  print('DEBUG: upcomingShiftsProvider - User: ${currentUser?.email}, isGuard: ${currentUser?.isGuard}');

  if (currentUser == null || !currentUser.isGuard) {
    print('DEBUG: upcomingShiftsProvider - Returning empty list');
    return Stream.value([]);
  }

  print('DEBUG: upcomingShiftsProvider - Getting upcoming shifts for: ${currentUser.id}');
  return guardService.getUpcomingShifts(currentUser.id).handleError((error) {
    print('DEBUG: upcomingShiftsProvider - Error: $error');
  });
});

/// Provider for current guard's shift history
final shiftHistoryProvider = StreamProvider.autoDispose<List<GuardShiftInfo>>((ref) {
  final guardService = ref.watch(guardServiceProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  if (currentUser == null || !currentUser.isGuard) {
    return Stream.value([]);
  }
  
  return guardService.getShiftHistory(currentUser.id);
});

/// Provider for current guard's statistics
final guardStatsProvider = FutureProvider.autoDispose<GuardStats>((ref) async {
  final guardService = ref.watch(guardServiceProvider);
  final currentUser = ref.watch(currentUserProvider);

  print('DEBUG: guardStatsProvider - User: ${currentUser?.email}, isGuard: ${currentUser?.isGuard}');

  if (currentUser == null || !currentUser.isGuard) {
    print('DEBUG: guardStatsProvider - Returning empty stats');
    return GuardStats(
      totalCompletedShifts: 0,
      totalHoursWorked: 0,
      upcomingShiftsCount: 0,
      pendingInvitationsCount: 0,
    );
  }

  print('DEBUG: guardStatsProvider - Getting stats for: ${currentUser.id}');
  try {
    final stats = await guardService.getGuardStats(currentUser.id);
    print('DEBUG: guardStatsProvider - Stats loaded: $stats');
    return stats;
  } catch (error) {
    print('DEBUG: guardStatsProvider - Error: $error');
    rethrow;
  }
});

/// Provider for updating shift responses
final shiftResponseProvider = Provider<ShiftResponseNotifier>((ref) {
  final guardService = ref.watch(guardServiceProvider);
  return ShiftResponseNotifier(guardService);
});

/// Notifier for handling shift response updates
class ShiftResponseNotifier {
  final GuardService _guardService;
  
  ShiftResponseNotifier(this._guardService);
  
  Future<void> respondToShift({
    required String eventId,
    required String shiftId,
    required String guardId,
    required AssignmentStatus status,
  }) async {
    await _guardService.updateShiftResponse(eventId, shiftId, guardId, status);
  }
}
