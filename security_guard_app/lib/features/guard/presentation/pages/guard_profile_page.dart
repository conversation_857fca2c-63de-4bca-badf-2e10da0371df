import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/services/auth_service.dart';
import '../../../../shared/models/user_model.dart';

class GuardProfilePage extends ConsumerStatefulWidget {
  const GuardProfilePage({super.key});

  @override
  ConsumerState<GuardProfilePage> createState() => _GuardProfilePageState();
}

class _GuardProfilePageState extends ConsumerState<GuardProfilePage> {
  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile header
            _buildProfileHeader(currentUser),
            
            const SizedBox(height: 24),
            
            // Personal information
            _buildPersonalInfoSection(currentUser),
            
            const SizedBox(height: 24),
            
            // Employment information
            _buildEmploymentInfoSection(currentUser),
            
            const SizedBox(height: 24),
            
            // License information
            _buildLicenseInfoSection(currentUser),
            
            const SizedBox(height: 24),
            
            // Settings section
            _buildSettingsSection(),
            
            const SizedBox(height: 24),
            
            // Logout button
            _buildLogoutSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(currentUser) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white.withOpacity(0.2),
            child: Text(
              currentUser?.firstName.isNotEmpty == true 
                  ? currentUser!.firstName[0].toUpperCase()
                  : 'G',
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  currentUser?.fullName ?? 'Guard User',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  currentUser?.email ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    AppLocale.securityGuard.getString(context),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoSection(currentUser) {
    return _buildSection(
      title: AppLocale.personalInformation.getString(context),
      icon: Icons.person,
      children: [
        _buildInfoRow(
          AppLocale.firstName.getString(context),
          currentUser?.firstName ?? '-',
        ),
        _buildInfoRow(
          AppLocale.lastName.getString(context),
          currentUser?.lastName ?? '-',
        ),
        _buildInfoRow(
          AppLocale.email.getString(context),
          currentUser?.email ?? '-',
        ),
        _buildInfoRow(
          AppLocale.phone.getString(context),
          currentUser?.phone ?? '-',
        ),
        if (currentUser?.emso?.isNotEmpty == true)
          _buildInfoRow(
            'EMŠO',
            currentUser!.emso!,
          ),
      ],
    );
  }

  Widget _buildEmploymentInfoSection(currentUser) {
    return _buildSection(
      title: AppLocale.employmentInformation.getString(context),
      icon: Icons.work,
      children: [
        _buildInfoRow(
          AppLocale.employmentStatus.getString(context),
          _getEmploymentStatusText(currentUser?.employmentStatus),
        ),
        if (currentUser?.employmentStatus == EmploymentStatus.custom && currentUser?.customWorkingHours != null)
          _buildInfoRow(
            AppLocale.customHoursLabel.getString(context),
            '${currentUser!.customWorkingHours} ${AppLocale.hoursPerWeek.getString(context)}',
          ),
        _buildInfoRow(
          AppLocale.status.getString(context),
          currentUser?.isActive == true
              ? AppLocale.active.getString(context)
              : AppLocale.inactive.getString(context),
        ),
      ],
    );
  }

  Widget _buildLicenseInfoSection(currentUser) {
    return _buildSection(
      title: AppLocale.licenses.getString(context),
      icon: Icons.verified,
      children: [
        _buildLicenseRow(
          'NPK GOSTINSKI LOKALI',
          currentUser?.hasNpkLicense == true,
        ),
        _buildLicenseRow(
          'DELO NA OBJEKTU',
          currentUser?.hasDeloNaObjektuLicense == true,
        ),
      ],
    );
  }

  Widget _buildSettingsSection() {
    return _buildSection(
      title: AppLocale.settings.getString(context),
      icon: Icons.settings,
      children: [
        ListTile(
          leading: const Icon(Icons.language),
          title: Text(AppLocale.language.getString(context)),
          subtitle: Text(AppLocale.slovenian.getString(context)),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // TODO: Implement language selection
          },
        ),
        ListTile(
          leading: const Icon(Icons.notifications),
          title: Text(AppLocale.notifications.getString(context)),
          subtitle: Text(AppLocale.manageNotifications.getString(context)),
          trailing: const Icon(Icons.chevron_right),
          onTap: () {
            // TODO: Implement notification settings
          },
        ),
      ],
    );
  }

  Widget _buildLogoutSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.errorColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.logout,
            color: AppTheme.errorColor,
            size: 32,
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: _handleLogout,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 48),
            ),
            child: Text(AppLocale.logout.getString(context)),
          ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(icon, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLicenseRow(String licenseName, bool hasLicense) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Row(
        children: [
          Icon(
            hasLicense ? Icons.check_circle : Icons.cancel,
            color: hasLicense ? AppTheme.successColor : AppTheme.errorColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              licenseName,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: hasLicense ? AppTheme.successColor : AppTheme.errorColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getEmploymentStatusText(EmploymentStatus? status) {
    if (status == null) return '-';

    switch (status) {
      case EmploymentStatus.full_time:
        return AppLocale.fullTime40h.getString(context);
      case EmploymentStatus.custom:
        return AppLocale.customHours.getString(context);
      case EmploymentStatus.inactive:
        return AppLocale.inactive.getString(context);
      default:
        return '-';
    }
  }

  Future<void> _handleLogout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocale.logout.getString(context)),
        content: Text(AppLocale.logoutConfirmation.getString(context)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocale.cancel.getString(context)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocale.logout.getString(context)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await ref.read(authServiceProvider).signOut();
        ref.read(currentUserProvider.notifier).state = null;
        if (mounted) {
          context.go('/login');
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Logout failed: ${e.toString()}'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    }
  }
}
