import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../shared/services/auth_service.dart';
import '../../../../core/localization/app_localization.dart';
import 'guard_home_page.dart';
import 'guard_invitations_page.dart';
import 'guard_profile_page.dart';

class GuardAppShell extends ConsumerStatefulWidget {
  const GuardAppShell({super.key});

  @override
  ConsumerState<GuardAppShell> createState() => _GuardAppShellState();
}

class _GuardAppShellState extends ConsumerState<GuardAppShell> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const GuardHomePage(),
    const GuardInvitationsPage(),
    const GuardProfilePage(),
  ];

  @override
  void initState() {
    super.initState();
    // Ensure user is loaded when guard app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCurrentUser();
    });
  }

  Future<void> _loadCurrentUser() async {
    try {
      final authService = ref.read(authServiceProvider);
      final user = await authService.getCurrentUser();

      print('DEBUG: GuardAppShell - Loading user: ${user?.email}, Role: ${user?.role}');

      if (user != null) {
        ref.read(currentUserProvider.notifier).state = user;
        print('DEBUG: GuardAppShell - User set in provider: ${user.email}');
      } else {
        print('DEBUG: GuardAppShell - No user found, redirecting to login');
        if (mounted) {
          context.go('/login');
        }
      }
    } catch (e) {
      print('DEBUG: GuardAppShell - Error loading user: $e');
      if (mounted) {
        context.go('/login');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);

    // Show loading screen while user is being loaded
    if (currentUser == null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 16),
              Text(
                'Loading guard interface...',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_getPageTitle()),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // Profile avatar
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: CircleAvatar(
              backgroundColor: Colors.white.withOpacity(0.2),
              child: Text(
                currentUser?.firstName.isNotEmpty == true 
                    ? currentUser!.firstName[0].toUpperCase()
                    : 'G',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ],
      ),
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: Theme.of(context).colorScheme.primary,
        unselectedItemColor: Colors.grey,
        items: [
          BottomNavigationBarItem(
            icon: const Icon(Icons.home),
            label: AppLocale.home.getString(context),
          ),
          BottomNavigationBarItem(
            icon: Stack(
              children: [
                const Icon(Icons.mail),
                // TODO: Add notification badge for pending invitations
                // if (pendingInvitationsCount > 0)
                //   Positioned(
                //     right: 0,
                //     top: 0,
                //     child: Container(
                //       padding: const EdgeInsets.all(2),
                //       decoration: BoxDecoration(
                //         color: AppTheme.errorColor,
                //         borderRadius: BorderRadius.circular(10),
                //       ),
                //       constraints: const BoxConstraints(
                //         minWidth: 16,
                //         minHeight: 16,
                //       ),
                //       child: Text(
                //         '$pendingInvitationsCount',
                //         style: const TextStyle(
                //           color: Colors.white,
                //           fontSize: 12,
                //         ),
                //         textAlign: TextAlign.center,
                //       ),
                //     ),
                //   ),
              ],
            ),
            label: AppLocale.invitations.getString(context),
          ),
          BottomNavigationBarItem(
            icon: const Icon(Icons.person),
            label: AppLocale.profile.getString(context),
          ),
        ],
      ),
    );
  }

  String _getPageTitle() {
    switch (_currentIndex) {
      case 0:
        return AppLocale.myShifts.getString(context);
      case 1:
        return AppLocale.invitations.getString(context);
      case 2:
        return AppLocale.profile.getString(context);
      default:
        return AppLocale.guardApp.getString(context);
    }
  }
}
