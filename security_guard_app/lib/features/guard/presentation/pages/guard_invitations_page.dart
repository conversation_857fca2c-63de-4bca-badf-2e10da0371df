import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/services/auth_service.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../../providers/guard_providers.dart';
import '../../services/guard_service.dart';
import '../widgets/shift_card.dart';

class GuardInvitationsPage extends ConsumerStatefulWidget {
  const GuardInvitationsPage({super.key});

  @override
  ConsumerState<GuardInvitationsPage> createState() => _GuardInvitationsPageState();
}

class _GuardInvitationsPageState extends ConsumerState<GuardInvitationsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _refreshInvitations,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header section
              _buildHeaderSection(),
              
              const SizedBox(height: 24),
              
              // Pending invitations
              _buildPendingInvitationsSection(),
              
              const SizedBox(height: 24),
              
              // Responded invitations
              _buildRespondedInvitationsSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.warningColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.warningColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.mail,
            color: AppTheme.warningColor,
            size: 32,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocale.shiftInvitations.getString(context),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.warningColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  AppLocale.invitationsDescription.getString(context),
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.warningColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingInvitationsSection() {
    final pendingInvitationsAsync = ref.watch(pendingInvitationsProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppLocale.pendingInvitations.getString(context),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            pendingInvitationsAsync.when(
              data: (invitations) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  invitations.length.toString(),
                  style: TextStyle(
                    color: AppTheme.warningColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
              loading: () => const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              error: (_, __) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.errorColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '!',
                  style: TextStyle(
                    color: AppTheme.errorColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        pendingInvitationsAsync.when(
          data: (invitations) {
            if (invitations.isEmpty) {
              return _buildEmptyState(
                icon: Icons.mail_outline,
                title: AppLocale.noPendingInvitations.getString(context),
                subtitle: AppLocale.noPendingInvitationsMessage.getString(context),
                color: AppTheme.warningColor,
              );
            }

            return Column(
              children: invitations.map((shiftInfo) {
                return ShiftCard(
                  shiftInfo: shiftInfo,
                  showActions: true,
                  onTap: () => _navigateToShiftDetail(shiftInfo),
                  onStatusChange: (status) => _handleInvitationResponse(shiftInfo, status),
                );
              }).toList(),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildEmptyState(
            icon: Icons.error,
            title: 'Error loading invitations',
            subtitle: 'Please try again later',
            color: AppTheme.errorColor,
          ),
        ),
      ],
    );
  }

  Widget _buildRespondedInvitationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.recentResponses.getString(context),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // TODO: Replace with actual response data
        _buildEmptyState(
          icon: Icons.check_circle_outline,
          title: AppLocale.noRecentResponses.getString(context),
          subtitle: AppLocale.noRecentResponsesMessage.getString(context),
          color: Colors.grey,
        ),
      ],
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 48,
            color: color,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // TODO: Implement invitation card widget
  Widget _buildInvitationCard({
    required String eventTitle,
    required String location,
    required DateTime startTime,
    required DateTime endTime,
    required VoidCallback onAccept,
    required VoidCallback onDecline,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              eventTitle,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  location,
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${_formatDateTime(startTime)} - ${_formatDateTime(endTime)}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: onAccept,
                    icon: const Icon(Icons.check),
                    label: Text(AppLocale.interested.getString(context)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.successColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onDecline,
                    icon: const Icon(Icons.close),
                    label: Text(AppLocale.unavailable.getString(context)),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: AppTheme.errorColor,
                      side: BorderSide(color: AppTheme.errorColor),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    // TODO: Implement proper Slovenian date formatting
    return '${dateTime.day}.${dateTime.month}.${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _refreshInvitations() async {
    ref.invalidate(pendingInvitationsProvider);
  }

  void _navigateToShiftDetail(GuardShiftInfo shiftInfo) {
    // TODO: Navigate to shift detail page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening details for ${shiftInfo.event.title}'),
      ),
    );
  }

  Future<void> _handleInvitationResponse(GuardShiftInfo shiftInfo, AssignmentStatus status) async {
    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) return;

      final shiftResponseNotifier = ref.read(shiftResponseProvider);
      await shiftResponseNotifier.respondToShift(
        eventId: shiftInfo.event.id,
        shiftId: shiftInfo.shift.id,
        guardId: currentUser.id,
        status: status,
      );

      if (mounted) {
        final message = status == AssignmentStatus.interested
            ? 'You have accepted the shift invitation'
            : 'You have declined the shift invitation';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: status == AssignmentStatus.interested
                ? AppTheme.successColor
                : AppTheme.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating response: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
