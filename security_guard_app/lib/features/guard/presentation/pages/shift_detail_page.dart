import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../../../../shared/services/auth_service.dart';
import '../../providers/guard_providers.dart';
import '../../services/guard_service.dart';

class ShiftDetailPage extends ConsumerWidget {
  final GuardShiftInfo shiftInfo;

  const ShiftDetailPage({
    super.key,
    required this.shiftInfo,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Text(shiftInfo.event.title),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        actions: [
          if (shiftInfo.assignment.status == AssignmentStatus.pending)
            PopupMenuButton<AssignmentStatus>(
              onSelected: (status) => _handleStatusChange(context, ref, status),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: AssignmentStatus.interested,
                  child: Row(
                    children: [
                      Icon(Icons.check, color: AppTheme.successColor),
                      const SizedBox(width: 8),
                      Text(AppLocale.interested.getString(context)),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: AssignmentStatus.unavailable,
                  child: Row(
                    children: [
                      Icon(Icons.close, color: AppTheme.errorColor),
                      const SizedBox(width: 8),
                      Text(AppLocale.unavailable.getString(context)),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            _buildStatusCard(context),
            
            const SizedBox(height: 16),
            
            // Event Information
            _buildEventInfoCard(context),
            
            const SizedBox(height: 16),
            
            // Shift Details
            _buildShiftDetailsCard(context),
            
            const SizedBox(height: 16),
            
            // Requirements
            if (shiftInfo.shift.requiredLicenses.isNotEmpty)
              _buildRequirementsCard(context),
            
            const SizedBox(height: 16),
            
            // Notes
            if (shiftInfo.shift.notes?.isNotEmpty == true)
              _buildNotesCard(context),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            if (shiftInfo.assignment.status == AssignmentStatus.pending)
              _buildActionButtons(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(BuildContext context) {
    Color statusColor;
    IconData statusIcon;
    String statusText;
    
    switch (shiftInfo.assignment.status) {
      case AssignmentStatus.pending:
        statusColor = AppTheme.warningColor;
        statusIcon = Icons.schedule;
        statusText = AppLocale.pending.getString(context);
        break;
      case AssignmentStatus.interested:
        statusColor = AppTheme.successColor;
        statusIcon = Icons.check_circle;
        statusText = AppLocale.interested.getString(context);
        break;
      case AssignmentStatus.confirmed:
        statusColor = AppTheme.successColor;
        statusIcon = Icons.verified;
        statusText = AppLocale.confirmed.getString(context);
        break;
      case AssignmentStatus.unavailable:
        statusColor = AppTheme.errorColor;
        statusIcon = Icons.cancel;
        statusText = AppLocale.unavailable.getString(context);
        break;
      case AssignmentStatus.completed:
        statusColor = AppTheme.primaryColor;
        statusIcon = Icons.task_alt;
        statusText = AppLocale.completed.getString(context);
        break;
      case AssignmentStatus.cancelled:
        statusColor = Colors.grey;
        statusIcon = Icons.block;
        statusText = AppLocale.cancelled.getString(context);
        break;
    }
    
    return Card(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          gradient: LinearGradient(
            colors: [
              statusColor.withOpacity(0.1),
              statusColor.withOpacity(0.05),
            ],
          ),
        ),
        child: Row(
          children: [
            Icon(statusIcon, color: statusColor, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (shiftInfo.assignment.responseTime != null)
                    Text(
                      'Responded: ${DateFormat('MMM d, HH:mm').format(shiftInfo.assignment.responseTime!)}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventInfoCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.eventDetails.getString(context),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.event,
              AppLocale.eventTitle.getString(context),
              shiftInfo.event.title,
            ),
            _buildInfoRow(
              Icons.location_on,
              AppLocale.location.getString(context),
              shiftInfo.event.location,
            ),
            if (shiftInfo.event.description.isNotEmpty)
              _buildInfoRow(
                Icons.description,
                AppLocale.description.getString(context),
                shiftInfo.event.description,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildShiftDetailsCard(BuildContext context) {
    final startTime = DateFormat('EEEE, MMM d, yyyy \'at\' HH:mm').format(shiftInfo.shift.startTime);
    final endTime = DateFormat('EEEE, MMM d, yyyy \'at\' HH:mm').format(shiftInfo.shift.endTime);
    final duration = shiftInfo.shift.endTime.difference(shiftInfo.shift.startTime);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Shift Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.play_arrow,
              AppLocale.startTime.getString(context),
              startTime,
            ),
            _buildInfoRow(
              Icons.stop,
              AppLocale.endTime.getString(context),
              endTime,
            ),
            _buildInfoRow(
              Icons.access_time,
              AppLocale.duration.getString(context),
              '${duration.inHours}h ${duration.inMinutes % 60}m',
            ),
            if (shiftInfo.assignment.customHours != null)
              _buildInfoRow(
                Icons.schedule,
                'Working Hours',
                '${shiftInfo.assignment.customHours!.toStringAsFixed(1)} hours (custom)',
                highlight: true,
              ),
            _buildInfoRow(
              Icons.people,
              AppLocale.requiredGuards.getString(context),
              shiftInfo.shift.requiredGuards.toString(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRequirementsCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Requirements',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: shiftInfo.shift.requiredLicenses.map((license) {
                return Chip(
                  label: Text(license),
                  backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                  side: BorderSide(color: AppTheme.primaryColor.withOpacity(0.3)),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.notes.getString(context),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              shiftInfo.shift.notes!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, {bool highlight = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: highlight ? FontWeight.bold : FontWeight.normal,
                    color: highlight ? AppTheme.primaryColor : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _handleStatusChange(context, ref, AssignmentStatus.unavailable),
            icon: const Icon(Icons.close),
            label: Text(AppLocale.unavailable.getString(context)),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.errorColor,
              side: BorderSide(color: AppTheme.errorColor),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _handleStatusChange(context, ref, AssignmentStatus.interested),
            icon: const Icon(Icons.check),
            label: Text(AppLocale.interested.getString(context)),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.successColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleStatusChange(BuildContext context, WidgetRef ref, AssignmentStatus status) async {
    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) return;

      final shiftResponseNotifier = ref.read(shiftResponseProvider);
      await shiftResponseNotifier.respondToShift(
        eventId: shiftInfo.event.id,
        shiftId: shiftInfo.shift.id,
        guardId: currentUser.id,
        status: status,
      );

      if (context.mounted) {
        final message = status == AssignmentStatus.interested
            ? 'You have accepted the shift invitation'
            : 'You have declined the shift invitation';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: status == AssignmentStatus.interested
                ? AppTheme.successColor
                : AppTheme.errorColor,
          ),
        );
        
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating response: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
