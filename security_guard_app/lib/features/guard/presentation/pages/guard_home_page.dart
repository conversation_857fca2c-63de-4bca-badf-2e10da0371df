import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/services/auth_service.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../../providers/guard_providers.dart';
import '../../services/guard_service.dart';
import '../widgets/shift_card.dart';
import '../widgets/stats_card.dart';
import '../../../../shared/services/auth_service.dart';

class GuardHomePage extends ConsumerStatefulWidget {
  const GuardHomePage({super.key});

  @override
  ConsumerState<GuardHomePage> createState() => _GuardHomePageState();
}

class _GuardHomePageState extends ConsumerState<GuardHomePage> {
  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final guardStatsAsync = ref.watch(guardStatsProvider);
    final upcomingShiftsAsync = ref.watch(upcomingShiftsProvider);
    final pendingInvitationsAsync = ref.watch(pendingInvitationsProvider);

    // Debug logging
    print('DEBUG: GuardHomePage - Current user: ${currentUser?.email}, Role: ${currentUser?.role}');
    print('DEBUG: GuardHomePage - Stats state: ${guardStatsAsync.runtimeType}');
    print('DEBUG: GuardHomePage - Upcoming shifts state: ${upcomingShiftsAsync.runtimeType}');

    if (currentUser == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _refreshData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section
            _buildWelcomeSection(currentUser.firstName),

            const SizedBox(height: 24),

            // Quick stats
            guardStatsAsync.when(
              data: (stats) => _buildQuickStats(stats),
              loading: () => _buildLoadingStats(),
              error: (error, stack) => _buildErrorStats(),
            ),

            const SizedBox(height: 24),

            // Upcoming shifts section
            _buildUpcomingShiftsSection(upcomingShiftsAsync),

            const SizedBox(height: 24),

            // Pending invitations section
            _buildPendingInvitationsSection(pendingInvitationsAsync),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(String firstName) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${AppLocale.welcome.getString(context)}, $firstName!',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocale.guardWelcomeMessage.getString(context),
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(GuardStats stats) {
    return QuickStatsRow(
      upcomingShifts: stats.upcomingShiftsCount.toString(),
      pendingInvitations: stats.pendingInvitationsCount.toString(),
      totalHours: stats.totalHoursWorked.toStringAsFixed(1),
      onUpcomingTap: () {
        // TODO: Navigate to upcoming shifts
      },
      onInvitationsTap: () {
        // TODO: Navigate to invitations tab
      },
      onHoursTap: () {
        // TODO: Navigate to shift history
      },
    );
  }

  Widget _buildLoadingStats() {
    return QuickStatsRow(
      upcomingShifts: '...',
      pendingInvitations: '...',
      totalHours: '...',
    );
  }

  Widget _buildErrorStats() {
    return QuickStatsRow(
      upcomingShifts: '0',
      pendingInvitations: '0',
      totalHours: '0',
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingShiftsSection(AsyncValue<List<GuardShiftInfo>> upcomingShiftsAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppLocale.upcomingShifts.getString(context),
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to all shifts view
              },
              child: Text(AppLocale.viewAll.getString(context)),
            ),
          ],
        ),
        const SizedBox(height: 16),

        upcomingShiftsAsync.when(
          data: (shifts) {
            if (shifts.isEmpty) {
              return _buildEmptyState(
                icon: Icons.schedule,
                title: AppLocale.noUpcomingShifts.getString(context),
                subtitle: AppLocale.noUpcomingShiftsMessage.getString(context),
              );
            }

            return Column(
              children: shifts.take(3).map((shiftInfo) {
                return ShiftCard(
                  shiftInfo: shiftInfo,
                  onTap: () => _navigateToShiftDetail(shiftInfo),
                );
              }).toList(),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildEmptyState(
            icon: Icons.error,
            title: 'Error loading shifts',
            subtitle: 'Please try again later',
          ),
        ),
      ],
    );
  }

  Widget _buildPendingInvitationsSection(AsyncValue<List<GuardShiftInfo>> pendingInvitationsAsync) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.pendingInvitations.getString(context),
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        pendingInvitationsAsync.when(
          data: (invitations) {
            if (invitations.isEmpty) {
              return _buildEmptyState(
                icon: Icons.mail_outline,
                title: AppLocale.noPendingInvitations.getString(context),
                subtitle: AppLocale.noPendingInvitationsMessage.getString(context),
              );
            }

            return Column(
              children: invitations.take(2).map((shiftInfo) {
                return ShiftCard(
                  shiftInfo: shiftInfo,
                  showActions: true,
                  onTap: () => _navigateToShiftDetail(shiftInfo),
                  onStatusChange: (status) => _handleInvitationResponse(shiftInfo, status),
                );
              }).toList(),
            );
          },
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildEmptyState(
            icon: Icons.error,
            title: 'Error loading invitations',
            subtitle: 'Please try again later',
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 48,
            color: Colors.grey,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _refreshData() async {
    ref.invalidate(guardStatsProvider);
    ref.invalidate(upcomingShiftsProvider);
    ref.invalidate(pendingInvitationsProvider);
  }

  void _navigateToShiftDetail(GuardShiftInfo shiftInfo) {
    // TODO: Navigate to shift detail page
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening details for ${shiftInfo.event.title}'),
      ),
    );
  }

  Future<void> _handleInvitationResponse(GuardShiftInfo shiftInfo, AssignmentStatus status) async {
    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) return;

      final shiftResponseNotifier = ref.read(shiftResponseProvider);
      await shiftResponseNotifier.respondToShift(
        eventId: shiftInfo.event.id,
        shiftId: shiftInfo.shift.id,
        guardId: currentUser.id,
        status: status,
      );

      if (mounted) {
        final message = status == AssignmentStatus.interested
            ? 'You have accepted the shift invitation'
            : 'You have declined the shift invitation';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: status == AssignmentStatus.interested
                ? AppTheme.successColor
                : AppTheme.errorColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating response: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
