import 'package:flutter/material.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../../services/guard_service.dart';

class ShiftCard extends StatelessWidget {
  final GuardShiftInfo shiftInfo;
  final VoidCallback? onTap;
  final bool showActions;
  final Function(AssignmentStatus)? onStatusChange;

  const ShiftCard({
    super.key,
    required this.shiftInfo,
    this.onTap,
    this.showActions = false,
    this.onStatusChange,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with event title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      shiftInfo.event.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Location
              Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      shiftInfo.event.location,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // Time and duration
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatShiftTime(),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${shiftInfo.effectiveHours.toStringAsFixed(1)} ${AppLocale.hours.getString(context)}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
              
              // Custom hours indicator
              if (shiftInfo.assignment.customHours != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 14,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Custom working hours',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
              
              // Required licenses
              if (shiftInfo.shift.requiredLicenses.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  children: shiftInfo.shift.requiredLicenses.map((license) {
                    return Chip(
                      label: Text(
                        license,
                        style: const TextStyle(fontSize: 10),
                      ),
                      backgroundColor: Colors.blue[50],
                      side: BorderSide(color: Colors.blue[200]!),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    );
                  }).toList(),
                ),
              ],
              
              // Action buttons for pending invitations
              if (showActions && shiftInfo.assignment.status == AssignmentStatus.pending) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => onStatusChange?.call(AssignmentStatus.unavailable),
                        icon: const Icon(Icons.close, size: 16),
                        label: Text(AppLocale.unavailable.getString(context)),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red[600],
                          side: BorderSide(color: Colors.red[300]!),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => onStatusChange?.call(AssignmentStatus.interested),
                        icon: const Icon(Icons.check, size: 16),
                        label: Text(AppLocale.interested.getString(context)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.successColor,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color backgroundColor;
    Color textColor;
    String statusText;
    
    switch (shiftInfo.assignment.status) {
      case AssignmentStatus.pending:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[800]!;
        statusText = AppLocale.pending.getString(context);
        break;
      case AssignmentStatus.interested:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
        statusText = AppLocale.interested.getString(context);
        break;
      case AssignmentStatus.confirmed:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
        statusText = AppLocale.confirmed.getString(context);
        break;
      case AssignmentStatus.unavailable:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[800]!;
        statusText = AppLocale.unavailable.getString(context);
        break;
      case AssignmentStatus.completed:
        backgroundColor = Colors.blue[100]!;
        textColor = Colors.blue[800]!;
        statusText = AppLocale.completed.getString(context);
        break;
      case AssignmentStatus.cancelled:
        backgroundColor = Colors.grey[100]!;
        textColor = Colors.grey[800]!;
        statusText = AppLocale.cancelled.getString(context);
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatShiftTime() {
    final startDate = DateFormat('MMM d').format(shiftInfo.shift.startTime);
    final startTime = DateFormat('HH:mm').format(shiftInfo.shift.startTime);
    final endTime = DateFormat('HH:mm').format(shiftInfo.shift.endTime);
    
    // Check if shift spans multiple days
    if (DateFormat('yyyy-MM-dd').format(shiftInfo.shift.startTime) != 
        DateFormat('yyyy-MM-dd').format(shiftInfo.shift.endTime)) {
      final endDate = DateFormat('MMM d').format(shiftInfo.shift.endTime);
      return '$startDate $startTime - $endDate $endTime';
    } else {
      return '$startDate $startTime - $endTime';
    }
  }
}
