import 'package:flutter/material.dart';

import '../../../../core/theme/app_theme.dart';

class StatsCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;

  const StatsCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardColor = color ?? AppTheme.primaryColor;
    
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                cardColor.withOpacity(0.1),
                cardColor.withOpacity(0.05),
              ],
            ),
          ),
          child: <PERSON>umn(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: cardColor,
              ),
              const SizedBox(height: 8),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: cardColor,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class QuickStatsRow extends StatelessWidget {
  final String upcomingShifts;
  final String pendingInvitations;
  final String totalHours;
  final VoidCallback? onUpcomingTap;
  final VoidCallback? onInvitationsTap;
  final VoidCallback? onHoursTap;

  const QuickStatsRow({
    super.key,
    required this.upcomingShifts,
    required this.pendingInvitations,
    required this.totalHours,
    this.onUpcomingTap,
    this.onInvitationsTap,
    this.onHoursTap,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: StatsCard(
            title: 'Upcoming\nShifts',
            value: upcomingShifts,
            icon: Icons.schedule,
            color: AppTheme.primaryColor,
            onTap: onUpcomingTap,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: StatsCard(
            title: 'Pending\nInvitations',
            value: pendingInvitations,
            icon: Icons.mail_outline,
            color: AppTheme.warningColor,
            onTap: onInvitationsTap,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: StatsCard(
            title: 'Total\nHours',
            value: totalHours,
            icon: Icons.access_time,
            color: AppTheme.successColor,
            onTap: onHoursTap,
          ),
        ),
      ],
    );
  }
}
