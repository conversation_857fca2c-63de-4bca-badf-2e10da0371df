import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../shared/models/event_model.dart';
import '../../../shared/models/event_shift_model.dart';
import '../../../shared/models/shift_assignment_model.dart';
import '../../../shared/models/user_model.dart';

class GuardService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get all shifts assigned to a specific guard
  Stream<List<GuardShiftInfo>> getAssignedShifts(String guardId) {
    print('DEBUG: GuardService - Getting assigned shifts for guard: $guardId');

    return _firestore
        .collection('events')
        .where('assignedGuardIds', arrayContains: guardId)
        .snapshots()
        .asyncMap((snapshot) async {
      List<GuardShiftInfo> guardShifts = [];

      print('DEBUG: GuardService - Found ${snapshot.docs.length} events for guard');

      for (var doc in snapshot.docs) {
        try {
          final event = EventModel.fromFirestore(doc);
          print('DEBUG: GuardService - Processing event: ${event.title}');

          // Find shifts where this guard is assigned
          for (var shift in event.shifts) {
            final assignment = shift.assignments.firstWhere(
              (assignment) => assignment.guardId == guardId,
              orElse: () => ShiftAssignment(
                guardId: '',
                startTime: DateTime.now(),
                endTime: DateTime.now(),
                status: AssignmentStatus.pending,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            if (assignment.guardId == guardId) {
              print('DEBUG: GuardService - Found assignment: ${assignment.status} for shift ${shift.id}');
              guardShifts.add(GuardShiftInfo(
                event: event,
                shift: shift,
                assignment: assignment,
              ));
            }
          }
        } catch (e) {
          debugPrint('Error parsing event ${doc.id}: $e');
        }
      }

      print('DEBUG: GuardService - Total guard shifts found: ${guardShifts.length}');

      // Sort by start time
      guardShifts.sort((a, b) => a.shift.startTime.compareTo(b.shift.startTime));
      return guardShifts;
    }).handleError((error) {
      debugPrint('Error in getAssignedShifts stream: $error');
      throw Exception('Failed to load assigned shifts: ${error.toString()}');
    });
  }

  /// Get pending invitations for a guard (shifts with pending status)
  Stream<List<GuardShiftInfo>> getPendingInvitations(String guardId) {
    return getAssignedShifts(guardId).map((shifts) {
      print('DEBUG: getPendingInvitations - Total shifts: ${shifts.length}');

      final pendingShifts = shifts.where((shiftInfo) {
        final isPending = shiftInfo.assignment.status == AssignmentStatus.pending;
        print('DEBUG: getPendingInvitations - Event ${shiftInfo.event.title}: status=${shiftInfo.assignment.status}, isPending=$isPending');
        return isPending;
      }).toList();

      print('DEBUG: getPendingInvitations - Pending shifts found: ${pendingShifts.length}');
      return pendingShifts;
    });
  }

  /// Get confirmed shifts for a guard (both confirmed and interested status)
  Stream<List<GuardShiftInfo>> getConfirmedShifts(String guardId) {
    return getAssignedShifts(guardId).map((shifts) {
      print('DEBUG: getConfirmedShifts - Total assigned shifts: ${shifts.length}');

      final confirmedShifts = shifts.where((shiftInfo) {
        final isConfirmed = shiftInfo.assignment.status == AssignmentStatus.confirmed ||
                           shiftInfo.assignment.status == AssignmentStatus.interested;
        print('DEBUG: getConfirmedShifts - Shift ${shiftInfo.event.title}: status=${shiftInfo.assignment.status}, isConfirmed=$isConfirmed');
        return isConfirmed;
      }).toList();

      print('DEBUG: getConfirmedShifts - Confirmed shifts found: ${confirmedShifts.length}');
      return confirmedShifts;
    });
  }

  /// Get upcoming shifts (confirmed shifts in the future)
  Stream<List<GuardShiftInfo>> getUpcomingShifts(String guardId) {
    return getConfirmedShifts(guardId).map((shifts) {
      final now = DateTime.now();
      print('DEBUG: getUpcomingShifts - Current time: $now');
      print('DEBUG: getUpcomingShifts - Total confirmed shifts: ${shifts.length}');

      final upcomingShifts = shifts.where((shiftInfo) {
        final isAfterNow = shiftInfo.shift.startTime.isAfter(now);
        print('DEBUG: getUpcomingShifts - Shift ${shiftInfo.event.title}: ${shiftInfo.shift.startTime} > $now = $isAfterNow');
        return isAfterNow;
      }).toList();

      print('DEBUG: getUpcomingShifts - Upcoming shifts found: ${upcomingShifts.length}');
      return upcomingShifts;
    });
  }

  /// Update shift response (accept/decline invitation)
  Future<void> updateShiftResponse(
    String eventId,
    String shiftId,
    String guardId,
    AssignmentStatus status,
  ) async {
    try {
      final eventDoc = await _firestore.collection('events').doc(eventId).get();
      if (!eventDoc.exists) {
        throw Exception('Event not found');
      }

      final event = EventModel.fromFirestore(eventDoc);
      final updatedShifts = event.shifts.map((shift) {
        if (shift.id == shiftId) {
          final updatedAssignments = shift.assignments.map((assignment) {
            if (assignment.guardId == guardId) {
              return assignment.copyWith(
                status: status,
                responseTime: DateTime.now(),
                updatedAt: DateTime.now(),
              );
            }
            return assignment;
          }).toList();
          
          return EventShift(
            id: shift.id,
            startTime: shift.startTime,
            endTime: shift.endTime,
            requiredGuards: shift.requiredGuards,
            requiredLicenses: shift.requiredLicenses,
            assignments: updatedAssignments,
            notes: shift.notes,
            createdAt: shift.createdAt,
            updatedAt: DateTime.now(),
          );
        }
        return shift;
      }).toList();

      await _firestore.collection('events').doc(eventId).update({
        'shifts': updatedShifts.map((shift) => shift.toFirestore()).toList(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error updating shift response: $e');
      throw Exception('Failed to update shift response: ${e.toString()}');
    }
  }

  /// Get shift history for a guard (completed shifts)
  Stream<List<GuardShiftInfo>> getShiftHistory(String guardId) {
    return getAssignedShifts(guardId).map((shifts) {
      final now = DateTime.now();
      return shifts.where((shiftInfo) =>
        shiftInfo.shift.endTime.isBefore(now) &&
        (shiftInfo.assignment.status == AssignmentStatus.confirmed ||
         shiftInfo.assignment.status == AssignmentStatus.interested ||
         shiftInfo.assignment.status == AssignmentStatus.completed)
      ).toList();
    });
  }

  /// Get guard statistics
  Future<GuardStats> getGuardStats(String guardId) async {
    try {
      final shifts = await getAssignedShifts(guardId).first;
      final now = DateTime.now();
      
      final completedShifts = shifts.where((shift) =>
        shift.shift.endTime.isBefore(now) &&
        (shift.assignment.status == AssignmentStatus.confirmed ||
         shift.assignment.status == AssignmentStatus.interested ||
         shift.assignment.status == AssignmentStatus.completed)
      ).toList();

      final upcomingShifts = shifts.where((shift) =>
        shift.shift.startTime.isAfter(now) &&
        (shift.assignment.status == AssignmentStatus.confirmed ||
         shift.assignment.status == AssignmentStatus.interested)
      ).toList();
      
      final pendingInvitations = shifts.where((shift) => 
        shift.assignment.status == AssignmentStatus.pending
      ).toList();
      
      double totalHours = 0;
      for (var shift in completedShifts) {
        final hours = shift.assignment.customHours ?? 
          shift.shift.endTime.difference(shift.shift.startTime).inMinutes / 60.0;
        totalHours += hours;
      }
      
      return GuardStats(
        totalCompletedShifts: completedShifts.length,
        totalHoursWorked: totalHours,
        upcomingShiftsCount: upcomingShifts.length,
        pendingInvitationsCount: pendingInvitations.length,
      );
    } catch (e) {
      debugPrint('Error getting guard stats: $e');
      throw Exception('Failed to get guard statistics: ${e.toString()}');
    }
  }
}

/// Combined information about a guard's shift assignment
class GuardShiftInfo {
  final EventModel event;
  final EventShift shift;
  final ShiftAssignment assignment;

  GuardShiftInfo({
    required this.event,
    required this.shift,
    required this.assignment,
  });

  /// Get the effective working hours (custom hours or calculated from time)
  double get effectiveHours {
    return assignment.customHours ?? 
      shift.endTime.difference(shift.startTime).inMinutes / 60.0;
  }

  /// Check if this shift is in the future
  bool get isUpcoming => shift.startTime.isAfter(DateTime.now());

  /// Check if this shift is currently ongoing
  bool get isOngoing {
    final now = DateTime.now();
    return shift.startTime.isBefore(now) && shift.endTime.isAfter(now);
  }

  /// Check if this shift is completed
  bool get isCompleted => shift.endTime.isBefore(DateTime.now());
}

/// Guard statistics model
class GuardStats {
  final int totalCompletedShifts;
  final double totalHoursWorked;
  final int upcomingShiftsCount;
  final int pendingInvitationsCount;

  GuardStats({
    required this.totalCompletedShifts,
    required this.totalHoursWorked,
    required this.upcomingShiftsCount,
    required this.pendingInvitationsCount,
  });

  @override
  String toString() {
    return 'GuardStats(completed: $totalCompletedShifts, hours: $totalHoursWorked, upcoming: $upcomingShiftsCount, pending: $pendingInvitationsCount)';
  }
}
