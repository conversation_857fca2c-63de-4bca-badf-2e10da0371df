import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/services/user_service.dart';
import '../../../../shared/models/event_model.dart';
import '../../../../shared/models/user_model.dart';
import '../../../../shared/models/event_shift_model.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../widgets/event_creation_dialog.dart';

import '../widgets/shift_duplication_dialog.dart';

// Helper class for conflict detection
class ConflictInfo {
  final String eventTitle;
  final String shiftTime;
  final String conflictType;

  ConflictInfo({
    required this.eventTitle,
    required this.shiftTime,
    required this.conflictType,
  });
}

class EventDetailPage extends ConsumerStatefulWidget {
  final String eventId;

  const EventDetailPage({
    super.key,
    required this.eventId,
  });

  @override
  ConsumerState<EventDetailPage> createState() => _EventDetailPageState();
}

class _EventDetailPageState extends ConsumerState<EventDetailPage> with TickerProviderStateMixin {
  late TabController _tabController;
  EventModel? _currentEvent;

  // Shift editing state (removed old complex editing system)



  // Guard assignment state
  List<UserModel> _availableGuards = [];
  List<UserModel> _filteredGuards = [];
  String _guardSearchQuery = '';
  String? _selectedLicenseFilter;
  EmploymentStatus? _selectedEmploymentFilter;
  bool _showOnlyAvailable = false;
  bool _loadingGuards = false;

  // Smart conflict filtering state for shifts
  Map<int, bool> _showAllGuardsForShift = {};

  // Helper method for Slovenian date formatting
  String _formatSlovenianDate(DateTime dateTime) {
    final months = [
      '', 'januar', 'februar', 'marec', 'april', 'maj', 'junij',
      'julij', 'avgust', 'september', 'oktober', 'november', 'december'
    ];

    return '${dateTime.day}. ${months[dateTime.month]}';
  }

  String _formatShiftTime(DateTime startTime, DateTime endTime) {
    final startDate = _formatSlovenianDate(startTime);
    final endDate = _formatSlovenianDate(endTime);
    final startTimeStr = DateFormat('HH:mm').format(startTime);
    final endTimeStr = DateFormat('HH:mm').format(endTime);

    // If same day, show: "6. junij 9:00 - 17:00"
    if (startTime.day == endTime.day && startTime.month == endTime.month && startTime.year == endTime.year) {
      return '$startDate $startTimeStr - $endTimeStr';
    }
    // If different days, show: "6. junij 9:00 - 7. junij 17:00"
    else {
      return '$startDate $startTimeStr - $endDate $endTimeStr';
    }
  }

  // Edit shift time method
  Future<void> _editShiftTime(EventModel event, int shiftIndex, EventShift shift) async {
    DateTime? newStartTime;
    DateTime? newEndTime;

    // Show date picker for start date
    final startDate = await showDatePicker(
      context: context,
      initialDate: shift.startTime,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (startDate == null) return;

    // Show time picker for start time
    final startTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(shift.startTime),
    );

    if (startTime == null) return;

    newStartTime = DateTime(
      startDate.year,
      startDate.month,
      startDate.day,
      startTime.hour,
      startTime.minute,
    );

    // Show time picker for end time
    final endTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(shift.endTime),
    );

    if (endTime == null) return;

    // Use same date as start for end time, unless end time is before start time
    DateTime endDate = startDate;
    if (endTime.hour < startTime.hour || (endTime.hour == startTime.hour && endTime.minute <= startTime.minute)) {
      // If end time is before start time, assume it's next day
      endDate = startDate.add(const Duration(days: 1));
    }

    newEndTime = DateTime(
      endDate.year,
      endDate.month,
      endDate.day,
      endTime.hour,
      endTime.minute,
    );

    // Update the shift
    try {
      final updatedShifts = List<EventShift>.from(event.shifts);
      final updatedShift = shift.copyWith(
        startTime: newStartTime,
        endTime: newEndTime,
        updatedAt: DateTime.now(),
      );
      updatedShifts[shiftIndex] = updatedShift;

      final updatedEvent = event.copyWith(shifts: updatedShifts);
      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      setState(() {
        _currentEvent = updatedEvent;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Čas izmene je bil uspešno posodobljen'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Napaka pri posodabljanju časa izmene'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);

    // Load guards for assignment
    _loadAvailableGuards();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<EventModel?>(
      future: ref.read(eventServiceProvider).getEventById(widget.eventId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return MainLayout(
            title: 'Podrobnosti dogodka',
            currentIndex: 1,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return MainLayout(
            title: 'Podrobnosti dogodka',
            currentIndex: 1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Dogodek ni bil najden',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.go('/events'),
                    child: Text('Nazaj na dogodke'),
                  ),
                ],
              ),
            ),
          );
        }

        final event = snapshot.data!;
        _currentEvent = event;

        return MainLayout(
          title: event.title,
          currentIndex: 1,
          actions: [
            // Confirm Event Button (only show for pending events)
            if (event.isPending)
              IconButton(
                icon: const Icon(Icons.check_circle),
                onPressed: () => _confirmEvent(context, event),
                tooltip: 'Potrdi dogodek in pošlji povabila',
                color: AppTheme.successColor,
              ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _showDeleteDialog(context, event),
              tooltip: 'Izbriši dogodek',
            ),
          ],
          child: _buildEventDetail(context, event),
        );
      },
    );
  }

  Widget _buildEventDetail(BuildContext context, EventModel event) {
    return Column(
      children: [
        // Back button
        Container(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => context.go('/events'),
              ),
              Text(
                'Nazaj na dogodke',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),

        // Tab Bar
        Container(
          color: Theme.of(context).colorScheme.surface,
          child: TabBar(
            controller: _tabController,
            tabs: [
              Tab(
                icon: const Icon(Icons.info),
                text: 'Podrobnosti',
              ),
              Tab(
                icon: const Icon(Icons.schedule),
                text: 'Izmene',
              ),
              Tab(
                icon: const Icon(Icons.people),
                text: 'Varnostniki',
              ),
              Tab(
                icon: const Icon(Icons.analytics),
                text: 'Analitika',
              ),
            ],
          ),
        ),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildEventInfoTab(context, event),
              _buildShiftsTab(context, event),
              _buildGuardsTab(context, event),
              _buildAnalyticsTab(context, event),
            ],
          ),
        ),
      ],
    );
  }

  // Tab Content Methods
  Widget _buildEventInfoTab(BuildContext context, EventModel event) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEventInfoCard(context, event),
          const SizedBox(height: 16),
          _buildLocationCard(context, event),
          const SizedBox(height: 16),
          _buildStatusCard(context, event),
          const SizedBox(height: 16),
          _buildEventTypeCard(context, event),
        ],
      ),
    );
  }

  Widget _buildShiftsTab(BuildContext context, EventModel event) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildShiftsHeader(context, event),
          const SizedBox(height: 16),
          // Guard filters for assignment
          _buildGuardFiltersCard(context),
          const SizedBox(height: 16),
          if (event.shifts.isEmpty)
            _buildNoShiftsCard(context, event)
          else
            ...event.shifts.asMap().entries.map((entry) =>
              _buildShiftCard(context, event, entry.value, entry.key)),
        ],
      ),
    );
  }

  Widget _buildGuardFiltersCard(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filtri za varnostnike',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),

            // Search bar
            TextField(
              decoration: InputDecoration(
                hintText: 'Iskanje varnostnikov...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _guardSearchQuery = value;
                });
                _applyGuardFilters();
              },
            ),
            const SizedBox(height: 12),

            // Filter chips
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('Polni delovni čas'),
                  selected: _selectedEmploymentFilter == EmploymentStatus.full_time,
                  onSelected: (selected) {
                    setState(() {
                      _selectedEmploymentFilter = selected ? EmploymentStatus.full_time : null;
                    });
                    _applyGuardFilters();
                  },
                ),
                FilterChip(
                  label: const Text('NPK licenca'),
                  selected: _selectedLicenseFilter == 'NPK_GOSTINSKI_LOKALI',
                  onSelected: (selected) {
                    setState(() {
                      _selectedLicenseFilter = selected ? 'NPK_GOSTINSKI_LOKALI' : null;
                    });
                    _applyGuardFilters();
                  },
                ),
                FilterChip(
                  label: const Text('DELO licenca'),
                  selected: _selectedLicenseFilter == 'DELO_NA_OBJEKTU',
                  onSelected: (selected) {
                    setState(() {
                      _selectedLicenseFilter = selected ? 'DELO_NA_OBJEKTU' : null;
                    });
                    _applyGuardFilters();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuardsTab(BuildContext context, EventModel event) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGuardsHeader(context, event),
          const SizedBox(height: 16),
          _buildAssignedGuardsCard(context, event),
          const SizedBox(height: 16),
          _buildAvailableGuardsCard(context, event),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab(BuildContext context, EventModel event) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFinancialCard(context, event),
          const SizedBox(height: 16),
          _buildEventStatsCard(context, event),
        ],
      ),
    );
  }

  // New Card Methods for Enhanced Event Detail
  Widget _buildEventTypeCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getEventTypeIcon(event.type),
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Tip dogodka',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.primaryColor.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getEventTypeIcon(event.type),
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _getEventTypeDisplayName(event.type),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Text(
                          _getEventTypeDescription(event.type),
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventInfoCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title Section
            Row(
              children: [
                Icon(
                  Icons.event,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    event.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            
            // Description Section
            const SizedBox(height: 16),
            if (event.description?.isNotEmpty == true) ...[
              Text(
                'Opis',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  event.description!,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Date and Time
            _buildInfoRow(
              context,
              icon: Icons.access_time,
              title: 'Začetni čas',
              value: DateFormat('EEEE, dd MMMM yyyy • HH:mm').format(event.startDateTime),
            ),

            const SizedBox(height: 12),

            _buildInfoRow(
              context,
              icon: Icons.schedule,
              title: 'Trajanje',
              value: '${(event.expectedDurationMinutes / 60).toStringAsFixed(1)} ur',
            ),

            if (event.endDateTime != null) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                context,
                icon: Icons.event_available,
                title: 'Končni čas',
                value: DateFormat('EEEE, dd MMMM yyyy • HH:mm').format(event.endDateTime!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusCard(BuildContext context, EventModel event) {
    final statusColor = _getStatusColor(event.status);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: statusColor.withOpacity(0.3),
                  width: 2,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    _getLocalizedStatus(context, event.status),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Quick Actions
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildStatusButton(
                  context,
                  'Potrdi',
                  Icons.check_circle,
                  AppTheme.successColor,
                  () => _updateStatus(event, 'confirmed'),
                ),
                _buildStatusButton(
                  context,
                  'Zaključi',
                  Icons.task_alt,
                  AppTheme.primaryColor,
                  () => _updateStatus(event, 'completed'),
                ),
                _buildStatusButton(
                  context,
                  'Prekliči',
                  Icons.cancel,
                  AppTheme.errorColor,
                  () => _updateStatus(event, 'cancelled'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildLocationCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Lokacija',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                event.location,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Map placeholder
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.map,
                      size: 32,
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Map integration coming soon',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignedGuardsCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Dodeljeni varnostniki',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${event.assignedGuardIds.length}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            if (event.assignedGuardIds.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.warningColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppTheme.warningColor.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning_amber,
                      color: AppTheme.warningColor,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Ni dodeljenih varnostnikov',
                        style: TextStyle(
                          color: AppTheme.warningColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              ...event.assignedGuardIds.map((guardId) =>
                _buildAssignedGuardItem(context, event, guardId)),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignedGuardItem(BuildContext context, EventModel event, String guardId) {
    return FutureBuilder<UserModel?>(
      future: ref.read(userServiceProvider).getUserById(guardId),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final guard = snapshot.data!;
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).dividerColor,
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                child: Icon(
                  Icons.person,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      guard.fullName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      guard.email,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              // Communication buttons
              if (guard.phone.isNotEmpty) ...[
                IconButton(
                  icon: Icon(
                    Icons.phone,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  onPressed: () => _callGuard(guard),
                  tooltip: 'Pokliči varnostnika',
                ),
                IconButton(
                  icon: Icon(
                    Icons.sms,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                  onPressed: () => _sendSMS(guard),
                  tooltip: 'Pošlji SMS',
                ),
              ],
              IconButton(
                icon: Icon(
                  Icons.remove_circle_outline,
                  color: AppTheme.errorColor,
                ),
                onPressed: () => _removeGuard(event, guardId),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAvailableGuardsCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with search and filters
            Row(
              children: [
                Icon(
                  Icons.person_add,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Razpoložljivi varnostniki',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.successColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_filteredGuards.where((guard) => !event.assignedGuardIds.contains(guard.id)).length}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.successColor,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Search bar
            TextField(
              decoration: InputDecoration(
                hintText: 'Iskanje varnostnikov...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _guardSearchQuery = value;
                });
                _applyGuardFilters();
              },
            ),

            const SizedBox(height: 12),

            // Filter chips
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('Polni delovni čas'),
                  selected: _selectedEmploymentFilter == EmploymentStatus.full_time,
                  onSelected: (selected) {
                    setState(() {
                      _selectedEmploymentFilter = selected ? EmploymentStatus.full_time : null;
                    });
                    _applyGuardFilters();
                  },
                ),
                FilterChip(
                  label: const Text('NPK licenca'),
                  selected: _selectedLicenseFilter == 'NPK_GOSTINSKI_LOKALI',
                  onSelected: (selected) {
                    setState(() {
                      _selectedLicenseFilter = selected ? 'NPK_GOSTINSKI_LOKALI' : null;
                    });
                    _applyGuardFilters();
                  },
                ),
                FilterChip(
                  label: const Text('DELO licenca'),
                  selected: _selectedLicenseFilter == 'DELO_NA_OBJEKTU',
                  onSelected: (selected) {
                    setState(() {
                      _selectedLicenseFilter = selected ? 'DELO_NA_OBJEKTU' : null;
                    });
                    _applyGuardFilters();
                  },
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Guards list
            if (_loadingGuards)
              const Center(child: CircularProgressIndicator())
            else if (_filteredGuards.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Ni varnostnikov, ki bi ustrezali filtrom',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              ...(_filteredGuards
                  .where((guard) => !event.assignedGuardIds.contains(guard.id))
                  .take(10)
                  .map((guard) => _buildAvailableGuardItem(context, event, guard))),

            if (_filteredGuards.where((guard) => !event.assignedGuardIds.contains(guard.id)).length > 10) ...[
              const SizedBox(height: 8),
              Center(
                child: Text(
                  'Showing 10 of ${_filteredGuards.where((guard) => !event.assignedGuardIds.contains(guard.id)).length} guards',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableGuardItem(BuildContext context, EventModel event, UserModel guard) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
            child: Icon(
              Icons.person,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  guard.fullName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  guard.email,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                if (guard.hasNpkLicense || guard.hasDeloNaObjektuLicense) ...[
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 4,
                    children: [
                      if (guard.hasNpkLicense)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'NPK',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                      if (guard.hasDeloNaObjektuLicense)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'OBJ',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),
          // Communication buttons
          if (guard.phone.isNotEmpty) ...[
            IconButton(
              icon: Icon(
                Icons.phone,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              onPressed: () => _callGuard(guard),
              tooltip: 'Pokliči varnostnika',
            ),
            IconButton(
              icon: Icon(
                Icons.sms,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              onPressed: () => _sendSMS(guard),
              tooltip: 'Pošlji SMS',
            ),
          ],
          IconButton(
            icon: Icon(
              Icons.add_circle_outline,
              color: AppTheme.successColor,
            ),
            onPressed: () => _assignGuardToEvent(event, guard.id),
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialCard(BuildContext context, EventModel event) {
    final totalEventHours = event.expectedDurationMinutes / 60;

    // Calculate total guard hours including custom hours
    double totalGuardHours = 0;
    for (final shift in event.shifts) {
      for (final assignment in shift.assignments) {
        if (assignment.status == AssignmentStatus.confirmed) {
          totalGuardHours += assignment.effectiveHours;
        }
      }
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.euro,
                  color: AppTheme.successColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Finančni pregled',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildFinancialRow(
              context,
              'Trajanje dogodka',
              '${totalEventHours.toStringAsFixed(1)} ur',
            ),

            const SizedBox(height: 12),

            _buildFinancialRow(
              context,
              'Dodeljenih varnostnikov',
              '${event.assignedGuardIds.length}',
            ),

            const SizedBox(height: 12),

            _buildFinancialRow(
              context,
              'Skupno ur varnostnikov',
              '${totalGuardHours.toStringAsFixed(1)} ur',
            ),

            const SizedBox(height: 12),

            const Divider(),

            const SizedBox(height: 12),

            _buildFinancialRow(
              context,
              'Skupno delovnih ur',
              '${totalGuardHours.toStringAsFixed(1)} ur',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: isTotal ? 18 : 16,
            color: isTotal ? AppTheme.successColor : null,
          ),
        ),
      ],
    );
  }

  // Shifts Tab Methods
  Widget _buildShiftsHeader(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          children: [
            Icon(
              Icons.schedule,
              color: AppTheme.primaryColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Izmene dogodka',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${event.shifts.length} ${event.shifts.length == 1 ? 'shift' : 'shifts'} configured',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),

          ],
        ),
      ),
    );
  }

  Widget _buildNoShiftsCard(BuildContext context, EventModel event) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(40.0),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.schedule_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Ni dodanih izmen',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),

            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShiftCard(BuildContext context, EventModel event, EventShift shift, int index) {
    final assignedGuards = shift.assignments.where((a) => a.status == AssignmentStatus.confirmed).toList();

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with shift number and actions
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Izmena ${index + 1}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
                const Spacer(),
                // Guard count indicator
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: assignedGuards.length >= shift.requiredGuards
                        ? Colors.green.withOpacity(0.1)
                        : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${assignedGuards.length}/${shift.requiredGuards}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: assignedGuards.length >= shift.requiredGuards
                          ? Colors.green[700]
                          : Colors.orange[700],
                    ),
                  ),
                ),

              ],
            ),
            const SizedBox(height: 16),

            // Shift Time and Details
            Row(
              children: [
                Icon(Icons.schedule, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () => _editShiftTime(event, index, shift),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: Colors.transparent),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Text(
                                  _formatShiftTime(shift.startTime, shift.endTime),
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              Icon(
                                Icons.edit,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                            ],
                          ),
                        ),
                      ),
                      Text(
                        'Potrebno varnostnikov: ${shift.requiredGuards}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Required licenses
            if (shift.requiredLicenses.isNotEmpty) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.verified_user, size: 16, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(
                    'Potrebne licence: ',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                  Expanded(
                    child: Wrap(
                      spacing: 4,
                      children: shift.requiredLicenses.map((license) =>
                        Chip(
                          label: Text(
                            license,
                            style: const TextStyle(fontSize: 10),
                          ),
                          backgroundColor: Colors.orange.withOpacity(0.1),
                          side: BorderSide(color: Colors.orange.withOpacity(0.3)),
                        ),
                      ).toList(),
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // Assigned guards
            _buildAssignedGuardsForShift(context, event, shift, index),

            const SizedBox(height: 16),

            // Available guards to add
            _buildAvailableGuardsForShift(context, event, shift, index),
          ],
        ),
      ),
    );
  }

  Widget _buildAssignedGuardsForShift(BuildContext context, EventModel event, EventShift shift, int shiftIndex) {
    // Show all assignments, not just confirmed ones
    final allAssignments = shift.assignments;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dodeljeni varnostniki (${allAssignments.length})',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        if (allAssignments.isEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Colors.grey.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outlined,
                  color: Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Ni dodeljenih varnostnikov',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: allAssignments.map((assignment) {
              final guard = _availableGuards.firstWhere(
                (g) => g.id == assignment.guardId,
                orElse: () => UserModel(
                  id: assignment.guardId,
                  firstName: 'Unknown',
                  lastName: 'Guard',
                  email: '',
                  phone: '',
                  role: 'guard',
                  createdAt: DateTime.now(),
                ),
              );

              // Get status color and text
              final statusInfo = _getAssignmentStatusInfo(assignment.status);

              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusInfo.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: statusInfo.color.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircleAvatar(
                          radius: 10,
                          backgroundColor: statusInfo.color.withOpacity(0.2),
                          child: Text(
                            guard.firstName[0].toUpperCase(),
                            style: TextStyle(
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                              color: statusInfo.color.shade700,
                            ),
                          ),
                        ),
                        const SizedBox(width: 6),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              guard.fullName,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                                color: statusInfo.color.shade700,
                              ),
                            ),
                            Text(
                              statusInfo.text,
                              style: TextStyle(
                                fontSize: 10,
                                color: statusInfo.color.shade600,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(width: 4),
                        // Clock icon for custom hours
                        GestureDetector(
                          onTap: () => _showCustomHoursDialog(context, event, assignment, shiftIndex),
                          child: Icon(
                            Icons.access_time,
                            size: 14,
                            color: assignment.hasCustomHours ? AppTheme.primaryColor : Colors.green[700],
                          ),
                        ),
                        const SizedBox(width: 4),
                        GestureDetector(
                          onTap: () => _removeGuardFromShift(event, assignment.guardId, shiftIndex),
                          child: Icon(
                            Icons.close,
                            size: 14,
                            color: Colors.green[700],
                          ),
                        ),
                      ],
                    ),
                    // Show custom hours if set
                    if (assignment.hasCustomHours) ...[
                      const SizedBox(height: 2),
                      Text(
                        'Delovne ure: ${assignment.customHours!.toStringAsFixed(1)}h',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ],
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildAvailableGuardsForShift(BuildContext context, EventModel event, EventShift shift, int shiftIndex) {
    final assignedGuardIds = shift.assignments.map((a) => a.guardId).toSet();

    // Get guards filtered for this specific shift (hides conflicts by default)
    final shiftFilteredGuards = _getFilteredGuardsForShift(event, shiftIndex);

    // Filter guards that are not already assigned to this shift
    final availableGuards = shiftFilteredGuards.where((guard) =>
      !assignedGuardIds.contains(guard.id)
    ).toList();

    // Filter by required licenses
    final qualifiedGuards = availableGuards.where((guard) {
      for (String requiredLicense in shift.requiredLicenses) {
        if (requiredLicense == 'NPK GOSTINSKI LOKALI' && !guard.hasNpkLicense) {
          return false;
        } else if (requiredLicense == 'DELO NA OBJEKTU' && !guard.hasDeloNaObjektuLicense) {
          return false;
        }
      }
      return true;
    }).toList();

    // Count total guards (including conflicted ones) for the toggle
    final totalAvailableGuards = _filteredGuards.where((guard) =>
      !assignedGuardIds.contains(guard.id)
    ).length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with toggle
        Row(
          children: [
            Expanded(
              child: Text(
                'Razpoložljivi varnostniki (${qualifiedGuards.length})',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (totalAvailableGuards > qualifiedGuards.length)
              Row(
                children: [
                  Text(
                    'Prikaži vse',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 4),
                  Switch(
                    value: _showAllGuardsForShift[shiftIndex] ?? false,
                    onChanged: (value) {
                      setState(() {
                        _showAllGuardsForShift[shiftIndex] = value;
                      });
                    },
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ],
              ),
          ],
        ),
        const SizedBox(height: 8),
        if (qualifiedGuards.isEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Colors.orange.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_outlined,
                  color: Colors.orange[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    (_showAllGuardsForShift[shiftIndex] ?? false)
                        ? 'Ni razpoložljivih varnostnikov z ustreznimi licencami'
                        : 'Ni razpoložljivih varnostnikov brez konfliktov. Vklopite "Prikaži vse" za več možnosti.',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: qualifiedGuards.take(10).map((guard) { // Show max 10 guards
              final hasConflict = _guardHasConflictsForShift(event, guard.id, shiftIndex);

              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: hasConflict
                      ? Colors.orange.withOpacity(0.1)
                      : Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: hasConflict
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.blue.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 10,
                      backgroundColor: hasConflict
                          ? Colors.orange.withOpacity(0.2)
                          : Colors.blue.withOpacity(0.2),
                      child: Text(
                        guard.firstName[0].toUpperCase(),
                        style: TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                          color: hasConflict ? Colors.orange[700] : Colors.blue[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    if (hasConflict) ...[
                      Icon(
                        Icons.warning,
                        size: 12,
                        color: Colors.orange[700],
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(
                      guard.fullName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: hasConflict ? Colors.orange[700] : Colors.blue[700],
                      ),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () => _assignGuardToShiftDirect(event, guard.id, shiftIndex),
                      child: Icon(
                        Icons.add,
                        size: 14,
                        color: hasConflict ? Colors.orange[700] : Colors.blue[700],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  // Guards Tab Methods
  Widget _buildGuardsHeader(BuildContext context, EventModel event) {
    final totalAssigned = event.assignedGuardIds.length;
    final totalRequired = event.shifts.fold<int>(0, (sum, shift) => sum + shift.requiredGuards);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Row(
          children: [
            Icon(
              Icons.people,
              color: AppTheme.primaryColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Dodeljevanje varnostnikov',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Dodeljenih $totalAssigned od $totalRequired varnostnikov',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: totalAssigned >= totalRequired
                    ? AppTheme.successColor.withOpacity(0.1)
                    : AppTheme.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                totalAssigned >= totalRequired ? 'Popolnoma zasedeno' : 'Premalo osebja',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: totalAssigned >= totalRequired
                      ? AppTheme.successColor
                      : AppTheme.warningColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventStatsCard(BuildContext context, EventModel event) {
    final totalShifts = event.shifts.length;
    final totalHours = event.shifts.fold<double>(0, (sum, shift) =>
        sum + shift.endTime.difference(shift.startTime).inMinutes / 60);

    // Calculate total guard hours including custom hours
    double totalGuardHours = 0;
    for (final shift in event.shifts) {
      for (final assignment in shift.assignments) {
        if (assignment.status == AssignmentStatus.confirmed) {
          totalGuardHours += assignment.effectiveHours;
        }
      }
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Statistike dogodka',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(context, 'Skupno izmen', totalShifts.toString(), Icons.schedule),
                ),
                Expanded(
                  child: _buildStatItem(context, 'Ur dogodka', '${totalHours.toStringAsFixed(1)}h', Icons.access_time),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(context, 'Dodeljenih varnostnikov', event.assignedGuardIds.length.toString(), Icons.people),
                ),
                Expanded(
                  child: _buildStatItem(context, 'Skupno ur varnostnikov', '${totalGuardHours.toStringAsFixed(1)}h', Icons.work),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).dividerColor),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppTheme.primaryColor),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper Methods
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'confirmed':
        return AppTheme.successColor;
      case 'pending':
        return AppTheme.warningColor;
      case 'completed':
        return AppTheme.primaryColor;
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return Colors.grey;
    }
  }

  String _getLocalizedStatus(BuildContext context, String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return 'Aktiven';
      case 'pending':
        return 'V čakanju';
      case 'confirmed':
        return 'Potrjen';
      case 'completed':
        return 'Zaključen';
      case 'cancelled':
        return 'Preklican';
      default:
        return status;
    }
  }

  // Event Type Helper Methods
  IconData _getEventTypeIcon(EventType type) {
    switch (type) {
      case EventType.venue:
        return Icons.store;
      case EventType.event:
        return Icons.event;
      case EventType.local:
        return Icons.location_city;
    }
  }

  String _getEventTypeDisplayName(EventType type) {
    switch (type) {
      case EventType.venue:
        return 'Varovanje objekta';
      case EventType.event:
        return 'Varovanje dogodka';
      case EventType.local:
        return 'Varovanje lokala';
    }
  }

  String _getEventTypeDescription(EventType type) {
    switch (type) {
      case EventType.venue:
        return 'Potrebna DELO NA OBJEKTU licenca';
      case EventType.event:
        return 'Ni potrebnih posebnih licenc';
      case EventType.local:
        return 'Potrebna NPK GOSTINSKI LOKALI licenca';
    }
  }

  // Guard Management Methods
  Future<void> _loadAvailableGuards() async {
    setState(() {
      _loadingGuards = true;
    });

    try {
      final userService = ref.read(userServiceProvider);
      final guardsStream = userService.getGuards();
      final guards = await guardsStream.first;

      setState(() {
        _availableGuards = guards.where((guard) => guard.isActive).toList();
        _applyGuardFilters();
        _loadingGuards = false;
      });
    } catch (e) {
      setState(() {
        _availableGuards = [];
        _filteredGuards = [];
        _loadingGuards = false;
      });
    }
  }

  void _applyGuardFilters() {
    setState(() {
      _filteredGuards = _availableGuards.where((guard) {
        // Search filter
        if (_guardSearchQuery.isNotEmpty) {
          final query = _guardSearchQuery.toLowerCase();
          if (!guard.fullName.toLowerCase().contains(query) &&
              !guard.email.toLowerCase().contains(query)) {
            return false;
          }
        }

        // License filter (enhanced for Slovenian licenses)
        if (_selectedLicenseFilter != null) {
          bool hasLicense = false;

          // Check Slovenian licenses
          if (_selectedLicenseFilter == 'NPK_GOSTINSKI_LOKALI') {
            hasLicense = guard.hasNpkLicense;
          } else if (_selectedLicenseFilter == 'DELO_NA_OBJEKTU') {
            hasLicense = guard.hasDeloNaObjektuLicense;
          } else {
            // Check legacy license IDs
            hasLicense = guard.licenseIds.contains(_selectedLicenseFilter);
          }

          if (!hasLicense) {
            return false;
          }
        }

        // Employment status filter
        if (_selectedEmploymentFilter != null) {
          if (guard.employmentStatus != _selectedEmploymentFilter) {
            return false;
          }
        }

        // Availability filter (placeholder - would need availability data)
        if (_showOnlyAvailable) {
          // TODO: Implement availability checking
        }

        return true;
      }).toList();
    });
  }

  // Smart conflict filtering methods
  bool _guardHasConflictsForShift(EventModel event, String guardId, int targetShiftIndex) {
    if (targetShiftIndex >= event.shifts.length) return false;

    final targetShift = event.shifts[targetShiftIndex];

    // Check conflicts with other shifts in this event
    for (int i = 0; i < event.shifts.length; i++) {
      if (i == targetShiftIndex) continue; // Skip the target shift

      final otherShift = event.shifts[i];

      // Check if guard is already assigned to this shift
      final hasAssignment = otherShift.assignments.any((assignment) =>
          assignment.guardId == guardId &&
          assignment.status == AssignmentStatus.confirmed);

      if (hasAssignment && _hasTimeOverlap(
          targetShift.startTime, targetShift.endTime,
          otherShift.startTime, otherShift.endTime)) {
        return true;
      }
    }

    return false;
  }

  bool _hasTimeOverlap(DateTime start1, DateTime end1, DateTime start2, DateTime end2) {
    return start1.isBefore(end2) && end1.isAfter(start2);
  }

  // Get filtered guards for a specific shift (used in shift assignment UI)
  List<UserModel> _getFilteredGuardsForShift(EventModel event, int shiftIndex) {
    final showAll = _showAllGuardsForShift[shiftIndex] ?? false;

    if (showAll) {
      return _filteredGuards; // Show all guards when toggle is enabled
    }

    // Hide guards that have conflicts with this shift
    return _filteredGuards.where((guard) {
      return !_guardHasConflictsForShift(event, guard.id, shiftIndex);
    }).toList();
  }

  void _assignGuardToEvent(EventModel event, String guardId) async {
    // Show shift selection dialog for proper assignment
    await _showShiftSelectionDialog(event, guardId);
  }

  // Direct assignment method for inline UI
  Future<void> _assignGuardToShiftDirect(EventModel event, String guardId, int shiftIndex) async {
    try {
      final shift = event.shifts[shiftIndex];
      final updatedShifts = List<EventShift>.from(event.shifts);

      // Create new shift assignment
      final newAssignment = ShiftAssignment(
        guardId: guardId,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: AssignmentStatus.pending, // Start as pending invitation
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Add assignment to shift
      final updatedAssignments = List<ShiftAssignment>.from(shift.assignments);
      updatedAssignments.add(newAssignment);

      updatedShifts[shiftIndex] = shift.copyWith(assignments: updatedAssignments);

      // Update assigned guard IDs for the event
      final allAssignedGuards = <String>{};
      for (final shift in updatedShifts) {
        for (final assignment in shift.assignments) {
          if (assignment.status == AssignmentStatus.confirmed) {
            allAssignedGuards.add(assignment.guardId);
          }
        }
      }

      final updatedEvent = event.copyWith(
        shifts: updatedShifts,
        assignedGuardIds: allAssignedGuards.toList(),
      );

      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      setState(() {
        _currentEvent = updatedEvent;
      });

      if (mounted) {
        final guard = _availableGuards.firstWhere((g) => g.id == guardId);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${guard.fullName} je bil dodeljen izmeni ${shiftIndex + 1}'),
            backgroundColor: AppTheme.successColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Napaka pri dodeljevanju varnostnika: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  // Remove guard from shift
  Future<void> _removeGuardFromShift(EventModel event, String guardId, int shiftIndex) async {
    try {
      final shift = event.shifts[shiftIndex];
      final updatedShifts = List<EventShift>.from(event.shifts);

      // Remove assignment from shift
      final updatedAssignments = shift.assignments
          .where((assignment) => assignment.guardId != guardId)
          .toList();

      updatedShifts[shiftIndex] = shift.copyWith(assignments: updatedAssignments);

      // Update assigned guard IDs for the event
      final allAssignedGuards = <String>{};
      for (final shift in updatedShifts) {
        for (final assignment in shift.assignments) {
          if (assignment.status == AssignmentStatus.confirmed) {
            allAssignedGuards.add(assignment.guardId);
          }
        }
      }

      final updatedEvent = event.copyWith(
        shifts: updatedShifts,
        assignedGuardIds: allAssignedGuards.toList(),
      );

      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      setState(() {
        _currentEvent = updatedEvent;
      });

      if (mounted) {
        final guard = _availableGuards.firstWhere((g) => g.id == guardId);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${guard.fullName} je bil odstranjen iz izmene ${shiftIndex + 1}'),
            backgroundColor: AppTheme.warningColor,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Napaka pri odstranjevanju varnostnika: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  Future<void> _showShiftSelectionDialog(EventModel event, String guardId) async {
    final guard = _availableGuards.firstWhere((g) => g.id == guardId);

    final selectedShiftIndex = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Assign ${guard.fullName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Select which shift to assign this guard to:'),
            const SizedBox(height: 16),
            ...event.shifts.asMap().entries.map((entry) {
              final index = entry.key;
              final shift = entry.value;
              final conflicts = _checkGuardConflicts(guardId, shift);

              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  title: Text('Shift ${index + 1}'),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${DateFormat('HH:mm').format(shift.startTime)} - ${DateFormat('HH:mm').format(shift.endTime)}'),
                      if (conflicts.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Conflicts: ${conflicts.length} overlapping assignments',
                          style: TextStyle(color: AppTheme.errorColor, fontSize: 12),
                        ),
                      ],
                    ],
                  ),
                  trailing: conflicts.isNotEmpty
                      ? Icon(Icons.warning, color: AppTheme.errorColor)
                      : Icon(Icons.check_circle, color: AppTheme.successColor),
                  onTap: conflicts.isEmpty
                      ? () => Navigator.of(context).pop(index)
                      : () => _showConflictDialog(context, guard, shift, conflicts),
                ),
              );
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Prekliči'),
          ),
        ],
      ),
    );

    if (selectedShiftIndex != null) {
      await _assignGuardToShift(event, guardId, selectedShiftIndex);
    }
  }

  Future<void> _assignGuardToShift(EventModel event, String guardId, int shiftIndex) async {
    try {
      final shift = event.shifts[shiftIndex];
      final updatedShifts = List<EventShift>.from(event.shifts);

      // Create new shift assignment
      final newAssignment = ShiftAssignment(
        guardId: guardId,
        startTime: shift.startTime,
        endTime: shift.endTime,
        status: AssignmentStatus.pending, // Start as pending invitation
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Add assignment to shift
      final updatedAssignments = List<ShiftAssignment>.from(shift.assignments);
      updatedAssignments.add(newAssignment);

      updatedShifts[shiftIndex] = shift.copyWith(assignments: updatedAssignments);

      // Update assigned guard IDs for the event
      final allAssignedGuards = <String>{};
      for (final shift in updatedShifts) {
        for (final assignment in shift.assignments) {
          if (assignment.status == AssignmentStatus.confirmed) {
            allAssignedGuards.add(assignment.guardId);
          }
        }
      }

      final updatedEvent = event.copyWith(
        shifts: updatedShifts,
        assignedGuardIds: allAssignedGuards.toList(),
      );

      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      setState(() {
        _currentEvent = updatedEvent;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Guard assigned to shift ${shiftIndex + 1}'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Napaka pri posodabljanju dogodka'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  // Conflict Detection Methods
  List<ConflictInfo> _checkGuardConflicts(String guardId, EventShift shift) {
    final conflicts = <ConflictInfo>[];

    // Check conflicts within current event
    for (int i = 0; i < _currentEvent!.shifts.length; i++) {
      final otherShift = _currentEvent!.shifts[i];
      if (otherShift.id == shift.id) continue; // Skip same shift

      for (final assignment in otherShift.assignments) {
        if (assignment.guardId == guardId &&
            assignment.status == AssignmentStatus.confirmed &&
            _hasTimeOverlap(shift.startTime, shift.endTime, assignment.startTime, assignment.endTime)) {
          conflicts.add(ConflictInfo(
            eventTitle: _currentEvent!.title,
            shiftTime: '${DateFormat('HH:mm').format(otherShift.startTime)} - ${DateFormat('HH:mm').format(otherShift.endTime)}',
            conflictType: 'Same Event',
          ));
        }
      }
    }

    // TODO: Check conflicts with other events (would need to query all events)
    // This would require a more comprehensive conflict detection service

    return conflicts;
  }

  void _showConflictDialog(BuildContext context, UserModel guard, EventShift shift, List<ConflictInfo> conflicts) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: AppTheme.errorColor),
            const SizedBox(width: 8),
            Text('Schedule Conflict'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${guard.fullName} has conflicting assignments:'),
            const SizedBox(height: 12),
            ...conflicts.map((conflict) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          conflict.eventTitle,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          '${conflict.conflictType}: ${conflict.shiftTime}',
                          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )).toList(),
            const SizedBox(height: 12),
            Text(
              'Assigning this guard will create a double-booking. Do you want to proceed anyway?',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocale.cancel.getString(context)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Force assign despite conflicts
              _assignGuardToShift(_currentEvent!, guard.id,
                _currentEvent!.shifts.indexOf(shift));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: Text('Assign Anyway'),
          ),
        ],
      ),
    );
  }

  // Custom Hours Dialog
  Future<void> _showCustomHoursDialog(BuildContext context, EventModel event, ShiftAssignment assignment, int shiftIndex) async {
    final TextEditingController hoursController = TextEditingController();
    final TextEditingController noteController = TextEditingController();

    // Initialize with current values
    final currentHours = assignment.customHours ?? assignment.effectiveHours;
    hoursController.text = currentHours.toStringAsFixed(1);
    noteController.text = assignment.note ?? '';

    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.access_time, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text('Prilagodi delovne ure'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Guard info
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
                      child: Text(
                        _availableGuards.firstWhere((g) => g.id == assignment.guardId).firstName[0].toUpperCase(),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _availableGuards.firstWhere((g) => g.id == assignment.guardId).fullName,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            'Privzete ure izmene: ${assignment.endTime.difference(assignment.startTime).inMinutes / 60.0}h',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Hours input
              TextField(
                controller: hoursController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  labelText: 'Delovne ure',
                  hintText: 'Vnesite število ur (npr. 6.5)',
                  prefixIcon: Icon(Icons.access_time),
                  border: OutlineInputBorder(),
                  suffixText: 'ur',
                ),
              ),
              const SizedBox(height: 16),

              // Note input
              TextField(
                controller: noteController,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Opomba (neobvezno)',
                  hintText: 'Dodajte opombo o prilagoditvi ur...',
                  prefixIcon: Icon(Icons.note),
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Prekliči'),
          ),
          if (assignment.hasCustomHours)
            TextButton(
              onPressed: () => Navigator.of(context).pop({
                'hours': null, // Reset to default
                'note': noteController.text.trim().isEmpty ? null : noteController.text.trim(),
              }),
              child: Text('Ponastavi na privzeto'),
            ),
          ElevatedButton(
            onPressed: () {
              final hours = double.tryParse(hoursController.text);
              if (hours != null && hours > 0 && hours <= 24) {
                Navigator.of(context).pop({
                  'hours': hours,
                  'note': noteController.text.trim().isEmpty ? null : noteController.text.trim(),
                });
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Vnesite veljavno število ur (0-24)'),
                    backgroundColor: AppTheme.errorColor,
                  ),
                );
              }
            },
            child: Text('Shrani'),
          ),
        ],
      ),
    );

    if (result != null) {
      await _updateAssignmentCustomHours(event, assignment, shiftIndex, result['hours'], result['note']);
    }

    hoursController.dispose();
    noteController.dispose();
  }

  // Update assignment custom hours
  Future<void> _updateAssignmentCustomHours(EventModel event, ShiftAssignment assignment, int shiftIndex, double? customHours, String? note) async {
    try {
      final shift = event.shifts[shiftIndex];
      final updatedShifts = List<EventShift>.from(event.shifts);

      // Find and update the assignment
      final assignmentIndex = shift.assignments.indexWhere((a) => a.guardId == assignment.guardId);
      if (assignmentIndex != -1) {
        final updatedAssignments = List<ShiftAssignment>.from(shift.assignments);
        updatedAssignments[assignmentIndex] = assignment.copyWith(
          customHours: customHours,
          note: note,
          updatedAt: DateTime.now(),
        );

        updatedShifts[shiftIndex] = shift.copyWith(
          assignments: updatedAssignments,
          updatedAt: DateTime.now(),
        );

        final updatedEvent = event.copyWith(
          shifts: updatedShifts,
          updatedAt: DateTime.now(),
        );

        await ref.read(eventServiceProvider).updateEvent(updatedEvent);

        setState(() {
          _currentEvent = updatedEvent;
        });

        if (mounted) {
          final guard = _availableGuards.firstWhere((g) => g.id == assignment.guardId);
          final message = customHours != null
              ? 'Delovne ure za ${guard.fullName} so bile nastavljene na ${customHours.toStringAsFixed(1)}h'
              : 'Delovne ure za ${guard.fullName} so bile ponastavljene na privzeto';

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(message),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Napaka pri posodabljanju delovnih ur: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  // Communication Methods
  Future<void> _callGuard(UserModel guard) async {
    if (guard.phone.isNotEmpty) {
      final phoneUrl = Uri.parse('tel:${guard.phone}');
      try {
        if (await canLaunchUrl(phoneUrl)) {
          await launchUrl(phoneUrl);
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Cannot make phone calls on this device'),
                backgroundColor: AppTheme.warningColor,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error making call: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No phone number available for ${guard.fullName}'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
      }
    }
  }

  Future<void> _sendSMS(UserModel guard) async {
    if (guard.phone.isNotEmpty) {
      final smsUrl = Uri.parse('sms:${guard.phone}');
      try {
        if (await canLaunchUrl(smsUrl)) {
          await launchUrl(smsUrl);
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Cannot send SMS on this device'),
                backgroundColor: AppTheme.warningColor,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error sending SMS: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No phone number available for ${guard.fullName}'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
      }
    }
  }





  // Action Methods



  void _duplicateShift(EventModel event, EventShift shift, int shiftIndex) async {
    final duplicatedShifts = await showDialog<List<EventShift>>(
      context: context,
      builder: (context) => ShiftDuplicationDialog(
        sourceShift: shift,
        sourceShiftIndex: shiftIndex,
      ),
    );

    if (duplicatedShifts != null && duplicatedShifts.isNotEmpty) {
      final updatedShifts = List<EventShift>.from(event.shifts)
        ..addAll(duplicatedShifts);

      final updatedEvent = event.copyWith(
        shifts: updatedShifts,
        updatedAt: DateTime.now(),
      );

      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      if (mounted) {
        setState(() {
          _currentEvent = updatedEvent;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${duplicatedShifts.length} shifts created successfully'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  void _deleteShift(EventModel event, int shiftIndex) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Izbriši izmeno'),
        content: Text('Ali ste prepričani, da želite izbrisati to izmeno?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Prekliči'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: Text('Izbriši'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final updatedShifts = List<EventShift>.from(event.shifts)..removeAt(shiftIndex);
      final updatedEvent = event.copyWith(shifts: updatedShifts);
      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      if (mounted) {
        setState(() {
          _currentEvent = updatedEvent;
        });
      }
    }
  }

  ({Color color, String text}) _getAssignmentStatusInfo(AssignmentStatus status) {
    switch (status) {
      case AssignmentStatus.pending:
        return (color: Colors.orange, text: 'Čaka odziv');
      case AssignmentStatus.interested:
        return (color: Colors.green, text: 'Sprejel');
      case AssignmentStatus.confirmed:
        return (color: Colors.blue, text: 'Potrjen');
      case AssignmentStatus.unavailable:
        return (color: Colors.red, text: 'Zavrnil');
      case AssignmentStatus.completed:
        return (color: Colors.purple, text: 'Končano');
      case AssignmentStatus.cancelled:
        return (color: Colors.grey, text: 'Preklicano');
    }
  }

  Future<void> _confirmEvent(BuildContext context, EventModel event) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Potrdi dogodek'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Ali ste prepričani, da želite potrditi ta dogodek?'),
            const SizedBox(height: 16),
            Text(
              'To bo poslalo povabila vsem dodeljenim varnostnikom.',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Prekliči'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.successColor,
              foregroundColor: Colors.white,
            ),
            child: Text('Potrdi in pošlji povabila'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Create assignments for all assigned guards in all shifts
        final updatedShifts = <EventShift>[];

        for (final shift in event.shifts) {
          final assignments = <ShiftAssignment>[];

          // Create assignments for guards assigned to this event
          for (final guardId in event.assignedGuardIds) {
            // Check if assignment already exists for this guard in this shift
            final existingAssignment = shift.assignments.firstWhere(
              (assignment) => assignment.guardId == guardId,
              orElse: () => ShiftAssignment(
                guardId: '',
                startTime: DateTime.now(),
                endTime: DateTime.now(),
                status: AssignmentStatus.pending,
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ),
            );

            if (existingAssignment.guardId.isEmpty) {
              // Create new assignment
              assignments.add(ShiftAssignment(
                guardId: guardId,
                startTime: shift.startTime,
                endTime: shift.endTime,
                status: AssignmentStatus.pending, // Guards need to respond
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              ));
            } else {
              // Keep existing assignment
              assignments.add(existingAssignment);
            }
          }

          // Update shift with assignments
          updatedShifts.add(shift.copyWith(
            assignments: assignments,
            updatedAt: DateTime.now(),
          ));
        }

        // Update event status to confirmed and add assignments
        final updatedEvent = event.copyWith(
          status: 'confirmed',
          shifts: updatedShifts,
          updatedAt: DateTime.now(),
        );

        await ref.read(eventServiceProvider).updateEvent(updatedEvent);

        setState(() {
          _currentEvent = updatedEvent;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Dogodek je bil potrjen in povabila so bila poslana!'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Napaka pri potrjevanju dogodka: $e'),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      }
    }
  }

  void _showDeleteDialog(BuildContext context, EventModel event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Izbriši dogodek'),
        content: Text('Ali ste prepričani, da želite izbrisati ta dogodek? Te akcije ni mogoče razveljaviti.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Prekliči'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(eventServiceProvider).deleteEvent(event.id);
              if (context.mounted) {
                context.go('/events');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: Text('Izbriši'),
          ),
        ],
      ),
    );
  }

  void _updateStatus(EventModel event, String newStatus) async {
    try {
      final updatedEvent = event.copyWith(status: newStatus);
      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      setState(() {
        _currentEvent = updatedEvent;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Dogodek je bil posodobljen'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Napaka pri posodabljanju dogodka'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  void _removeGuard(EventModel event, String guardId) async {
    try {
      final updatedShifts = <EventShift>[];

      // Remove guard from all shifts
      for (final shift in event.shifts) {
        final updatedAssignments = shift.assignments
            .where((assignment) => assignment.guardId != guardId)
            .toList();
        updatedShifts.add(shift.copyWith(assignments: updatedAssignments));
      }

      // Update assigned guard IDs for the event
      final allAssignedGuards = <String>{};
      for (final shift in updatedShifts) {
        for (final assignment in shift.assignments) {
          if (assignment.status == AssignmentStatus.confirmed) {
            allAssignedGuards.add(assignment.guardId);
          }
        }
      }

      final updatedEvent = event.copyWith(
        shifts: updatedShifts,
        assignedGuardIds: allAssignedGuards.toList(),
      );

      await ref.read(eventServiceProvider).updateEvent(updatedEvent);

      setState(() {
        _currentEvent = updatedEvent;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Varnostnik je bil odstranjen iz vseh izmen'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Napaka pri posodabljanju dogodka'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }


}
