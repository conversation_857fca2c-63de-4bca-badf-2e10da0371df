import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/models/event_model.dart';
import '../widgets/event_creation_dialog.dart' hide eventServiceProvider;

class EventsPage extends ConsumerStatefulWidget {
  const EventsPage({super.key});

  @override
  ConsumerState<EventsPage> createState() => _EventsPageState();
}

class _EventsPageState extends ConsumerState<EventsPage> {
  String _selectedFilter = 'all';
  final List<String> _filterOptions = ['all', 'pending', 'active', 'completed', 'cancelled'];

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: AppLocale.eventsManagement.getString(context),
      currentIndex: 1,
      actions: [
        // Filter Dropdown
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: DropdownButton<String>(
            value: _selectedFilter,
            icon: const Icon(Icons.filter_list, color: Colors.white),
            dropdownColor: AppTheme.primaryColor,
            style: const TextStyle(color: Colors.white),
            underline: Container(),
            onChanged: (String? newValue) {
              if (newValue != null) {
                setState(() {
                  _selectedFilter = newValue;
                });
              }
            },
            items: _filterOptions.map<DropdownMenuItem<String>>((String value) {
              String displayText;
              switch (value) {
                case 'all':
                  displayText = AppLocale.all.getString(context);
                  break;
                case 'pending':
                  displayText = AppLocale.pending.getString(context);
                  break;
                case 'active':
                  displayText = AppLocale.active.getString(context);
                  break;
                case 'completed':
                  displayText = AppLocale.completed.getString(context);
                  break;
                case 'cancelled':
                  displayText = AppLocale.cancelled.getString(context);
                  break;
                default:
                  displayText = value;
              }
              return DropdownMenuItem<String>(
                value: value,
                child: Text(
                  displayText.toUpperCase(),
                  style: const TextStyle(color: Colors.white),
                ),
              );
            }).toList(),
          ),
        ),
        // Add Event Button
        IconButton(
          icon: const Icon(Icons.add),
          onPressed: () => _showCreateEventDialog(context),
        ),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateEventDialog(context),
        child: const Icon(Icons.add),
      ),
      child: _buildEventsContent(context),
    );
  }

  Widget _buildEventsContent(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1024;
    final isTablet = screenSize.width > 768 && screenSize.width <= 1024;

    int crossAxisCount;
    if (isDesktop) {
      crossAxisCount = 3;
    } else if (isTablet) {
      crossAxisCount = 2;
    } else {
      crossAxisCount = 1;
    }
    return StreamBuilder<List<EventModel>>(
      stream: _selectedFilter == 'all'
          ? ref.read(eventServiceProvider).getEvents()
          : ref.read(eventServiceProvider).getEventsByStatus(_selectedFilter),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: AppTheme.errorColor,
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocale.errorLoadingEvents.getString(context),
                  style: TextStyle(
                    fontSize: 18,
                    color: AppTheme.errorColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  snapshot.error.toString(),
                  style: const TextStyle(color: Color(0xFF424242)),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        final events = snapshot.data ?? [];

        if (events.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.event_busy,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocale.noEventsFound.getString(context),
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _selectedFilter == 'all'
                      ? AppLocale.createFirstEvent.getString(context)
                      : AppLocale.noFilteredEvents.getString(context).replaceAll('{status}', _selectedFilter),
                  style: TextStyle(color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton.icon(
                  onPressed: () => _showCreateEventDialog(context),
                  icon: const Icon(Icons.add),
                  label: Text(AppLocale.createEvent.getString(context)),
                ),
              ],
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: events.length,
            itemBuilder: (context, index) {
              final event = events[index];
              return _buildEventCard(context, event);
            },
          ),
        );
      },
    );
  }

  Widget _buildEventCard(BuildContext context, EventModel event) {
    final statusColor = _getStatusColor(event.status);

    return Card(
      elevation: 3,
      child: InkWell(
        onTap: () => _showEventDetails(context, event),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: statusColor.withOpacity(0.3),
              width: 2,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      event.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).textTheme.titleLarge?.color,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      event.status.toUpperCase(),
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: statusColor,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Location
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7)),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      event.location,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Date and time
              Row(
                children: [
                  Icon(Icons.access_time, size: 16, color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7)),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      _formatDateTime(event.startDateTime),
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Duration
              Row(
                children: [
                  Icon(Icons.timer, size: 16, color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7)),
                  const SizedBox(width: 4),
                  Text(
                    '${event.expectedDurationMinutes ~/ 60}h ${event.expectedDurationMinutes % 60}m',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
                    ),
                  ),
                ],
              ),

              const Spacer(),

              // Guards assigned
              Row(
                children: [
                  Icon(Icons.people, size: 16, color: AppTheme.primaryColor),
                  const SizedBox(width: 4),
                  Text(
                    AppLocale.guardsAssigned.getString(context).replaceAll('{count}', event.assignedGuardIds.length.toString()),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).textTheme.bodyLarge?.color,
                    ),
                  ),
                  const Spacer(),
                  // Hourly rate
                  Text(
                    '${event.expectedDurationMinutes ~/ 60}h ${event.expectedDurationMinutes % 60}m',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return AppTheme.successColor;
      case 'pending':
        return AppTheme.warningColor;
      case 'completed':
        return AppTheme.primaryColor;
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _showCreateEventDialog(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => const EventCreationDialog(),
    );

    // If event was created successfully, the dialog returns true
    if (result == true) {
      // The events list will automatically update via StreamBuilder
      debugPrint('Event created successfully');
    }
  }

  void _showEventDetails(BuildContext context, EventModel event) {
    context.go('/events/${event.id}');
  }
}
