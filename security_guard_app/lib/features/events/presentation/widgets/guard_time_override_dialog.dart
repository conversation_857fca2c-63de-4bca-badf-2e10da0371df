import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/models/event_shift_model.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../../../../shared/models/user_model.dart';

class GuardTimeOverrideDialog extends ConsumerStatefulWidget {
  final EventShift shift;
  final UserModel guard;
  final ShiftAssignment? existingAssignment;

  const GuardTimeOverrideDialog({
    super.key,
    required this.shift,
    required this.guard,
    this.existingAssignment,
  });

  @override
  ConsumerState<GuardTimeOverrideDialog> createState() => _GuardTimeOverrideDialogState();
}

class _GuardTimeOverrideDialogState extends ConsumerState<GuardTimeOverrideDialog> {
  late DateTime _startTime;
  late DateTime _endTime;
  late TimeOfDay _startTimeOfDay;
  late TimeOfDay _endTimeOfDay;
  final TextEditingController _noteController = TextEditingController();
  bool _useCustomTimes = false;
  
  @override
  void initState() {
    super.initState();
    _initializeTimes();
  }

  void _initializeTimes() {
    if (widget.existingAssignment != null) {
      _startTime = widget.existingAssignment!.startTime;
      _endTime = widget.existingAssignment!.endTime;
      _noteController.text = widget.existingAssignment!.note ?? '';
      
      // Check if times are different from shift defaults
      _useCustomTimes = _startTime != widget.shift.startTime || 
                       _endTime != widget.shift.endTime;
    } else {
      _startTime = widget.shift.startTime;
      _endTime = widget.shift.endTime;
      _useCustomTimes = false;
    }
    
    _startTimeOfDay = TimeOfDay.fromDateTime(_startTime);
    _endTimeOfDay = TimeOfDay.fromDateTime(_endTime);
  }

  void _updateStartTime(TimeOfDay newTime) {
    setState(() {
      _startTimeOfDay = newTime;
      _startTime = DateTime(
        _startTime.year,
        _startTime.month,
        _startTime.day,
        newTime.hour,
        newTime.minute,
      );
    });
  }

  void _updateEndTime(TimeOfDay newTime) {
    setState(() {
      _endTimeOfDay = newTime;
      _endTime = DateTime(
        _endTime.year,
        _endTime.month,
        _endTime.day,
        newTime.hour,
        newTime.minute,
      );
    });
  }

  bool _isValidTimeRange() {
    return _startTime.isBefore(_endTime);
  }

  bool _isWithinShiftBounds() {
    return _startTime.isAfter(widget.shift.startTime.subtract(const Duration(hours: 2))) &&
           _endTime.isBefore(widget.shift.endTime.add(const Duration(hours: 2)));
  }

  Duration get _scheduledDuration => _endTime.difference(_startTime);
  double get _scheduledHours => _scheduledDuration.inMinutes / 60.0;

  bool get _hasOvertime => _scheduledHours > 8.0;

  void _resetToShiftDefaults() {
    setState(() {
      _startTime = widget.shift.startTime;
      _endTime = widget.shift.endTime;
      _startTimeOfDay = TimeOfDay.fromDateTime(_startTime);
      _endTimeOfDay = TimeOfDay.fromDateTime(_endTime);
      _useCustomTimes = false;
    });
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isValid = _isValidTimeRange();
    final isWithinBounds = _isWithinShiftBounds();
    
    return Dialog(
      child: Container(
        width: 500,
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                  child: Icon(
                    Icons.schedule,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Guard Time Override',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        widget.guard.fullName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Shift default times info
            _buildShiftInfoCard(),
            
            const SizedBox(height: 16),
            
            // Custom times toggle
            SwitchListTile(
              title: Text(
                'Use Custom Times',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              subtitle: Text(
                _useCustomTimes 
                    ? 'Custom start and end times for this guard'
                    : 'Use shift default times',
              ),
              value: _useCustomTimes,
              onChanged: (value) {
                setState(() {
                  _useCustomTimes = value;
                  if (!value) {
                    _resetToShiftDefaults();
                  }
                });
              },
              activeColor: AppTheme.primaryColor,
            ),
            
            const SizedBox(height: 16),
            
            // Time selection (only shown if custom times enabled)
            if (_useCustomTimes) ...[
              _buildTimeSelectionCard(),
              const SizedBox(height: 16),
            ],
            
            // Schedule summary
            _buildScheduleSummaryCard(isValid, isWithinBounds),
            
            const SizedBox(height: 16),
            
            // Notes section
            _buildNotesSection(),
            
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(AppLocale.cancel.getString(context)),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: isValid ? _saveAssignment : null,
                  child: Text(AppLocale.save.getString(context)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShiftInfoCard() {
    return Card(
      color: Colors.grey[50],
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.grey[600], size: 20),
                const SizedBox(width: 8),
                Text(
                  'Shift Default Times',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  '${TimeOfDay.fromDateTime(widget.shift.startTime).format(context)} - ${TimeOfDay.fromDateTime(widget.shift.endTime).format(context)}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${widget.shift.duration.inHours}h ${widget.shift.duration.inMinutes % 60}m',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSelectionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.edit_calendar, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Custom Times',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _resetToShiftDefaults,
                  icon: const Icon(Icons.refresh, size: 16),
                  label: const Text('Reset'),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                // Start time
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Start Time',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      InkWell(
                        onTap: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: _startTimeOfDay,
                          );
                          if (time != null) {
                            _updateStartTime(time);
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.access_time, size: 20, color: Colors.grey[600]),
                              const SizedBox(width: 8),
                              Text(
                                _startTimeOfDay.format(context),
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // End time
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'End Time',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      InkWell(
                        onTap: () async {
                          final time = await showTimePicker(
                            context: context,
                            initialTime: _endTimeOfDay,
                          );
                          if (time != null) {
                            _updateEndTime(time);
                          }
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.access_time, size: 20, color: Colors.grey[600]),
                              const SizedBox(width: 8),
                              Text(
                                _endTimeOfDay.format(context),
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleSummaryCard(bool isValid, bool isWithinBounds) {
    return Card(
      color: isValid
          ? (isWithinBounds ? AppTheme.successColor.withOpacity(0.05) : AppTheme.warningColor.withOpacity(0.05))
          : AppTheme.errorColor.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isValid
                      ? (isWithinBounds ? Icons.check_circle : Icons.warning)
                      : Icons.error,
                  color: isValid
                      ? (isWithinBounds ? AppTheme.successColor : AppTheme.warningColor)
                      : AppTheme.errorColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Schedule Summary',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Duration info
            Row(
              children: [
                Icon(Icons.timer, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text('Duration: '),
                Text(
                  '${_scheduledDuration.inHours}h ${_scheduledDuration.inMinutes % 60}m',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                if (_hasOvertime) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppTheme.warningColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'OVERTIME',
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.warningColor,
                      ),
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 8),

            // Validation messages
            if (!isValid)
              Row(
                children: [
                  Icon(Icons.error, size: 16, color: AppTheme.errorColor),
                  const SizedBox(width: 8),
                  Text(
                    'End time must be after start time',
                    style: TextStyle(
                      color: AppTheme.errorColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            else if (!isWithinBounds)
              Row(
                children: [
                  Icon(Icons.warning, size: 16, color: AppTheme.warningColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Times are outside normal shift bounds (±2 hours)',
                      style: TextStyle(
                        color: AppTheme.warningColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              )
            else
              Row(
                children: [
                  Icon(Icons.check, size: 16, color: AppTheme.successColor),
                  const SizedBox(width: 8),
                  Text(
                    'Schedule looks good',
                    style: TextStyle(
                      color: AppTheme.successColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _noteController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any special notes for this assignment...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.all(12),
          ),
        ),
      ],
    );
  }

  void _saveAssignment() {
    final assignment = ShiftAssignment(
      guardId: widget.guard.id,
      startTime: _startTime,
      endTime: _endTime,
      status: AssignmentStatus.confirmed,
      note: _noteController.text.trim().isEmpty ? null : _noteController.text.trim(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.of(context).pop(assignment);
  }
}
