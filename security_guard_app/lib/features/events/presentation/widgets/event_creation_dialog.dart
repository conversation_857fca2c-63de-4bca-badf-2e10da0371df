import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/event_model.dart';
import '../../../../shared/models/event_shift_model.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../../../../shared/models/user_model.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/services/auth_service.dart';
import '../../../../shared/services/user_service.dart';
import '../../../../shared/services/guard_suggestion_service.dart';

// Helper class for conflict detection
class ConflictInfo {
  final String eventTitle;
  final String shiftTime;
  final String conflictType;

  ConflictInfo({
    required this.eventTitle,
    required this.shiftTime,
    required this.conflictType,
  });
}

class EventCreationDialog extends ConsumerStatefulWidget {
  final EventModel? event; // null for create, non-null for edit

  const EventCreationDialog({
    super.key,
    this.event,
  });

  @override
  ConsumerState<EventCreationDialog> createState() => _EventCreationDialogState();
}

class _EventCreationDialogState extends ConsumerState<EventCreationDialog> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _locationController = TextEditingController();

  // Enhanced event properties
  EventType _selectedEventType = EventType.event; // Default to event (no special licenses)
  List<EventShift> _shifts = [];

  // Event date/time and guard requirements
  DateTime? _eventDate;
  TimeOfDay? _eventStartTime;
  TimeOfDay? _eventEndTime;
  int _totalGuardsNeeded = 1;

  // Legacy properties (for backward compatibility)
  DateTime? _startDate;
  TimeOfDay? _startTime;
  DateTime? _endDate;
  TimeOfDay? _endTime;
  List<String> _selectedGuardIds = [];
  List<UserModel> _availableGuards = [];
  bool _isLoading = false;

  // Guard suggestions
  Map<int, List<GuardSuggestion>> _shiftSuggestions = {};
  bool _loadingSuggestions = false;
  bool _showSuggestions = true;

  // Guard assignment state for the third tab
  List<UserModel> _filteredGuards = [];
  String _searchQuery = '';
  String? _selectedLicenseFilter;
  EmploymentStatus? _selectedEmploymentFilter;
  bool _showOnlyAvailable = false;
  bool _showAllGuards = false; // Toggle to show guards with conflicts

  // Track assignments per shift with proper ShiftAssignment objects
  Map<int, List<ShiftAssignment>> _shiftAssignments = {};

  // Tab controller for enhanced UI
  late TabController _tabController;
  int _currentTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentTabIndex = _tabController.index;
      });
    });
    _initializeForm();
    _loadAvailableGuards();
  }

  void _initializeForm() {
    if (widget.event != null) {
      final event = widget.event!;
      _titleController.text = event.title;
      _descriptionController.text = event.description;
      _locationController.text = event.location;

      // Enhanced properties
      _selectedEventType = event.type;
      _shifts = List.from(event.shifts);

      // Event date/time properties
      _eventDate = DateTime(
        event.startDateTime.year,
        event.startDateTime.month,
        event.startDateTime.day,
      );
      _eventStartTime = TimeOfDay.fromDateTime(event.startDateTime);
      _eventEndTime = TimeOfDay.fromDateTime(event.endDateTime);

      // Calculate total guards needed from shifts
      _totalGuardsNeeded = event.shifts.isNotEmpty
          ? event.shifts.map((s) => s.requiredGuards).reduce((a, b) => a > b ? a : b)
          : 1;

      // Legacy properties (for backward compatibility)
      _startDate = DateTime(
        event.startDateTime.year,
        event.startDateTime.month,
        event.startDateTime.day,
      );
      _startTime = TimeOfDay.fromDateTime(event.startDateTime);

      _endDate = DateTime(
        event.endDateTime.year,
        event.endDateTime.month,
        event.endDateTime.day,
      );
      _endTime = TimeOfDay.fromDateTime(event.endDateTime);

      _selectedGuardIds = List.from(event.assignedGuardIds);

      // Initialize shift assignments
      _initializeShiftAssignments();
    } else {
      // Initialize with default values for new events
      final now = DateTime.now();
      _eventDate = now;
      _eventStartTime = const TimeOfDay(hour: 9, minute: 0);
      _eventEndTime = const TimeOfDay(hour: 17, minute: 0);
      _totalGuardsNeeded = 1;

      // Initialize with default shift for new events
      _addDefaultShift();

      // Initialize empty shift assignments
      _initializeShiftAssignments();
    }
  }

  void _addDefaultShift() {
    final now = DateTime.now();

    // Use event date/time if available, otherwise use defaults
    final defaultStart = _eventDate != null && _eventStartTime != null
        ? DateTime(
            _eventDate!.year,
            _eventDate!.month,
            _eventDate!.day,
            _eventStartTime!.hour,
            _eventStartTime!.minute,
          )
        : DateTime(now.year, now.month, now.day, 9, 0); // 9 AM

    final defaultEnd = _eventDate != null && _eventEndTime != null
        ? DateTime(
            _eventDate!.year,
            _eventDate!.month,
            _eventDate!.day,
            _eventEndTime!.hour,
            _eventEndTime!.minute,
          )
        : DateTime(now.year, now.month, now.day, 17, 0); // 5 PM

    _shifts.add(EventShift(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      startTime: defaultStart,
      endTime: defaultEnd.isBefore(defaultStart) ? defaultEnd.add(const Duration(days: 1)) : defaultEnd,
      requiredGuards: _totalGuardsNeeded,
      requiredLicenses: _getRequiredLicensesForEventType(_selectedEventType),
      assignments: [],
      notes: '',
      createdAt: now,
      updatedAt: now,
    ));
  }

  void _initializeShiftAssignments() {
    // Initialize with current assignments from shifts or legacy system
    for (int i = 0; i < _shifts.length; i++) {
      final shift = _shifts[i];
      if (shift.assignments.isNotEmpty) {
        // Use existing shift assignments
        _shiftAssignments[i] = List<ShiftAssignment>.from(shift.assignments);
      } else {
        // Convert legacy guard IDs to basic assignments
        _shiftAssignments[i] = _selectedGuardIds.map((guardId) =>
          ShiftAssignment(
            guardId: guardId,
            startTime: shift.startTime,
            endTime: shift.endTime,
            status: AssignmentStatus.pending, // Start as pending invitation
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          )
        ).toList();
      }
    }
  }

  Future<void> _loadAvailableGuards() async {
    try {
      final userService = ref.read(userServiceProvider);
      final guardsStream = userService.getGuards();

      // Listen to the first emission of the stream
      final guards = await guardsStream.first;
      setState(() {
        _availableGuards = guards.where((guard) => guard.isActive).toList();
        _applyFilters();
      });

      // Load suggestions after guards are loaded
      _loadGuardSuggestions();
    } catch (e) {
      debugPrint('Error loading guards: $e');
      setState(() {
        _availableGuards = [];
      });
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredGuards = _availableGuards.where((guard) {
        // Search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!guard.fullName.toLowerCase().contains(query) &&
              !guard.email.toLowerCase().contains(query)) {
            return false;
          }
        }

        // License filter (enhanced for Slovenian licenses)
        if (_selectedLicenseFilter != null) {
          bool hasLicense = false;

          // Check Slovenian licenses
          if (_selectedLicenseFilter == 'NPK_GOSTINSKI_LOKALI') {
            hasLicense = guard.hasNpkLicense;
          } else if (_selectedLicenseFilter == 'DELO_NA_OBJEKTU') {
            hasLicense = guard.hasDeloNaObjektuLicense;
          } else {
            // Check legacy license IDs
            hasLicense = guard.licenseIds.contains(_selectedLicenseFilter);
          }

          if (!hasLicense) {
            return false;
          }
        }

        // Employment status filter
        if (_selectedEmploymentFilter != null) {
          if (guard.employmentStatus != _selectedEmploymentFilter) {
            return false;
          }
        }

        // Availability filter (placeholder - would need availability data)
        if (_showOnlyAvailable) {
          // TODO: Implement availability checking
        }

        return true;
      }).toList();
    });
  }

  // Helper method to check if a guard has conflicts for a specific shift
  bool _guardHasConflictsForShift(String guardId, int targetShiftIndex) {
    if (targetShiftIndex >= _shifts.length) return false;

    final targetShift = _shifts[targetShiftIndex];

    // Check conflicts with other shifts in this event
    for (int i = 0; i < _shifts.length; i++) {
      if (i == targetShiftIndex) continue; // Skip the target shift

      final otherShift = _shifts[i];
      final assignments = _shiftAssignments[i] ?? [];

      // Check if guard is already assigned to this shift
      final hasAssignment = assignments.any((assignment) =>
          assignment.guardId == guardId &&
          assignment.status == AssignmentStatus.confirmed);

      if (hasAssignment && _hasTimeOverlap(
          targetShift.startTime, targetShift.endTime,
          otherShift.startTime, otherShift.endTime)) {
        return true;
      }
    }

    return false;
  }

  // Get filtered guards for a specific shift (used in shift assignment UI)
  List<UserModel> _getFilteredGuardsForShift(int shiftIndex) {
    if (_showAllGuards) {
      return _filteredGuards; // Show all guards when toggle is enabled
    }

    // Hide guards that have conflicts with this shift
    return _filteredGuards.where((guard) {
      return !_guardHasConflictsForShift(guard.id, shiftIndex);
    }).toList();
  }

  void _assignGuardToShift(String guardId, int shiftIndex) {
    setState(() {
      final existingAssignment = _shiftAssignments[shiftIndex]!
          .where((assignment) => assignment.guardId == guardId)
          .firstOrNull;

      if (existingAssignment == null) {
        final shift = _shifts[shiftIndex];
        final newAssignment = ShiftAssignment(
          guardId: guardId,
          startTime: shift.startTime,
          endTime: shift.endTime,
          status: AssignmentStatus.pending, // Start as pending invitation
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        _shiftAssignments[shiftIndex]!.add(newAssignment);

        // Also add to legacy selected guards list
        if (!_selectedGuardIds.contains(guardId)) {
          _selectedGuardIds.add(guardId);
        }
      }
    });
  }

  bool _hasTimeOverlap(DateTime start1, DateTime end1, DateTime start2, DateTime end2) {
    return start1.isBefore(end2) && end1.isAfter(start2);
  }

  void _removeGuardFromShift(String guardId, int shiftIndex) {
    setState(() {
      _shiftAssignments[shiftIndex]!.removeWhere(
        (assignment) => assignment.guardId == guardId
      );

      // Check if guard is assigned to any other shift
      bool assignedToOtherShift = false;
      for (final assignments in _shiftAssignments.values) {
        if (assignments.any((assignment) => assignment.guardId == guardId)) {
          assignedToOtherShift = true;
          break;
        }
      }

      // Remove from legacy list if not assigned to any shift
      if (!assignedToOtherShift) {
        _selectedGuardIds.remove(guardId);
      }
    });
  }

  Future<void> _loadGuardSuggestions() async {
    if (!_showSuggestions || _shifts.isEmpty) return;

    setState(() {
      _loadingSuggestions = true;
    });

    try {
      final suggestionService = ref.read(guardSuggestionServiceProvider);

      // Create a temporary event model for suggestions
      final tempEvent = EventModel(
        id: 'temp',
        title: _titleController.text.isNotEmpty ? _titleController.text : 'New Event',
        description: _descriptionController.text,
        location: _locationController.text,
        startDateTime: _shifts.isNotEmpty ? _shifts.first.startTime : DateTime.now(),
        endDateTime: _shifts.isNotEmpty ? _shifts.last.endTime : DateTime.now().add(const Duration(hours: 8)),
        type: _selectedEventType,
        shifts: _shifts,
        assignedGuardIds: [],
        status: 'draft',
        createdBy: '',
        companyId: 'default-company',
        expectedDurationMinutes: 480,
        requiredLicenses: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Filter guards by required licenses for the event type
      final requiredLicenses = _getRequiredLicensesForEventType(_selectedEventType);
      final filteredGuards = _availableGuards.where((guard) {
        if (requiredLicenses.isEmpty) return true; // No special licenses needed

        // Check if guard has all required licenses
        for (final requiredLicense in requiredLicenses) {
          bool hasLicense = false;

          if (requiredLicense == 'NPK GOSTINSKI LOKALI') {
            hasLicense = guard.slovenianLicenses.contains(SlovenianLicenseType.npk_gostinski_lokali);
          } else if (requiredLicense == 'DELO NA OBJEKTU') {
            hasLicense = guard.slovenianLicenses.contains(SlovenianLicenseType.delo_na_objektu);
          }

          if (!hasLicense) return false; // Guard doesn't have required license
        }

        return true; // Guard has all required licenses
      }).toList();

      for (int i = 0; i < _shifts.length; i++) {
        final shift = _shifts[i];
        final suggestions = await suggestionService.getGuardSuggestionsForShift(
          shift: shift,
          event: tempEvent,
          availableGuards: filteredGuards, // Use filtered guards
        );

        _shiftSuggestions[i] = suggestions;
      }

      setState(() {
        _loadingSuggestions = false;
      });
    } catch (e) {
      setState(() {
        _loadingSuggestions = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load suggestions: $e'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
      }
    }
  }

  // Navigation methods
  void _goToNextTab() {
    if (_currentTabIndex < 2) {
      if (_validateCurrentTab()) {
        _syncDataToNextTab();
        _tabController.animateTo(_currentTabIndex + 1);
      }
    }
  }

  void _goToPreviousTab() {
    if (_currentTabIndex > 0) {
      _tabController.animateTo(_currentTabIndex - 1);
    }
  }

  bool _validateCurrentTab() {
    switch (_currentTabIndex) {
      case 0: // Event Details Tab
        return _validateEventDetails();
      case 1: // Shifts Tab
        return _validateShifts();
      case 2: // Guards Tab
        return true; // No validation needed for guards tab
      default:
        return true;
    }
  }

  bool _validateEventDetails() {
    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocale.eventTitleRequired.getString(context)),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return false;
    }

    if (_locationController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocale.eventLocationRequired.getString(context)),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return false;
    }

    if (_eventDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Datum dogodka je obvezen'),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return false;
    }

    if (_eventStartTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Čas začetka je obvezen'),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return false;
    }

    return true;
  }

  bool _validateShifts() {
    if (_shifts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Dodajte vsaj eno izmeno'),
          backgroundColor: AppTheme.warningColor,
        ),
      );
      return false;
    }

    for (int i = 0; i < _shifts.length; i++) {
      final shift = _shifts[i];
      if (shift.requiredGuards <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Izmena ${i + 1} mora imeti vsaj enega varnostnika'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
        return false;
      }
    }

    return true;
  }

  void _syncDataToNextTab() {
    switch (_currentTabIndex) {
      case 0: // Moving from Event Details to Shifts
        _syncEventDetailsToShifts();
        break;
      case 1: // Moving from Shifts to Guards
        _syncShiftsToGuards();
        break;
    }
  }

  void _syncEventDetailsToShifts() {
    // Update existing shifts with new event details
    if (_shifts.isNotEmpty && _eventDate != null && _eventStartTime != null) {
      final newStartTime = DateTime(
        _eventDate!.year,
        _eventDate!.month,
        _eventDate!.day,
        _eventStartTime!.hour,
        _eventStartTime!.minute,
      );

      final newEndTime = _eventEndTime != null
          ? DateTime(
              _eventDate!.year,
              _eventDate!.month,
              _eventDate!.day,
              _eventEndTime!.hour,
              _eventEndTime!.minute,
            )
          : newStartTime.add(const Duration(hours: 8));

      // Update the first shift with event details
      if (_shifts.isNotEmpty) {
        final updatedShift = _shifts[0].copyWith(
          startTime: newStartTime,
          endTime: newEndTime.isBefore(newStartTime) ? newEndTime.add(const Duration(days: 1)) : newEndTime,
          requiredGuards: _totalGuardsNeeded,
          requiredLicenses: _getRequiredLicensesForEventType(_selectedEventType),
          updatedAt: DateTime.now(),
        );

        setState(() {
          _shifts[0] = updatedShift;
        });
      }
    }
  }

  void _syncShiftsToGuards() {
    // Reinitialize shift assignments based on current shifts
    setState(() {
      _initializeShiftAssignments();
    });

    // Reload guard suggestions with updated shift data
    _loadGuardSuggestions();

    // Apply filters to show relevant guards
    _applyFilters();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _locationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.event != null;

    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.95,
        constraints: const BoxConstraints(maxWidth: 800, maxHeight: 800),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isEditing ? Icons.edit : Icons.add,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      isEditing
                        ? AppLocale.editEventTitle.getString(context)
                        : AppLocale.createEventTitle.getString(context),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),



            // Tab Bar
            Container(
              color: Theme.of(context).colorScheme.surface,
              child: TabBar(
                controller: _tabController,
                tabs: [
                  Tab(
                    icon: const Icon(Icons.info),
                    text: AppLocale.eventDetails.getString(context),
                  ),
                  Tab(
                    icon: const Icon(Icons.schedule),
                    text: AppLocale.shifts.getString(context),
                  ),
                  Tab(
                    icon: const Icon(Icons.people),
                    text: AppLocale.guards.getString(context),
                  ),
                ],
              ),
            ),
            
            // Tab Content
            Expanded(
              child: Form(
                key: _formKey,
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    // Tab 1: Event Details
                    _buildEventDetailsTab(),

                    // Tab 2: Shifts
                    _buildShiftsTab(),

                    // Tab 3: Guards (Legacy)
                    _buildGuardsTab(),
                  ],
                ),
              ),
            ),
            
            // Navigation Buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Left side - Previous button
                  Row(
                    children: [
                      if (_currentTabIndex > 0)
                        TextButton.icon(
                          onPressed: _goToPreviousTab,
                          icon: const Icon(Icons.arrow_back),
                          label: Text('Nazaj'),
                        ),
                    ],
                  ),

                  // Right side - Cancel, Next, or Create button
                  Row(
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(AppLocale.cancel.getString(context)),
                      ),
                      const SizedBox(width: 12),

                      if (_currentTabIndex < 2)
                        ElevatedButton.icon(
                          onPressed: _goToNextTab,
                          icon: const Icon(Icons.arrow_forward),
                          label: Text('Naprej'),
                        )
                      else
                        ElevatedButton(
                          onPressed: _isLoading ? null : _handleSubmit,
                          child: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : Text(
                                  isEditing
                                    ? AppLocale.update.getString(context)
                                    : AppLocale.create.getString(context),
                                ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTextField(
            controller: _titleController,
            label: AppLocale.eventTitle.getString(context),
            icon: Icons.title,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocale.eventTitleRequired.getString(context);
              }
              return null;
            },
          ),

          const SizedBox(height: 16),

          _buildTextField(
            controller: _descriptionController,
            label: '${AppLocale.eventDescription.getString(context)} (${AppLocale.optional.getString(context)})',
            icon: Icons.description,
            maxLines: 3,
            validator: null,
          ),

          const SizedBox(height: 16),

          _buildTextField(
            controller: _locationController,
            label: AppLocale.location.getString(context),
            icon: Icons.location_on,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return AppLocale.eventLocationRequired.getString(context);
              }
              return null;
            },
          ),

          const SizedBox(height: 24),

          // Event Type Selection
          _buildEventTypeSection(),

          const SizedBox(height: 24),

          // Event Date and Time Section
          _buildEventDateTimeSection(),

          const SizedBox(height: 24),

          // Guard Requirements Section
          _buildGuardRequirementsSection(),

          const SizedBox(height: 32),

          // Tab navigation hint
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Izpolnite osnovne podatke o dogodku in kliknite "Naprej" za nastavitev izmen.',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShiftsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppLocale.eventShifts.getString(context),
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              Row(
                children: [
                  // Suggestions toggle
                  Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        size: 16,
                        color: _showSuggestions ? AppTheme.primaryColor : Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Switch(
                        value: _showSuggestions,
                        onChanged: (value) {
                          setState(() {
                            _showSuggestions = value;
                          });
                          if (value) {
                            _loadGuardSuggestions();
                          }
                        },
                        activeColor: AppTheme.primaryColor,
                      ),
                    ],
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: _addNewShift,
                    icon: const Icon(Icons.add),
                    label: Text(AppLocale.addShift.getString(context)),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),

          if (_shifts.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocale.noShiftsAdded.getString(context),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _addNewShift,
                    child: Text(AppLocale.addFirstShift.getString(context)),
                  ),
                ],
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _shifts.length,
              itemBuilder: (context, index) => _buildShiftCard(index),
            ),

          const SizedBox(height: 32),

          // Tab navigation hint
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Nastavite izmene z zahtevanimi varnostniki in licencami, nato kliknite "Naprej" za dodelitev varnostnikov.',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGuardsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(Icons.people, color: Theme.of(context).colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                'Dodelitev varnostnikov',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Filters
          _buildGuardFilters(),
          const SizedBox(height: 16),

          // Shifts with guard assignment
          if (_shifts.isEmpty)
            Center(
              child: Column(
                children: [
                  Icon(
                    Icons.schedule_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Ni dodanih izmen',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Dodajte izmene v zavihku "Izmene"',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _shifts.length,
              itemBuilder: (context, index) => _buildShiftAssignmentSection(index),
            ),
        ],
      ),
    );
  }



  Widget _buildGuardFilters() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filtri',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),

            // Search bar
            TextField(
              decoration: InputDecoration(
                hintText: 'Iskanje varnostnikov...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _applyFilters();
              },
            ),
            const SizedBox(height: 12),

            // Filter chips
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: const Text('Polni delovni čas'),
                  selected: _selectedEmploymentFilter == EmploymentStatus.full_time,
                  onSelected: (selected) {
                    setState(() {
                      _selectedEmploymentFilter = selected ? EmploymentStatus.full_time : null;
                    });
                    _applyFilters();
                  },
                ),
                FilterChip(
                  label: const Text('NPK licenca'),
                  selected: _selectedLicenseFilter == 'NPK_GOSTINSKI_LOKALI',
                  onSelected: (selected) {
                    setState(() {
                      _selectedLicenseFilter = selected ? 'NPK_GOSTINSKI_LOKALI' : null;
                    });
                    _applyFilters();
                  },
                ),
                FilterChip(
                  label: const Text('DELO licenca'),
                  selected: _selectedLicenseFilter == 'DELO_NA_OBJEKTU',
                  onSelected: (selected) {
                    setState(() {
                      _selectedLicenseFilter = selected ? 'DELO_NA_OBJEKTU' : null;
                    });
                    _applyFilters();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShiftAssignmentSection(int shiftIndex) {
    final shift = _shifts[shiftIndex];
    final assignments = _shiftAssignments[shiftIndex] ?? [];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Shift header
            Row(
              children: [
                Icon(Icons.schedule, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Izmena ${shiftIndex + 1}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${DateFormat('dd/MM HH:mm').format(shift.startTime)} - ${DateFormat('dd/MM HH:mm').format(shift.endTime)}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: assignments.length >= shift.requiredGuards
                        ? Colors.green.withOpacity(0.1)
                        : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${assignments.length}/${shift.requiredGuards}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: assignments.length >= shift.requiredGuards
                          ? Colors.green[700]
                          : Colors.orange[700],
                    ),
                  ),
                ),
              ],
            ),

            // Required licenses
            if (shift.requiredLicenses.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.verified_user, size: 16, color: Colors.orange),
                  const SizedBox(width: 8),
                  Text(
                    'Potrebne licence: ',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: Colors.grey[700],
                    ),
                  ),
                  Expanded(
                    child: Wrap(
                      spacing: 4,
                      children: shift.requiredLicenses.map((license) =>
                        Chip(
                          label: Text(
                            license,
                            style: const TextStyle(fontSize: 10),
                          ),
                          backgroundColor: Colors.orange.withOpacity(0.1),
                          side: BorderSide(color: Colors.orange.withOpacity(0.3)),
                        ),
                      ).toList(),
                    ),
                  ),
                ],
              ),
            ],

            const SizedBox(height: 16),

            // Assigned guards
            _buildAssignedGuards(shiftIndex),

            const SizedBox(height: 16),

            // Available guards to add
            _buildAvailableGuardsForShift(shiftIndex),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
      ),
      validator: validator,
    );
  }

  Widget _buildEventTypeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.eventType.getString(context),
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: EventType.values.map((type) {
                return RadioListTile<EventType>(
                  title: Text(_getEventTypeDisplayName(type)),
                  subtitle: Text(_getEventTypeDescription(type)),
                  value: type,
                  groupValue: _selectedEventType,
                  onChanged: (EventType? value) {
                    if (value != null) {
                      setState(() {
                        _selectedEventType = value;
                        // Reload suggestions when event type changes (affects license requirements)
                        _loadGuardSuggestions();
                      });
                    }
                  },
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEventDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Datum in čas dogodka',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // Event Date
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectEventDate(context),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Datum dogodka',
                            prefixIcon: Icon(Icons.calendar_today),
                            border: OutlineInputBorder(),
                          ),
                          child: Text(
                            _eventDate != null
                              ? DateFormat('dd/MM/yyyy').format(_eventDate!)
                              : 'Izberite datum',
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Event Time Range
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectEventTime(context, true),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Začetek',
                            prefixIcon: Icon(Icons.access_time),
                            border: OutlineInputBorder(),
                          ),
                          child: Text(
                            _eventStartTime != null
                              ? _eventStartTime!.format(context)
                              : 'Čas začetka',
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectEventTime(context, false),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Konec',
                            prefixIcon: Icon(Icons.access_time),
                            border: OutlineInputBorder(),
                          ),
                          child: Text(
                            _eventEndTime != null
                              ? _eventEndTime!.format(context)
                              : 'Čas konca',
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGuardRequirementsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Potrebe po varnostnikih',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    const Icon(Icons.people),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Skupno število varnostnikov',
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                    SizedBox(
                      width: 80,
                      child: TextFormField(
                        initialValue: _totalGuardsNeeded.toString(),
                        keyboardType: TextInputType.number,
                        textAlign: TextAlign.center,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                        ),
                        onChanged: (value) {
                          final parsed = int.tryParse(value);
                          if (parsed != null && parsed > 0) {
                            setState(() {
                              _totalGuardsNeeded = parsed;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Required licenses display
                if (_getRequiredLicensesForEventType(_selectedEventType).isNotEmpty) ...[
                  const Divider(),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      const Icon(Icons.verified_user, color: Colors.orange),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Potrebne licence:',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Wrap(
                              spacing: 8,
                              children: _getRequiredLicensesForEventType(_selectedEventType)
                                  .map((license) => Chip(
                                        label: Text(
                                          license,
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                        backgroundColor: Colors.orange.withOpacity(0.1),
                                        side: BorderSide(color: Colors.orange.withOpacity(0.3)),
                                      ))
                                  .toList(),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _getEventTypeDisplayName(EventType type) {
    switch (type) {
      case EventType.event:
        return 'Varovanje dogodka'; // Event security - no special licenses needed
      case EventType.venue:
        return 'Varovanje objekta'; // Venue/object security - DELO NA OBJEKTU license needed
      case EventType.local:
        return 'Varovanje lokala'; // Local/hospitality security - NPK license needed
    }
  }

  String _getEventTypeDescription(EventType type) {
    switch (type) {
      case EventType.event:
        return 'Varovanje dogodkov, koncertov, festivalov - ni potrebnih posebnih licenc';
      case EventType.venue:
        return 'Varovanje objektov, stavb, prostorov - potrebna licenca DELO NA OBJEKTU';
      case EventType.local:
        return 'Varovanje gostinskih lokalov, klubov - potrebna NPK GOSTINSKI LOKALI licenca';
    }
  }

  List<String> _getRequiredLicensesForEventType(EventType type) {
    switch (type) {
      case EventType.event:
        return []; // No special licenses needed for events
      case EventType.venue:
        return ['DELO NA OBJEKTU']; // Object security license required
      case EventType.local:
        return ['NPK GOSTINSKI LOKALI']; // Hospitality license required
    }
  }

  Widget _buildAssignedGuards(int shiftIndex) {
    final assignments = _shiftAssignments[shiftIndex] ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Dodeljeni varnostniki (${assignments.length})',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        if (assignments.isEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Colors.grey.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.person_add_outlined,
                  color: Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Ni dodeljenih varnostnikov',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: assignments.map((assignment) {
              final guard = _availableGuards.firstWhere(
                (g) => g.id == assignment.guardId,
                orElse: () => UserModel(
                  id: assignment.guardId,
                  firstName: 'Unknown',
                  lastName: 'Guard',
                  email: '',
                  phone: '',
                  role: 'guard',
                  isActive: false,
                  createdAt: DateTime.now(),
                  updatedAt: DateTime.now(),
                ),
              );

              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.green.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 10,
                      backgroundColor: Colors.green.withOpacity(0.2),
                      child: Text(
                        guard.firstName[0].toUpperCase(),
                        style: TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                          color: Colors.green[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    Text(
                      guard.fullName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.green[700],
                      ),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () => _removeGuardFromShift(guard.id, shiftIndex),
                      child: Icon(
                        Icons.close,
                        size: 14,
                        color: Colors.green[700],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  Widget _buildAvailableGuardsForShift(int shiftIndex) {
    final shift = _shifts[shiftIndex];
    final assignments = _shiftAssignments[shiftIndex] ?? [];
    final assignedGuardIds = assignments.map((a) => a.guardId).toSet();

    // Get guards filtered for this specific shift (hides conflicts by default)
    final shiftFilteredGuards = _getFilteredGuardsForShift(shiftIndex);

    // Filter guards that are not already assigned to this shift
    final availableGuards = shiftFilteredGuards.where((guard) =>
      !assignedGuardIds.contains(guard.id)
    ).toList();

    // Filter by required licenses
    final qualifiedGuards = availableGuards.where((guard) {
      for (String requiredLicense in shift.requiredLicenses) {
        if (requiredLicense == 'NPK GOSTINSKI LOKALI' && !guard.hasNpkLicense) {
          return false;
        } else if (requiredLicense == 'DELO NA OBJEKTU' && !guard.hasDeloNaObjektuLicense) {
          return false;
        }
      }
      return true;
    }).toList();

    // Count total guards (including conflicted ones) for the toggle
    final totalAvailableGuards = _filteredGuards.where((guard) =>
      !assignedGuardIds.contains(guard.id)
    ).length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with toggle
        Row(
          children: [
            Expanded(
              child: Text(
                'Razpoložljivi varnostniki (${qualifiedGuards.length})',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (totalAvailableGuards > qualifiedGuards.length)
              Row(
                children: [
                  Text(
                    'Prikaži vse',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 4),
                  Switch(
                    value: _showAllGuards,
                    onChanged: (value) {
                      setState(() {
                        _showAllGuards = value;
                      });
                    },
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ],
              ),
          ],
        ),
        const SizedBox(height: 8),
        if (qualifiedGuards.isEmpty)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: Colors.orange.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_outlined,
                  color: Colors.orange[700],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _showAllGuards
                        ? 'Ni razpoložljivih varnostnikov z ustreznimi licencami'
                        : 'Ni razpoložljivih varnostnikov brez konfliktov. Vklopite "Prikaži vse" za več možnosti.',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )
        else
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: qualifiedGuards.take(10).map((guard) { // Show max 10 guards
              final hasConflict = _guardHasConflictsForShift(guard.id, shiftIndex);

              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: hasConflict
                      ? Colors.orange.withOpacity(0.1)
                      : Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: hasConflict
                        ? Colors.orange.withOpacity(0.3)
                        : Colors.blue.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 10,
                      backgroundColor: hasConflict
                          ? Colors.orange.withOpacity(0.2)
                          : Colors.blue.withOpacity(0.2),
                      child: Text(
                        guard.firstName[0].toUpperCase(),
                        style: TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                          color: hasConflict ? Colors.orange[700] : Colors.blue[700],
                        ),
                      ),
                    ),
                    const SizedBox(width: 6),
                    if (hasConflict) ...[
                      Icon(
                        Icons.warning,
                        size: 12,
                        color: Colors.orange[700],
                      ),
                      const SizedBox(width: 4),
                    ],
                    Text(
                      guard.fullName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: hasConflict ? Colors.orange[700] : Colors.blue[700],
                      ),
                    ),
                    const SizedBox(width: 4),
                    GestureDetector(
                      onTap: () => _assignGuardToShift(guard.id, shiftIndex),
                      child: Icon(
                        Icons.add,
                        size: 14,
                        color: hasConflict ? Colors.orange[700] : Colors.blue[700],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  void _addNewShift() {
    final now = DateTime.now();

    // Use event date/time if available, otherwise use defaults
    final defaultStart = _eventDate != null && _eventStartTime != null
        ? DateTime(
            _eventDate!.year,
            _eventDate!.month,
            _eventDate!.day,
            _eventStartTime!.hour,
            _eventStartTime!.minute,
          )
        : DateTime(now.year, now.month, now.day, 9, 0);

    final defaultEnd = _eventDate != null && _eventEndTime != null
        ? DateTime(
            _eventDate!.year,
            _eventDate!.month,
            _eventDate!.day,
            _eventEndTime!.hour,
            _eventEndTime!.minute,
          )
        : DateTime(now.year, now.month, now.day, 17, 0);

    setState(() {
      _shifts.add(EventShift(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        startTime: defaultStart,
        endTime: defaultEnd.isBefore(defaultStart) ? defaultEnd.add(const Duration(days: 1)) : defaultEnd,
        requiredGuards: _totalGuardsNeeded,
        requiredLicenses: _getRequiredLicensesForEventType(_selectedEventType),
        assignments: [],
        notes: '',
        createdAt: now,
        updatedAt: now,
      ));
    });

    // Initialize empty assignments for the new shift
    _shiftAssignments[_shifts.length - 1] = [];

    // Reload suggestions after adding new shift
    _loadGuardSuggestions();
  }

  Widget _buildShiftCard(int index) {
    final shift = _shifts[index];

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${AppLocale.shift.getString(context)} ${index + 1}',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: () => _editShift(index),
                      icon: const Icon(Icons.edit),
                      tooltip: AppLocale.editShift.getString(context),
                    ),
                    IconButton(
                      onPressed: () => _deleteShift(index),
                      icon: const Icon(Icons.delete),
                      tooltip: AppLocale.deleteShift.getString(context),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Start: ${DateFormat('dd/MM/yyyy HH:mm').format(shift.startTime)}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Expanded(
                  child: Text(
                    'End: ${DateFormat('dd/MM/yyyy HH:mm').format(shift.endTime)}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    '${AppLocale.requiredGuards.getString(context)}: ${shift.requiredGuards}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                Expanded(
                  child: Text(
                    '${AppLocale.licenses.getString(context)}: ${shift.requiredLicenses.isEmpty ? AppLocale.none.getString(context) : shift.requiredLicenses.join(', ')}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            if (shift.notes != null && shift.notes!.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                '${AppLocale.notes.getString(context)}: ${shift.notes}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],

            // Guard suggestions section
            if (_showSuggestions) _buildShiftSuggestions(index),
          ],
        ),
      ),
    );
  }

  void _editShift(int index) async {
    final shift = _shifts[index];

    final result = await showDialog<EventShift>(
      context: context,
      builder: (context) => _ShiftEditDialog(
        shift: shift,
        eventType: _selectedEventType,
      ),
    );

    if (result != null) {
      setState(() {
        _shifts[index] = result;
      });

      // Reload guard suggestions after shift changes
      _loadGuardSuggestions();
    }
  }

  void _deleteShift(int index) {
    setState(() {
      _shifts.removeAt(index);

      // Remove suggestions for this shift
      _shiftSuggestions.remove(index);
      // Reindex remaining suggestions
      final newSuggestions = <int, List<GuardSuggestion>>{};
      for (final entry in _shiftSuggestions.entries) {
        if (entry.key > index) {
          newSuggestions[entry.key - 1] = entry.value;
        } else if (entry.key < index) {
          newSuggestions[entry.key] = entry.value;
        }
      }
      _shiftSuggestions = newSuggestions;

      // Remove assignments for this shift
      _shiftAssignments.remove(index);
      // Reindex remaining assignments
      final newAssignments = <int, List<ShiftAssignment>>{};
      for (final entry in _shiftAssignments.entries) {
        if (entry.key > index) {
          newAssignments[entry.key - 1] = entry.value;
        } else if (entry.key < index) {
          newAssignments[entry.key] = entry.value;
        }
      }
      _shiftAssignments = newAssignments;
    });

    // Reload suggestions after shift deletion
    _loadGuardSuggestions();
  }

  Widget _buildShiftSuggestions(int shiftIndex) {
    final suggestions = _shiftSuggestions[shiftIndex] ?? [];
    if (suggestions.isEmpty && !_loadingSuggestions) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 12),
        const Divider(),
        const SizedBox(height: 8),
        Row(
          children: [
            Icon(Icons.auto_awesome, size: 16, color: AppTheme.primaryColor),
            const SizedBox(width: 8),
            Text(
              AppLocale.suggestedGuards.getString(context),
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
            const Spacer(),
            if (_loadingSuggestions)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          ],
        ),
        const SizedBox(height: 8),

        if (_loadingSuggestions)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Text(AppLocale.loadingSuggestions.getString(context)),
            ),
          )
        else if (suggestions.isEmpty)
          Padding(
            padding: const EdgeInsets.all(8),
            child: Text(
              AppLocale.noSuitableGuards.getString(context),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          )
        else
          ...suggestions.take(3).map((suggestion) => _buildSuggestionItem(suggestion, shiftIndex)),
      ],
    );
  }

  Widget _buildSuggestionItem(GuardSuggestion suggestion, int shiftIndex) {
    final guard = suggestion.guard;
    final isRecommended = suggestion.isRecommended;
    final matchScore = suggestion.matchScore;

    return Container(
      margin: const EdgeInsets.only(bottom: 6),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: isRecommended
            ? Colors.amber.withOpacity(0.1)
            : AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isRecommended
              ? Colors.amber.withOpacity(0.3)
              : AppTheme.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          // Suggestion indicator
          Icon(
            isRecommended ? Icons.star : Icons.thumb_up_outlined,
            size: 16,
            color: isRecommended ? Colors.amber[700] : AppTheme.primaryColor,
          ),
          const SizedBox(width: 10),
          // Guard info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  guard.fullName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  '${(matchScore * 100).round()}% ${AppLocale.matchScore.getString(context)} • ${suggestion.reasons.isNotEmpty ? suggestion.reasons.first : AppLocale.suitableCandidate.getString(context)}',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                // Show licenses
                if (guard.slovenianLicenses.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 4,
                    children: guard.slovenianLicenses.map((license) =>
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: Text(
                          license == SlovenianLicenseType.npk_gostinski_lokali ? 'NPK' : 'DELO',
                          style: TextStyle(
                            fontSize: 9,
                            color: AppTheme.successColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ).toList(),
                  ),
                ],
              ],
            ),
          ),
          // Quick assign button
          InkWell(
            onTap: () {
              setState(() {
                if (!_selectedGuardIds.contains(guard.id)) {
                  _selectedGuardIds.add(guard.id);
                }
              });
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(AppLocale.addedToEvent.getString(context).replaceAll('{name}', guard.fullName)),
                  backgroundColor: AppTheme.successColor,
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                AppLocale.add.getString(context),
                style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.startDate.getString(context),
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: AppLocale.selectDate.getString(context),
                date: _startDate,
                onTap: () => _selectDate(context, true),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTimeField(
                label: AppLocale.selectTime.getString(context),
                time: _startTime,
                onTap: () => _selectTime(context, true),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),

        Text(
          '${AppLocale.endDate.getString(context)} (${AppLocale.optional.getString(context)})',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 4),
        Text(
          AppLocale.endDateOptionalHint.getString(context),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.6),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: AppLocale.selectDate.getString(context),
                date: _endDate,
                onTap: () => _selectDate(context, false),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildTimeField(
                label: AppLocale.selectTime.getString(context),
                time: _endTime,
                onTap: () => _selectTime(context, false),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: const Icon(Icons.calendar_today),
          border: const OutlineInputBorder(),
        ),
        child: Text(
          date != null 
            ? DateFormat('dd/MM/yyyy').format(date)
            : '',
        ),
      ),
    );
  }

  Widget _buildTimeField({
    required String label,
    required TimeOfDay? time,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: label,
          prefixIcon: const Icon(Icons.access_time),
          border: const OutlineInputBorder(),
        ),
        child: Text(
          time != null
            ? time.format(context)
            : '',
        ),
      ),
    );
  }

  Widget _buildGuardAssignmentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.assignGuards.getString(context),
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _showGuardSelectionDialog,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                const Icon(Icons.people),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _selectedGuardIds.isEmpty
                        ? AppLocale.noGuardsSelected.getString(context)
                        : AppLocale.guardsSelected.getString(context).replaceAll('{count}', _selectedGuardIds.length.toString()),
                  ),
                ),
                const Icon(Icons.arrow_drop_down),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isStartDate
          ? (_startDate ?? DateTime.now())
          : (_endDate ?? _startDate ?? DateTime.now()),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
          // If end date is before start date, reset it
          if (_endDate != null && _endDate!.isBefore(picked)) {
            _endDate = null;
            _endTime = null;
          }
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime
          ? (_startTime ?? TimeOfDay.now())
          : (_endTime ?? _startTime ?? TimeOfDay.now()),
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _startTime = picked;
        } else {
          _endTime = picked;
        }
      });
    }
  }

  Future<void> _selectEventDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _eventDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        _eventDate = picked;
        // Update shifts to match the new event date
        _updateShiftsForEventDate();
        // Reload suggestions when date changes
        _loadGuardSuggestions();
      });
    }
  }

  Future<void> _selectEventTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime
          ? (_eventStartTime ?? const TimeOfDay(hour: 9, minute: 0))
          : (_eventEndTime ?? const TimeOfDay(hour: 17, minute: 0)),
    );

    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _eventStartTime = picked;
        } else {
          _eventEndTime = picked;
        }
        // Update shifts to match the new event times
        _updateShiftsForEventDate();
        // Reload suggestions when time changes
        _loadGuardSuggestions();
      });
    }
  }

  void _updateShiftsForEventDate() {
    if (_eventDate == null || _eventStartTime == null || _eventEndTime == null) return;

    final eventStart = DateTime(
      _eventDate!.year,
      _eventDate!.month,
      _eventDate!.day,
      _eventStartTime!.hour,
      _eventStartTime!.minute,
    );

    final eventEnd = DateTime(
      _eventDate!.year,
      _eventDate!.month,
      _eventDate!.day,
      _eventEndTime!.hour,
      _eventEndTime!.minute,
    );

    // If end time is before start time, assume it's next day
    final adjustedEventEnd = eventEnd.isBefore(eventStart)
        ? eventEnd.add(const Duration(days: 1))
        : eventEnd;

    setState(() {
      // Update existing shifts or create a default one
      if (_shifts.isEmpty) {
        _shifts.add(EventShift(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          startTime: eventStart,
          endTime: adjustedEventEnd,
          requiredGuards: _totalGuardsNeeded,
          requiredLicenses: _getRequiredLicensesForEventType(_selectedEventType),
          assignments: [],
          notes: '',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));
      } else {
        // Update the first shift to match event times
        final updatedShift = _shifts[0].copyWith(
          startTime: eventStart,
          endTime: adjustedEventEnd,
          requiredGuards: _totalGuardsNeeded,
          requiredLicenses: _getRequiredLicensesForEventType(_selectedEventType),
          updatedAt: DateTime.now(),
        );
        _shifts[0] = updatedShift;
      }
    });
  }

  void _showGuardSelectionDialog() {
    showDialog(
      context: context,
      builder: (context) => _GuardSelectionDialog(
        availableGuards: _availableGuards,
        selectedGuardIds: _selectedGuardIds,
        onSelectionChanged: (selectedIds) {
          setState(() {
            _selectedGuardIds = selectedIds;
          });
        },
      ),
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    // Validate shifts
    if (_shifts.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocale.pleaseAddAtLeastOneShift.getString(context)),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    // Calculate event start and end times from shifts
    DateTime? eventStartTime;
    DateTime? eventEndTime;

    for (final shift in _shifts) {
      if (eventStartTime == null || shift.startTime.isBefore(eventStartTime)) {
        eventStartTime = shift.startTime;
      }
      if (eventEndTime == null || shift.endTime.isAfter(eventEndTime)) {
        eventEndTime = shift.endTime;
      }
    }

    if (eventStartTime == null || eventEndTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocale.invalidShiftTimes.getString(context)),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authService = ref.read(authServiceProvider);
      final currentUser = authService.currentFirebaseUser;

      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final eventService = ref.read(eventServiceProvider);

      // Collect all required licenses from shifts
      final allRequiredLicenses = <String>{};
      for (final shift in _shifts) {
        allRequiredLicenses.addAll(shift.requiredLicenses);
      }

      final event = EventModel(
        id: widget.event?.id ?? '',
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        location: _locationController.text.trim(),
        startDateTime: eventStartTime,
        endDateTime: eventEndTime,
        type: _selectedEventType,
        status: widget.event?.status ?? 'pending',
        createdBy: currentUser.uid,
        companyId: 'default-company', // TODO: Get from user profile
        assignedGuardIds: _selectedGuardIds, // Legacy support
        shifts: _shifts, // New shift-based system
        expectedDurationMinutes: eventEndTime.difference(eventStartTime).inMinutes,
        requiredLicenses: allRequiredLicenses.toList(),
        createdAt: widget.event?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (widget.event == null) {
        // Create new event
        await eventService.createEvent(event);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocale.eventCreatedSuccess.getString(context)),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } else {
        // Update existing event
        await eventService.updateEvent(event);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocale.eventUpdatedSuccess.getString(context)),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      debugPrint('Error saving event: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocale.eventCreationFailed.getString(context)),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }










}

class _ShiftEditDialog extends StatefulWidget {
  final EventShift shift;
  final EventType eventType;

  const _ShiftEditDialog({
    required this.shift,
    required this.eventType,
  });

  @override
  State<_ShiftEditDialog> createState() => _ShiftEditDialogState();
}

class _ShiftEditDialogState extends State<_ShiftEditDialog> {
  final _formKey = GlobalKey<FormState>();

  late DateTime _startDate;
  late TimeOfDay _startTime;
  late DateTime _endDate;
  late TimeOfDay _endTime;
  late int _requiredGuards;
  late List<String> _requiredLicenses;
  late String _notes;

  @override
  void initState() {
    super.initState();

    _startDate = DateTime(
      widget.shift.startTime.year,
      widget.shift.startTime.month,
      widget.shift.startTime.day,
    );
    _startTime = TimeOfDay.fromDateTime(widget.shift.startTime);

    _endDate = DateTime(
      widget.shift.endTime.year,
      widget.shift.endTime.month,
      widget.shift.endTime.day,
    );
    _endTime = TimeOfDay.fromDateTime(widget.shift.endTime);

    _requiredGuards = widget.shift.requiredGuards;
    _requiredLicenses = List.from(widget.shift.requiredLicenses);
    _notes = widget.shift.notes ?? '';
  }

  List<String> _getRequiredLicensesForEventType(EventType type) {
    switch (type) {
      case EventType.event:
        return []; // No special licenses needed for events
      case EventType.venue:
        return ['DELO NA OBJEKTU']; // Object security license required
      case EventType.local:
        return ['NPK GOSTINSKI LOKALI']; // Hospitality license required
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 600, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.edit,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Uredi izmeno',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDateTimeSection(),
                      const SizedBox(height: 24),
                      _buildGuardRequirementsSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('Prekliči'),
                  ),
                  ElevatedButton(
                    onPressed: _saveShift,
                    child: Text('Shrani'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Datum in čas izmene',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // Start Date and Time
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(context, true),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Datum začetka',
                    prefixIcon: Icon(Icons.calendar_today),
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(_startDate),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: InkWell(
                onTap: () => _selectTime(context, true),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Čas začetka',
                    prefixIcon: Icon(Icons.access_time),
                    border: OutlineInputBorder(),
                  ),
                  child: Text(_startTime.format(context)),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // End Date and Time
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(context, false),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Datum konca',
                    prefixIcon: Icon(Icons.calendar_today),
                    border: OutlineInputBorder(),
                  ),
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(_endDate),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: InkWell(
                onTap: () => _selectTime(context, false),
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Čas konca',
                    prefixIcon: Icon(Icons.access_time),
                    border: OutlineInputBorder(),
                  ),
                  child: Text(_endTime.format(context)),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildGuardRequirementsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Potrebe po varnostnikih',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),

        // Required guards count
        Row(
          children: [
            const Icon(Icons.people),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Število potrebnih varnostnikov',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
            ),
            SizedBox(
              width: 80,
              child: TextFormField(
                initialValue: _requiredGuards.toString(),
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Obvezno';
                  }
                  final parsed = int.tryParse(value);
                  if (parsed == null || parsed <= 0) {
                    return 'Neveljavno';
                  }
                  return null;
                },
                onChanged: (value) {
                  final parsed = int.tryParse(value);
                  if (parsed != null && parsed > 0) {
                    _requiredGuards = parsed;
                  }
                },
              ),
            ),
          ],
        ),

        // Required licenses display
        if (_getRequiredLicensesForEventType(widget.eventType).isNotEmpty) ...[
          const SizedBox(height: 16),
          const Divider(),
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.verified_user, color: Colors.orange),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Potrebne licence:',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Wrap(
                      spacing: 8,
                      children: _getRequiredLicensesForEventType(widget.eventType)
                          .map((license) => Chip(
                                label: Text(
                                  license,
                                  style: const TextStyle(fontSize: 12),
                                ),
                                backgroundColor: Colors.orange.withOpacity(0.1),
                                side: BorderSide(color: Colors.orange.withOpacity(0.3)),
                              ))
                          .toList(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Opombe (neobvezno)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          initialValue: _notes,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'Dodatne opombe za to izmeno...',
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            _notes = value;
          },
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStart) async {
    final initialDate = isStart ? _startDate : _endDate;
    final picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime.now().subtract(const Duration(days: 1)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isStart) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _selectTime(BuildContext context, bool isStart) async {
    final initialTime = isStart ? _startTime : _endTime;
    final picked = await showTimePicker(
      context: context,
      initialTime: initialTime,
    );

    if (picked != null) {
      setState(() {
        if (isStart) {
          _startTime = picked;
        } else {
          _endTime = picked;
        }
      });
    }
  }

  void _saveShift() {
    if (_formKey.currentState?.validate() ?? false) {
      final startDateTime = DateTime(
        _startDate.year,
        _startDate.month,
        _startDate.day,
        _startTime.hour,
        _startTime.minute,
      );

      final endDateTime = DateTime(
        _endDate.year,
        _endDate.month,
        _endDate.day,
        _endTime.hour,
        _endTime.minute,
      );

      // Validate that end time is after start time
      if (endDateTime.isBefore(startDateTime) || endDateTime.isAtSameMomentAs(startDateTime)) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Čas konca mora biti po času začetka'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // Update required licenses based on event type
      _requiredLicenses = _getRequiredLicensesForEventType(widget.eventType);

      final updatedShift = widget.shift.copyWith(
        startTime: startDateTime,
        endTime: endDateTime,
        requiredGuards: _requiredGuards,
        requiredLicenses: _requiredLicenses,
        notes: _notes.isEmpty ? null : _notes,
        updatedAt: DateTime.now(),
      );

      Navigator.of(context).pop(updatedShift);
    }
  }
}

class _GuardSelectionDialog extends StatefulWidget {
  final List<UserModel> availableGuards;
  final List<String> selectedGuardIds;
  final Function(List<String>) onSelectionChanged;

  const _GuardSelectionDialog({
    required this.availableGuards,
    required this.selectedGuardIds,
    required this.onSelectionChanged,
  });

  @override
  State<_GuardSelectionDialog> createState() => _GuardSelectionDialogState();
}

class _GuardSelectionDialogState extends State<_GuardSelectionDialog> {
  late List<String> _tempSelectedIds;

  @override
  void initState() {
    super.initState();
    _tempSelectedIds = List.from(widget.selectedGuardIds);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.people,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      AppLocale.selectGuards.getString(context),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Guards List
            Expanded(
              child: widget.availableGuards.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.people_outline,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            AppLocale.noUsersFound.getString(context),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: widget.availableGuards.length,
                      itemBuilder: (context, index) {
                        final guard = widget.availableGuards[index];
                        final isSelected = _tempSelectedIds.contains(guard.id);

                        return Card(
                          margin: const EdgeInsets.only(bottom: 8),
                          child: CheckboxListTile(
                            value: isSelected,
                            onChanged: (bool? value) {
                              setState(() {
                                if (value == true) {
                                  _tempSelectedIds.add(guard.id);
                                } else {
                                  _tempSelectedIds.remove(guard.id);
                                }
                              });
                            },
                            title: Text(
                              guard.fullName,
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(guard.email),
                                Text(guard.phone),
                              ],
                            ),
                            secondary: CircleAvatar(
                              backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                              child: Text(
                                guard.firstName[0].toUpperCase(),
                                style: TextStyle(
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),

            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    AppLocale.guardsSelected.getString(context).replaceAll('{count}', _tempSelectedIds.length.toString()),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Row(
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(AppLocale.cancel.getString(context)),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: () {
                          widget.onSelectionChanged(_tempSelectedIds);
                          Navigator.of(context).pop();
                        },
                        child: Text(AppLocale.save.getString(context)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
