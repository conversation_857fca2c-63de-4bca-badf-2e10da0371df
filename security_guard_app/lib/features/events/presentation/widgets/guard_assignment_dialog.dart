import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/models/event_model.dart';
import '../../../../shared/models/event_shift_model.dart';
import '../../../../shared/models/shift_assignment_model.dart';
import '../../../../shared/models/user_model.dart';
import '../../../../shared/services/user_service.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/services/guard_suggestion_service.dart';
import 'guard_time_override_dialog.dart';

class GuardAssignmentDialog extends ConsumerStatefulWidget {
  final EventModel event;

  const GuardAssignmentDialog({
    super.key,
    required this.event,
  });

  @override
  ConsumerState<GuardAssignmentDialog> createState() => _GuardAssignmentDialogState();
}

class _GuardAssignmentDialogState extends ConsumerState<GuardAssignmentDialog> {
  List<UserModel> _availableGuards = [];
  List<UserModel> _filteredGuards = [];
  String _searchQuery = '';
  String? _selectedLicenseFilter;
  EmploymentStatus? _selectedEmploymentFilter;
  bool _showOnlyAvailable = false;
  bool _showSuggestions = true;

  // Track assignments per shift with proper ShiftAssignment objects
  Map<int, List<ShiftAssignment>> _shiftAssignments = {};

  // Guard suggestions per shift
  Map<int, List<GuardSuggestion>> _shiftSuggestions = {};
  bool _loadingSuggestions = false;

  @override
  void initState() {
    super.initState();
    _initializeAssignments();
    _loadAvailableGuards();
  }

  void _initializeAssignments() {
    // Initialize with current assignments from shifts or legacy system
    for (int i = 0; i < widget.event.shifts.length; i++) {
      final shift = widget.event.shifts[i];
      if (shift.assignments.isNotEmpty) {
        // Use existing shift assignments
        _shiftAssignments[i] = List<ShiftAssignment>.from(shift.assignments);
      } else {
        // Convert legacy guard IDs to basic assignments
        _shiftAssignments[i] = widget.event.assignedGuardIds.map((guardId) =>
          ShiftAssignment(
            guardId: guardId,
            startTime: shift.startTime,
            endTime: shift.endTime,
            status: AssignmentStatus.pending, // Start as pending invitation
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          )
        ).toList();
      }
    }
  }

  Future<void> _loadAvailableGuards() async {
    try {
      final userService = ref.read(userServiceProvider);
      final guardsStream = userService.getGuards();

      final guards = await guardsStream.first;
      setState(() {
        _availableGuards = guards.where((guard) => guard.isActive).toList();
        _applyFilters();
      });

      // Load suggestions after guards are loaded
      _loadGuardSuggestions();
    } catch (e) {
      debugPrint('Error loading guards: $e');
      setState(() {
        _availableGuards = [];
        _filteredGuards = [];
      });
    }
  }

  Future<void> _loadGuardSuggestions() async {
    if (!_showSuggestions || widget.event.shifts.isEmpty) return;

    setState(() {
      _loadingSuggestions = true;
    });

    try {
      final suggestionService = ref.read(guardSuggestionServiceProvider);

      for (int i = 0; i < widget.event.shifts.length; i++) {
        final shift = widget.event.shifts[i];
        final suggestions = await suggestionService.getGuardSuggestionsForShift(
          shift: shift,
          event: widget.event,
          availableGuards: _availableGuards,
        );

        _shiftSuggestions[i] = suggestions;
      }

      setState(() {
        _loadingSuggestions = false;
      });
    } catch (e) {
      setState(() {
        _loadingSuggestions = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load suggestions: $e'),
            backgroundColor: AppTheme.warningColor,
          ),
        );
      }
    }
  }

  void _applyFilters() {
    setState(() {
      _filteredGuards = _availableGuards.where((guard) {
        // Search filter
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          if (!guard.fullName.toLowerCase().contains(query) &&
              !guard.email.toLowerCase().contains(query)) {
            return false;
          }
        }

        // License filter (enhanced for Slovenian licenses)
        if (_selectedLicenseFilter != null) {
          bool hasLicense = false;

          // Check Slovenian licenses
          if (_selectedLicenseFilter == 'NPK_GOSTINSKI_LOKALI') {
            hasLicense = guard.hasNpkLicense;
          } else if (_selectedLicenseFilter == 'DELO_NA_OBJEKTU') {
            hasLicense = guard.hasDeloNaObjektuLicense;
          } else {
            // Check legacy license IDs
            hasLicense = guard.licenseIds.contains(_selectedLicenseFilter);
          }

          if (!hasLicense) {
            return false;
          }
        }

        // Employment status filter
        if (_selectedEmploymentFilter != null) {
          if (guard.employmentStatus != _selectedEmploymentFilter) {
            return false;
          }
        }

        // Availability filter (placeholder - would need availability data)
        if (_showOnlyAvailable) {
          // TODO: Implement availability checking
        }

        return true;
      }).toList();
    });
  }

  bool _canAssignToShift(UserModel guard, EventShift shift) {
    // Check if guard has required licenses
    for (String requiredLicense in shift.requiredLicenses) {
      if (!guard.licenseIds.contains(requiredLicense)) {
        return false;
      }
    }
    return true;
  }

  void _assignGuardToShift(String guardId, int shiftIndex) {
    setState(() {
      final existingAssignment = _shiftAssignments[shiftIndex]!
          .where((assignment) => assignment.guardId == guardId)
          .firstOrNull;

      if (existingAssignment == null) {
        final shift = widget.event.shifts[shiftIndex];
        final newAssignment = ShiftAssignment(
          guardId: guardId,
          startTime: shift.startTime,
          endTime: shift.endTime,
          status: AssignmentStatus.pending, // Start as pending invitation
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        _shiftAssignments[shiftIndex]!.add(newAssignment);
      }
    });
  }

  void _removeGuardFromShift(String guardId, int shiftIndex) {
    setState(() {
      _shiftAssignments[shiftIndex]!.removeWhere(
        (assignment) => assignment.guardId == guardId
      );
    });
  }

  void _editGuardAssignment(String guardId, int shiftIndex) async {
    final shift = widget.event.shifts[shiftIndex];
    final existingAssignment = _shiftAssignments[shiftIndex]!
        .where((assignment) => assignment.guardId == guardId)
        .firstOrNull;

    final guard = _availableGuards.firstWhere((g) => g.id == guardId);

    final result = await showDialog<ShiftAssignment>(
      context: context,
      builder: (context) => GuardTimeOverrideDialog(
        shift: shift,
        guard: guard,
        existingAssignment: existingAssignment,
      ),
    );

    if (result != null) {
      setState(() {
        // Remove existing assignment if any
        _shiftAssignments[shiftIndex]!.removeWhere(
          (assignment) => assignment.guardId == guardId
        );
        // Add updated assignment
        _shiftAssignments[shiftIndex]!.add(result);
      });
    }
  }

  Future<void> _saveAssignments() async {
    try {
      // Update shifts with new assignments
      final updatedShifts = <EventShift>[];
      for (int i = 0; i < widget.event.shifts.length; i++) {
        final shift = widget.event.shifts[i];
        final assignments = _shiftAssignments[i] ?? [];

        final updatedShift = shift.copyWith(
          assignments: assignments,
          updatedAt: DateTime.now(),
        );
        updatedShifts.add(updatedShift);
      }

      // Combine all assignments from all shifts (for legacy compatibility)
      final allAssignedGuards = <String>{};
      for (final assignments in _shiftAssignments.values) {
        allAssignedGuards.addAll(assignments.map((a) => a.guardId));
      }

      // Update the event with new assignments and shifts
      final updatedEvent = widget.event.copyWith(
        shifts: updatedShifts,
        assignedGuardIds: allAssignedGuards.toList(),
      );

      await ref.read(eventServiceProvider).updateEvent(updatedEvent);
      
      if (mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocale.guardAssignmentsUpdated.getString(context)),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${AppLocale.failedToUpdateAssignments.getString(context)}: $e'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.9,
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.people,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocale.guardAssignment.getString(context),
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        widget.event.title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Filters
            _buildFiltersSection(),
            
            const SizedBox(height: 16),
            
            // Main content
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Available guards list
                  Expanded(
                    flex: 1,
                    child: _buildAvailableGuardsList(),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // Shifts assignment area
                  Expanded(
                    flex: 2,
                    child: _buildShiftsAssignmentArea(),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(AppLocale.cancel.getString(context)),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _saveAssignments,
                  child: Text(AppLocale.save.getString(context)),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filters',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            
            // Search bar
            TextField(
              decoration: InputDecoration(
                hintText: 'Search guards...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
                _applyFilters();
              },
            ),
            
            const SizedBox(height: 12),
            
            // Filter chips
            Wrap(
              spacing: 8,
              children: [
                FilterChip(
                  label: Text('Full-time only'),
                  selected: _selectedEmploymentFilter == EmploymentStatus.full_time,
                  onSelected: (selected) {
                    setState(() {
                      _selectedEmploymentFilter = selected ? EmploymentStatus.full_time : null;
                    });
                    _applyFilters();
                  },
                ),
                FilterChip(
                  label: Text('Available only'),
                  selected: _showOnlyAvailable,
                  onSelected: (selected) {
                    setState(() {
                      _showOnlyAvailable = selected;
                    });
                    _applyFilters();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableGuardsList() {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.people_outline, color: AppTheme.primaryColor),
                    const SizedBox(width: 8),
                    Text(
                      'Available Guards (${_filteredGuards.length})',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Spacer(),
                    // Suggestions toggle
                    Row(
                      children: [
                        Icon(
                          Icons.auto_awesome,
                          size: 16,
                          color: _showSuggestions ? AppTheme.primaryColor : Colors.grey,
                        ),
                        const SizedBox(width: 4),
                        Switch(
                          value: _showSuggestions,
                          onChanged: (value) {
                            setState(() {
                              _showSuggestions = value;
                            });
                            if (value) {
                              _loadGuardSuggestions();
                            }
                          },
                          activeColor: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                // Enhanced filter controls
                _buildFilterControls(),
              ],
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: ListView.builder(
              itemCount: _filteredGuards.length,
              itemBuilder: (context, index) {
                final guard = _filteredGuards[index];
                return _buildDraggableGuardItem(guard);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterControls() {
    return Column(
      children: [
        // Search field
        TextField(
          decoration: InputDecoration(
            hintText: 'Search guards...',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
            _applyFilters();
          },
        ),
        const SizedBox(height: 8),
        // Filter dropdowns
        Row(
          children: [
            // License filter
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _selectedLicenseFilter,
                decoration: InputDecoration(
                  labelText: 'License',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: [
                  const DropdownMenuItem(value: null, child: Text('All Licenses')),
                  const DropdownMenuItem(value: 'NPK_GOSTINSKI_LOKALI', child: Text('NPK Gostinski')),
                  const DropdownMenuItem(value: 'DELO_NA_OBJEKTU', child: Text('Delo na Objektu')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedLicenseFilter = value;
                  });
                  _applyFilters();
                },
              ),
            ),
            const SizedBox(width: 8),
            // Employment status filter
            Expanded(
              child: DropdownButtonFormField<EmploymentStatus>(
                value: _selectedEmploymentFilter,
                decoration: InputDecoration(
                  labelText: 'Employment',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                items: [
                  const DropdownMenuItem(value: null, child: Text('All Status')),
                  const DropdownMenuItem(value: EmploymentStatus.full_time, child: Text('Full Time')),
                  const DropdownMenuItem(value: EmploymentStatus.custom, child: Text('Custom')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedEmploymentFilter = value;
                  });
                  _applyFilters();
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildShiftsAssignmentArea() {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Icon(Icons.schedule, color: AppTheme.primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Shift Assignments',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: widget.event.shifts.isEmpty
                ? _buildNoShiftsMessage()
                : ListView.builder(
                    itemCount: widget.event.shifts.length,
                    itemBuilder: (context, index) {
                      final shift = widget.event.shifts[index];
                      return _buildShiftAssignmentCard(shift, index);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildDraggableGuardItem(UserModel guard) {
    return Draggable<UserModel>(
      data: guard,
      feedback: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          width: 200,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.9),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.white,
                child: Icon(
                  Icons.person,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  guard.fullName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
      childWhenDragging: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.grey[400],
              child: Icon(
                Icons.person,
                color: Colors.grey[600],
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    guard.fullName,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                  Text(
                    guard.email,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Theme.of(context).dividerColor),
        ),
        child: Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
              child: Icon(
                Icons.person,
                color: AppTheme.primaryColor,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    guard.fullName,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    guard.email,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  // Show Slovenian licenses
                  if (guard.slovenianLicenses.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Wrap(
                      spacing: 4,
                      children: guard.slovenianLicenses.map((license) =>
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppTheme.successColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            license == SlovenianLicenseType.npk_gostinski_lokali ? 'NPK' : 'DELO',
                            style: TextStyle(
                              fontSize: 10,
                              color: AppTheme.successColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ).toList(),
                    ),
                  ],
                  // Show suggestion info if available
                  if (_showSuggestions) _buildGuardSuggestionInfo(guard),
                ],
              ),
            ),
            if (guard.employmentStatus == EmploymentStatus.full_time)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'FT',
                  style: TextStyle(
                    fontSize: 10,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuardSuggestionInfo(UserModel guard) {
    // Find the best suggestion for this guard across all shifts
    GuardSuggestion? bestSuggestion;
    int? bestShiftIndex;

    for (int i = 0; i < widget.event.shifts.length; i++) {
      final suggestions = _shiftSuggestions[i] ?? [];
      final guardSuggestion = suggestions.where((s) => s.guard.id == guard.id).firstOrNull;

      if (guardSuggestion != null) {
        if (bestSuggestion == null || guardSuggestion.matchScore > bestSuggestion.matchScore) {
          bestSuggestion = guardSuggestion;
          bestShiftIndex = i;
        }
      }
    }

    if (bestSuggestion == null) return const SizedBox.shrink();

    final matchScore = bestSuggestion.matchScore;
    final isRecommended = bestSuggestion.isRecommended;

    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        children: [
          Icon(
            isRecommended ? Icons.star : Icons.info_outline,
            size: 12,
            color: isRecommended ? Colors.amber : AppTheme.primaryColor,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              isRecommended
                  ? 'Recommended for Shift ${(bestShiftIndex! + 1)}'
                  : 'Match: ${(matchScore * 100).round()}%',
              style: TextStyle(
                fontSize: 10,
                color: isRecommended ? Colors.amber[700] : AppTheme.primaryColor,
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShiftAssignmentCard(EventShift shift, int shiftIndex) {
    final assignments = _shiftAssignments[shiftIndex] ?? [];
    final isFullyStaffed = assignments.length >= shift.requiredGuards;

    return DragTarget<UserModel>(
      onAccept: (guard) {
        if (_canAssignToShift(guard, shift)) {
          _assignGuardToShift(guard.id, shiftIndex);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocale.guardDoesNotMeetRequirements.getString(context)),
              backgroundColor: AppTheme.errorColor,
            ),
          );
        }
      },
      builder: (context, candidateData, rejectedData) {
        final isHovering = candidateData.isNotEmpty;

        return Container(
          margin: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isHovering
                ? AppTheme.primaryColor.withOpacity(0.1)
                : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isHovering
                  ? AppTheme.primaryColor
                  : isFullyStaffed
                      ? AppTheme.successColor
                      : AppTheme.warningColor,
              width: 2,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Shift header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'Shift ${shiftIndex + 1}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isFullyStaffed
                            ? AppTheme.successColor.withOpacity(0.1)
                            : AppTheme.warningColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${assignments.length}/${shift.requiredGuards}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: isFullyStaffed ? AppTheme.successColor : AppTheme.warningColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // Shift details
                Row(
                  children: [
                    Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${shift.startTime.hour.toString().padLeft(2, '0')}:${shift.startTime.minute.toString().padLeft(2, '0')} - ${shift.endTime.hour.toString().padLeft(2, '0')}:${shift.endTime.minute.toString().padLeft(2, '0')}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),

                if (shift.requiredLicenses.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.verified_user, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          'Requires: ${shift.requiredLicenses.join(', ')}',
                          style: Theme.of(context).textTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 12),

                // Assigned guards
                if (assignments.isEmpty)
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!, style: BorderStyle.solid),
                    ),
                    child: Center(
                      child: Text(
                        AppLocale.dragGuardsHere.getString(context),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  )
                else
                  ...assignments.map((assignment) =>
                    _buildAssignedGuardItem(assignment, shiftIndex)),

                // Show suggestions if enabled and available
                if (_showSuggestions && !_loadingSuggestions)
                  _buildShiftSuggestions(shiftIndex),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAssignedGuardItem(ShiftAssignment assignment, int shiftIndex) {
    return FutureBuilder<UserModel?>(
      future: ref.read(userServiceProvider).getUserById(assignment.guardId),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final guard = snapshot.data!;
        final shift = widget.event.shifts[shiftIndex];
        final hasRequiredLicenses = _canAssignToShift(guard, shift);
        final hasTimeOverride = assignment.startTime != shift.startTime ||
                               assignment.endTime != shift.endTime;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: hasRequiredLicenses
                ? AppTheme.successColor.withOpacity(0.1)
                : AppTheme.errorColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: hasRequiredLicenses
                  ? AppTheme.successColor.withOpacity(0.3)
                  : AppTheme.errorColor.withOpacity(0.3),
            ),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 12,
                backgroundColor: hasRequiredLicenses
                    ? AppTheme.successColor.withOpacity(0.2)
                    : AppTheme.errorColor.withOpacity(0.2),
                child: Icon(
                  hasRequiredLicenses ? Icons.check : Icons.warning,
                  color: hasRequiredLicenses ? AppTheme.successColor : AppTheme.errorColor,
                  size: 12,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            guard.fullName,
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        if (hasTimeOverride)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              'CUSTOM',
                              style: TextStyle(
                                fontSize: 8,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (hasTimeOverride) ...[
                      const SizedBox(height: 2),
                      Text(
                        '${TimeOfDay.fromDateTime(assignment.startTime).format(context)} - ${TimeOfDay.fromDateTime(assignment.endTime).format(context)}',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                    if (!hasRequiredLicenses)
                      Text(
                        'Missing required licenses',
                        style: TextStyle(
                          fontSize: 10,
                          color: AppTheme.errorColor,
                        ),
                      ),
                  ],
                ),
              ),
              // Edit button
              IconButton(
                onPressed: () => _editGuardAssignment(assignment.guardId, shiftIndex),
                icon: Icon(
                  Icons.edit,
                  size: 16,
                  color: AppTheme.primaryColor,
                ),
                constraints: const BoxConstraints(
                  minWidth: 24,
                  minHeight: 24,
                ),
                padding: EdgeInsets.zero,
                tooltip: 'Edit times',
              ),
              // Remove button
              IconButton(
                onPressed: () => _removeGuardFromShift(assignment.guardId, shiftIndex),
                icon: Icon(
                  Icons.close,
                  size: 16,
                  color: AppTheme.errorColor,
                ),
                constraints: const BoxConstraints(
                  minWidth: 24,
                  minHeight: 24,
                ),
                padding: EdgeInsets.zero,
                tooltip: 'Remove guard',
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildShiftSuggestions(int shiftIndex) {
    final suggestions = _shiftSuggestions[shiftIndex] ?? [];
    if (suggestions.isEmpty) return const SizedBox.shrink();

    // Show top 3 recommendations
    final topSuggestions = suggestions.take(3).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 12),
        Row(
          children: [
            Icon(Icons.auto_awesome, size: 14, color: AppTheme.primaryColor),
            const SizedBox(width: 4),
            Text(
              'Suggested Guards',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...topSuggestions.map((suggestion) => _buildSuggestionItem(suggestion, shiftIndex)),
      ],
    );
  }

  Widget _buildSuggestionItem(GuardSuggestion suggestion, int shiftIndex) {
    final guard = suggestion.guard;
    final isRecommended = suggestion.isRecommended;
    final matchScore = suggestion.matchScore;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isRecommended
            ? Colors.amber.withOpacity(0.1)
            : AppTheme.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isRecommended
              ? Colors.amber.withOpacity(0.3)
              : AppTheme.primaryColor.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          // Suggestion indicator
          Icon(
            isRecommended ? Icons.star : Icons.thumb_up_outlined,
            size: 14,
            color: isRecommended ? Colors.amber[700] : AppTheme.primaryColor,
          ),
          const SizedBox(width: 8),
          // Guard info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  guard.fullName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                ),
                Text(
                  '${(matchScore * 100).round()}% match • ${suggestion.reasons.first}',
                  style: TextStyle(
                    fontSize: 9,
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          // Quick assign button
          InkWell(
            onTap: () {
              if (_canAssignToShift(guard, widget.event.shifts[shiftIndex])) {
                _assignGuardToShift(guard.id, shiftIndex);
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                AppLocale.assign.getString(context),
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoShiftsMessage() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No shifts configured',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add shifts to this event to assign guards',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
