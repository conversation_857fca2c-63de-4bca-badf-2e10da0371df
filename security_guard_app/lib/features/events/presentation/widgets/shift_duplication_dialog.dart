import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/models/event_shift_model.dart';
import '../../../../shared/models/shift_assignment_model.dart';

enum DuplicationPattern {
  daily,
  weekly,
  custom,
}

class ShiftDuplicationDialog extends ConsumerStatefulWidget {
  final EventShift sourceShift;
  final int sourceShiftIndex;

  const ShiftDuplicationDialog({
    super.key,
    required this.sourceShift,
    required this.sourceShiftIndex,
  });

  @override
  ConsumerState<ShiftDuplicationDialog> createState() => _ShiftDuplicationDialogState();
}

class _ShiftDuplicationDialogState extends ConsumerState<ShiftDuplicationDialog> {
  DuplicationPattern _selectedPattern = DuplicationPattern.daily;
  int _numberOfCopies = 1;
  int _customIntervalDays = 1;
  bool _copyAssignments = true;
  bool _progressTimes = false;
  int _timeProgressionHours = 0;
  
  final TextEditingController _numberOfCopiesController = TextEditingController(text: '1');
  final TextEditingController _customIntervalController = TextEditingController(text: '1');
  final TextEditingController _timeProgressionController = TextEditingController(text: '0');

  @override
  void initState() {
    super.initState();
    _numberOfCopiesController.addListener(_updateNumberOfCopies);
    _customIntervalController.addListener(_updateCustomInterval);
    _timeProgressionController.addListener(_updateTimeProgression);
  }

  void _updateNumberOfCopies() {
    final value = int.tryParse(_numberOfCopiesController.text) ?? 1;
    setState(() {
      _numberOfCopies = value.clamp(1, 30);
    });
  }

  void _updateCustomInterval() {
    final value = int.tryParse(_customIntervalController.text) ?? 1;
    setState(() {
      _customIntervalDays = value.clamp(1, 365);
    });
  }

  void _updateTimeProgression() {
    final value = int.tryParse(_timeProgressionController.text) ?? 0;
    setState(() {
      _timeProgressionHours = value.clamp(-12, 12);
    });
  }

  int get _intervalDays {
    switch (_selectedPattern) {
      case DuplicationPattern.daily:
        return 1;
      case DuplicationPattern.weekly:
        return 7;
      case DuplicationPattern.custom:
        return _customIntervalDays;
    }
  }

  List<EventShift> _generateDuplicatedShifts() {
    final duplicatedShifts = <EventShift>[];
    
    for (int i = 1; i <= _numberOfCopies; i++) {
      final dayOffset = _intervalDays * i;
      final timeOffset = _progressTimes ? Duration(hours: _timeProgressionHours * i) : Duration.zero;
      
      final newStartTime = widget.sourceShift.startTime
          .add(Duration(days: dayOffset))
          .add(timeOffset);
      final newEndTime = widget.sourceShift.endTime
          .add(Duration(days: dayOffset))
          .add(timeOffset);

      // Copy assignments if requested
      final newAssignments = _copyAssignments
          ? widget.sourceShift.assignments.map((assignment) =>
              assignment.copyWith(
                startTime: assignment.startTime
                    .add(Duration(days: dayOffset))
                    .add(timeOffset),
                endTime: assignment.endTime
                    .add(Duration(days: dayOffset))
                    .add(timeOffset),
                createdAt: DateTime.now(),
                updatedAt: DateTime.now(),
              )
            ).toList()
          : <ShiftAssignment>[];

      final duplicatedShift = EventShift(
        id: '${widget.sourceShift.id}_copy_$i',
        startTime: newStartTime,
        endTime: newEndTime,
        requiredGuards: widget.sourceShift.requiredGuards,
        requiredLicenses: List<String>.from(widget.sourceShift.requiredLicenses),
        assignments: newAssignments,
        notes: widget.sourceShift.notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      duplicatedShifts.add(duplicatedShift);
    }

    return duplicatedShifts;
  }

  @override
  void dispose() {
    _numberOfCopiesController.dispose();
    _customIntervalController.dispose();
    _timeProgressionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final duplicatedShifts = _generateDuplicatedShifts();
    
    return Dialog(
      child: Container(
        width: 600,
        height: 700,
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.content_copy,
                  color: AppTheme.primaryColor,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Duplicate Shift',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Shift ${widget.sourceShiftIndex + 1}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Source shift info
            _buildSourceShiftCard(),
            
            const SizedBox(height: 24),
            
            // Duplication options
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPatternSelection(),
                    const SizedBox(height: 16),
                    _buildNumberOfCopies(),
                    const SizedBox(height: 16),
                    _buildOptionsSection(),
                    const SizedBox(height: 24),
                    _buildPreviewSection(duplicatedShifts),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(AppLocale.cancel.getString(context)),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(duplicatedShifts),
                  child: Text('Create ${duplicatedShifts.length} Shifts'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSourceShiftCard() {
    return Card(
      color: AppTheme.primaryColor.withOpacity(0.05),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Source Shift',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  '${TimeOfDay.fromDateTime(widget.sourceShift.startTime).format(context)} - ${TimeOfDay.fromDateTime(widget.sourceShift.endTime).format(context)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${widget.sourceShift.requiredGuards} guards',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ),
              ],
            ),
            
            if (widget.sourceShift.requiredLicenses.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.verified_user, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Requires: ${widget.sourceShift.requiredLicenses.join(', ')}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            ],
            
            if (widget.sourceShift.assignments.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.people, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    '${widget.sourceShift.assignments.length} guards assigned',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPatternSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Duplication Pattern',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        Column(
          children: [
            RadioListTile<DuplicationPattern>(
              title: const Text('Daily'),
              subtitle: const Text('Create shifts for consecutive days'),
              value: DuplicationPattern.daily,
              groupValue: _selectedPattern,
              onChanged: (value) {
                setState(() {
                  _selectedPattern = value!;
                });
              },
              activeColor: AppTheme.primaryColor,
            ),
            RadioListTile<DuplicationPattern>(
              title: const Text('Weekly'),
              subtitle: const Text('Create shifts for the same day each week'),
              value: DuplicationPattern.weekly,
              groupValue: _selectedPattern,
              onChanged: (value) {
                setState(() {
                  _selectedPattern = value!;
                });
              },
              activeColor: AppTheme.primaryColor,
            ),
            RadioListTile<DuplicationPattern>(
              title: const Text('Custom Interval'),
              subtitle: Text('Create shifts every $_customIntervalDays days'),
              value: DuplicationPattern.custom,
              groupValue: _selectedPattern,
              onChanged: (value) {
                setState(() {
                  _selectedPattern = value!;
                });
              },
              activeColor: AppTheme.primaryColor,
            ),
          ],
        ),

        if (_selectedPattern == DuplicationPattern.custom) ...[
          const SizedBox(height: 12),
          Row(
            children: [
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _customIntervalController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Days between shifts',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              const Text('days'),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildNumberOfCopies() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Number of Copies',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _numberOfCopiesController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: 'Number of shifts to create',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '$_numberOfCopies shifts',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOptionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Options',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),

        Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('Copy Guard Assignments'),
                  subtitle: Text(
                    _copyAssignments
                        ? 'Assigned guards will be copied to new shifts'
                        : 'New shifts will have no guard assignments',
                  ),
                  value: _copyAssignments,
                  onChanged: (value) {
                    setState(() {
                      _copyAssignments = value;
                    });
                  },
                  activeColor: AppTheme.primaryColor,
                ),

                const Divider(),

                SwitchListTile(
                  title: const Text('Progressive Time Adjustment'),
                  subtitle: Text(
                    _progressTimes
                        ? 'Each shift will be adjusted by $_timeProgressionHours hours'
                        : 'All shifts will have the same time',
                  ),
                  value: _progressTimes,
                  onChanged: (value) {
                    setState(() {
                      _progressTimes = value;
                    });
                  },
                  activeColor: AppTheme.primaryColor,
                ),

                if (_progressTimes) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      const SizedBox(width: 16),
                      Expanded(
                        child: TextField(
                          controller: _timeProgressionController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            labelText: 'Hours adjustment per shift',
                            helperText: 'Positive = later, Negative = earlier',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Text('hours'),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPreviewSection(List<EventShift> duplicatedShifts) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Preview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${duplicatedShifts.length} new shifts',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.successColor,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: duplicatedShifts.isEmpty
              ? const Center(
                  child: Text(
                    'No shifts to preview',
                    style: TextStyle(color: Colors.grey),
                  ),
                )
              : ListView.builder(
                  itemCount: duplicatedShifts.length,
                  itemBuilder: (context, index) {
                    final shift = duplicatedShifts[index];
                    return ListTile(
                      dense: true,
                      leading: CircleAvatar(
                        radius: 12,
                        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                        child: Text(
                          '${index + 1}',
                          style: TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ),
                      title: Text(
                        '${TimeOfDay.fromDateTime(shift.startTime).format(context)} - ${TimeOfDay.fromDateTime(shift.endTime).format(context)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      subtitle: Text(
                        '${shift.startTime.day}/${shift.startTime.month}/${shift.startTime.year}',
                        style: const TextStyle(fontSize: 12),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (shift.assignments.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: AppTheme.successColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '${shift.assignments.length} assigned',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: AppTheme.successColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }
}
