import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/services/migration_service.dart';

class MigrationPage extends ConsumerStatefulWidget {
  const MigrationPage({super.key});

  @override
  ConsumerState<MigrationPage> createState() => _MigrationPageState();
}

class _MigrationPageState extends ConsumerState<MigrationPage> {
  bool _isLoading = false;
  bool _migrationNeeded = false;
  String _statusMessage = '';
  List<String> _migrationLogs = [];

  @override
  void initState() {
    super.initState();
    _checkMigrationStatus();
  }

  Future<void> _checkMigrationStatus() async {
    setState(() {
      _isLoading = true;
      _statusMessage = 'Checking migration status...';
    });

    try {
      final migrationService = ref.read(migrationServiceProvider);
      final needed = await migrationService.isMigrationNeeded();
      
      setState(() {
        _migrationNeeded = needed;
        _statusMessage = needed 
            ? 'Migration required: Your database needs to be updated to support the new Event & Shift System.'
            : 'Migration complete: Your database is up to date with the latest Event & Shift System.';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _statusMessage = 'Error checking migration status: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _runMigration() async {
    if (!_migrationNeeded) return;

    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog();
    if (!confirmed) return;

    setState(() {
      _isLoading = true;
      _statusMessage = 'Running migration...';
      _migrationLogs.clear();
    });

    try {
      final migrationService = ref.read(migrationServiceProvider);
      
      // Create backup first
      _addLog('Creating backup of existing data...');
      await migrationService.createBackup();
      _addLog('✅ Backup created successfully');
      
      // Run migrations
      _addLog('Starting event migration...');
      await migrationService.migrateEventsToShiftSystem();
      _addLog('✅ Events migrated successfully');
      
      _addLog('Starting user migration...');
      await migrationService.migrateUsersToEnhancedModel();
      _addLog('✅ Users migrated successfully');
      
      _addLog('🎉 Migration completed successfully!');
      
      setState(() {
        _migrationNeeded = false;
        _statusMessage = 'Migration completed successfully! Your database now supports the enhanced Event & Shift System.';
        _isLoading = false;
      });
      
      // Show success dialog
      _showSuccessDialog();
      
    } catch (e) {
      _addLog('❌ Migration failed: $e');
      setState(() {
        _statusMessage = 'Migration failed: $e';
        _isLoading = false;
      });
      
      // Show error dialog
      _showErrorDialog(e.toString());
    }
  }

  void _addLog(String message) {
    setState(() {
      _migrationLogs.add('${DateTime.now().toLocal().toString().substring(11, 19)}: $message');
    });
  }

  Future<bool> _showConfirmationDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Migration'),
        content: const Text(
          'This will migrate your database to support the new Event & Shift System. '
          'A backup will be created before the migration starts.\n\n'
          'This process may take a few minutes depending on your data size.\n\n'
          'Do you want to continue?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(AppLocale.cancel.getString(context)),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Start Migration'),
          ),
        ],
      ),
    ) ?? false;
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Migration Successful'),
        content: const Text(
          'Your database has been successfully migrated to support the new Event & Shift System!\n\n'
          'New features available:\n'
          '• Multi-shift events\n'
          '• Guard response system\n'
          '• License management\n'
          '• Employment status tracking\n'
          '• Enhanced scheduling'
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocale.ok.getString(context)),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Migration Failed'),
        content: Text(
          'The migration process encountered an error:\n\n$error\n\n'
          'Your original data is safe. You can try running the migration again or contact support.'
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocale.ok.getString(context)),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: 'Database Migration',
      currentIndex: -1,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          _migrationNeeded ? Icons.warning : Icons.check_circle,
                          color: _migrationNeeded ? Colors.orange : Colors.green,
                          size: 32,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Event & Shift System Migration',
                                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                _statusMessage,
                                style: Theme.of(context).textTheme.bodyLarge,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    if (_migrationNeeded) ...[
                      const SizedBox(height: 20),
                      ElevatedButton.icon(
                        onPressed: _isLoading ? null : _runMigration,
                        icon: _isLoading 
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.upgrade),
                        label: Text(_isLoading ? 'Running Migration...' : 'Start Migration'),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Migration Details Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'What\'s New in the Enhanced System',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildFeatureItem(
                      icon: Icons.schedule,
                      title: 'Multi-Shift Events',
                      description: 'Events can now have multiple shifts with different time ranges and guard requirements.',
                    ),
                    _buildFeatureItem(
                      icon: Icons.how_to_reg,
                      title: 'Guard Response System',
                      description: 'Guards can express interest or unavailability for specific shifts.',
                    ),
                    _buildFeatureItem(
                      icon: Icons.badge,
                      title: 'License Management',
                      description: 'Track guard licenses and certifications with expiry monitoring.',
                    ),
                    _buildFeatureItem(
                      icon: Icons.work,
                      title: 'Employment Status',
                      description: 'Manage full-time, part-time, and contractor guards with different priorities.',
                    ),
                    _buildFeatureItem(
                      icon: Icons.event_note,
                      title: 'Event Types',
                      description: 'Categorize events as venue, event, or local with specific license requirements.',
                    ),
                  ],
                ),
              ),
            ),
            
            // Migration Logs
            if (_migrationLogs.isNotEmpty) ...[
              const SizedBox(height: 24),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Migration Log',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        height: 200,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          border: Border.all(color: Theme.of(context).dividerColor),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ListView.builder(
                          itemCount: _migrationLogs.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                _migrationLogs[index],
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontFamily: 'monospace',
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
