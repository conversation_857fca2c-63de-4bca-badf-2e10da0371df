import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/services/migration_service.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/services/user_service.dart';

class AdminPage extends ConsumerStatefulWidget {
  const AdminPage({super.key});

  @override
  ConsumerState<AdminPage> createState() => _AdminPageState();
}

class _AdminPageState extends ConsumerState<AdminPage> {
  bool _migrationNeeded = false;
  bool _isCheckingMigration = true;
  bool _isLoadingStats = true;

  // Dashboard statistics
  int _totalEvents = 0;
  int _activeEvents = 0;
  int _totalGuards = 0;
  int _activeGuards = 0;
  int _pendingEvents = 0;
  int _completedEvents = 0;

  @override
  void initState() {
    super.initState();
    _checkMigrationStatus();
    _loadDashboardStats();
  }

  Future<void> _checkMigrationStatus() async {
    try {
      final migrationService = ref.read(migrationServiceProvider);
      final needed = await migrationService.isMigrationNeeded();
      setState(() {
        _migrationNeeded = needed;
        _isCheckingMigration = false;
      });
    } catch (e) {
      setState(() {
        _isCheckingMigration = false;
      });
    }
  }

  Future<void> _loadDashboardStats() async {
    try {
      final eventService = ref.read(eventServiceProvider);
      final userService = ref.read(userServiceProvider);

      // Load events using stream (take first emission)
      final eventsStream = eventService.getEvents();
      final events = await eventsStream.first;

      // Load guards using stream (take first emission)
      final guardsStream = userService.getGuards();
      final guards = await guardsStream.first;

      setState(() {
        _totalEvents = events.length;
        _activeEvents = events.where((e) => e.status == 'confirmed' || e.status == 'pending').length;
        _pendingEvents = events.where((e) => e.status == 'pending').length;
        _completedEvents = events.where((e) => e.status == 'completed').length;

        _totalGuards = guards.length;
        _activeGuards = guards.where((g) => g.isActive).length;

        _isLoadingStats = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingStats = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: AppLocale.adminPanel.getString(context),
      currentIndex: 5,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Migration Status Card
            if (_isCheckingMigration)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Checking system status...'),
                    ],
                  ),
                ),
              )
            else if (_migrationNeeded)
              Card(
                color: Colors.orange.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.warning,
                            color: Colors.orange.shade700,
                            size: 32,
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Database Migration Required',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange.shade700,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'Your database needs to be updated to support the new Event & Shift System features.',
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () => context.go('/migration'),
                        icon: const Icon(Icons.upgrade),
                        label: const Text('Run Migration'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange.shade700,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            const SizedBox(height: 24),

            // Dashboard Statistics
            Text(
              'System Overview',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            if (_isLoadingStats)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Loading system statistics...'),
                    ],
                  ),
                ),
              )
            else
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: MediaQuery.of(context).size.width > 800 ? 4 : 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 1.5,
                children: [
                  _buildStatCard(
                    title: 'Total Events',
                    value: _totalEvents.toString(),
                    icon: Icons.event,
                    color: Colors.blue,
                  ),
                  _buildStatCard(
                    title: 'Active Events',
                    value: _activeEvents.toString(),
                    icon: Icons.event_available,
                    color: Colors.green,
                  ),
                  _buildStatCard(
                    title: 'Total Guards',
                    value: _totalGuards.toString(),
                    icon: Icons.people,
                    color: Colors.purple,
                  ),
                  _buildStatCard(
                    title: 'Active Guards',
                    value: _activeGuards.toString(),
                    icon: Icons.person_add,
                    color: Colors.teal,
                  ),
                  _buildStatCard(
                    title: 'Pending Events',
                    value: _pendingEvents.toString(),
                    icon: Icons.pending,
                    color: Colors.orange,
                  ),
                  _buildStatCard(
                    title: 'Completed Events',
                    value: _completedEvents.toString(),
                    icon: Icons.check_circle,
                    color: Colors.green.shade700,
                  ),
                ],
              ),

            const SizedBox(height: 32),

            // Admin Tools Grid
            Text(
              'Admin Tools',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: MediaQuery.of(context).size.width > 600 ? 3 : 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildAdminTool(
                  icon: Icons.upgrade,
                  title: 'Database Migration',
                  description: 'Upgrade to Event & Shift System',
                  onTap: () => context.go('/migration'),
                  color: _migrationNeeded ? Colors.orange : Colors.green,
                ),
                _buildAdminTool(
                  icon: Icons.people,
                  title: 'User Management',
                  description: 'Manage guards and admins',
                  onTap: () => context.go('/guards'),
                  color: Colors.blue,
                ),
                _buildAdminTool(
                  icon: Icons.event,
                  title: 'Event Management',
                  description: 'Create and manage events',
                  onTap: () => context.go('/events'),
                  color: Colors.purple,
                ),
                _buildAdminTool(
                  icon: Icons.analytics,
                  title: 'Reports & Analytics',
                  description: 'View performance reports',
                  onTap: () => context.go('/reports'),
                  color: Colors.teal,
                ),
                _buildAdminTool(
                  icon: Icons.settings,
                  title: 'Settings',
                  description: 'App configuration',
                  onTap: () => context.go('/settings'),
                  color: Colors.grey,
                ),
                _buildAdminTool(
                  icon: Icons.backup,
                  title: 'Data Backup',
                  description: 'Coming soon',
                  onTap: () => _showComingSoonDialog('Data Backup'),
                  color: Colors.indigo,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdminTool({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 48,
                color: color,
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showComingSoonDialog(String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocale.comingSoon.getString(context)),
        content: Text('$feature ${AppLocale.comingSoonDesc.getString(context)}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocale.ok.getString(context)),
          ),
        ],
      ),
    );
  }
}
