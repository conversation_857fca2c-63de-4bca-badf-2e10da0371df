import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../shared/models/user_model.dart';
import '../../../../shared/services/user_service.dart';

class UserCreationDialog extends ConsumerStatefulWidget {
  final UserModel? user; // null for create, non-null for edit

  const UserCreationDialog({
    super.key,
    this.user,
  });

  @override
  ConsumerState<UserCreationDialog> createState() => _UserCreationDialogState();
}

class _UserCreationDialogState extends ConsumerState<UserCreationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emsoController = TextEditingController();
  final _customHoursController = TextEditingController();

  String _selectedRole = AppConstants.roleGuard;
  bool _isActive = true;
  bool _isLoading = false;

  // Enhanced Slovenian-specific fields
  EmploymentStatus? _selectedEmploymentStatus;
  Set<SlovenianLicenseType> _selectedSlovenianLicenses = {};
  int? _customWorkingHours;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.user != null) {
      final user = widget.user!;
      _firstNameController.text = user.firstName;
      _lastNameController.text = user.lastName;
      _emailController.text = user.email;
      _phoneController.text = user.phone;
      _selectedRole = user.role;
      _isActive = user.isActive;

      // Initialize Slovenian-specific fields
      _selectedEmploymentStatus = user.employmentStatus;
      _customWorkingHours = user.customWorkingHours;
      if (user.customWorkingHours != null) {
        _customHoursController.text = user.customWorkingHours.toString();
      }
      if (user.emso != null) {
        _emsoController.text = user.emso!;
      }
      _selectedSlovenianLicenses = Set.from(user.slovenianLicenses);
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    _emsoController.dispose();
    _customHoursController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.user != null;
    
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isEditing ? Icons.edit : Icons.person_add,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      isEditing 
                        ? AppLocale.editUser.getString(context)
                        : AppLocale.createUser.getString(context),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // Form Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // First Name
                      _buildTextField(
                        controller: _firstNameController,
                        label: AppLocale.firstName.getString(context),
                        icon: Icons.person,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return AppLocale.firstNameRequired.getString(context);
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Last Name
                      _buildTextField(
                        controller: _lastNameController,
                        label: AppLocale.lastName.getString(context),
                        icon: Icons.person_outline,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return AppLocale.lastNameRequired.getString(context);
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Email
                      _buildTextField(
                        controller: _emailController,
                        label: AppLocale.email.getString(context),
                        icon: Icons.email,
                        keyboardType: TextInputType.emailAddress,
                        enabled: !isEditing, // Don't allow email editing
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return AppLocale.emailRequired.getString(context);
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return AppLocale.invalidEmail.getString(context);
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Phone
                      _buildTextField(
                        controller: _phoneController,
                        label: AppLocale.phone.getString(context),
                        icon: Icons.phone,
                        keyboardType: TextInputType.phone,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return AppLocale.phoneRequired.getString(context);
                          }
                          if (!RegExp(r'^\+?[\d\s\-\(\)]+$').hasMatch(value)) {
                            return AppLocale.invalidPhone.getString(context);
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // Password (only for new users)
                      if (!isEditing) ...[
                        TextFormField(
                          controller: _passwordController,
                          obscureText: _obscurePassword,
                          decoration: InputDecoration(
                            labelText: AppLocale.password.getString(context),
                            prefixIcon: const Icon(Icons.lock),
                            suffixIcon: IconButton(
                              icon: Icon(
                                _obscurePassword ? Icons.visibility : Icons.visibility_off,
                              ),
                              onPressed: () {
                                setState(() {
                                  _obscurePassword = !_obscurePassword;
                                });
                              },
                            ),
                            border: const OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Password is required';
                            }
                            if (value.length < 6) {
                              return 'Password must be at least 6 characters';
                            }
                            return null;
                          },
                        ),
                        
                        const SizedBox(height: 16),
                      ],
                      
                      // Role Selection
                      DropdownButtonFormField<String>(
                        value: _selectedRole,
                        decoration: InputDecoration(
                          labelText: AppLocale.role.getString(context),
                          prefixIcon: const Icon(Icons.badge),
                          border: const OutlineInputBorder(),
                        ),
                        items: [
                          DropdownMenuItem(
                            value: AppConstants.roleGuard,
                            child: Text(AppLocale.guard.getString(context)),
                          ),
                          DropdownMenuItem(
                            value: AppConstants.roleAdmin,
                            child: Text(AppLocale.admin.getString(context)),
                          ),
                          DropdownMenuItem(
                            value: AppConstants.roleSuperAdmin,
                            child: Text(AppLocale.superAdmin.getString(context)),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedRole = value;
                            });
                          }
                        },
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return AppLocale.roleRequired.getString(context);
                          }
                          return null;
                        },
                      ),
                      
                      const SizedBox(height: 16),

                      // Slovenian-specific fields for guards
                      if (_selectedRole == AppConstants.roleGuard) ...[
                        // EMŠO Field
                        _buildTextField(
                          controller: _emsoController,
                          label: AppLocale.emsoLabel.getString(context),
                          icon: Icons.credit_card,
                          keyboardType: TextInputType.number,
                          validator: (value) {
                            if (value != null && value.isNotEmpty) {
                              if (value.length != 13 || !RegExp(r'^\d{13}$').hasMatch(value)) {
                                return AppLocale.emsoValidation.getString(context);
                              }
                            }
                            return null;
                          },
                        ),

                        const SizedBox(height: 16),

                        // Employment Status
                        DropdownButtonFormField<EmploymentStatus>(
                          value: _selectedEmploymentStatus,
                          decoration: InputDecoration(
                            labelText: AppLocale.employmentStatusEnhanced.getString(context),
                            prefixIcon: const Icon(Icons.work),
                            border: const OutlineInputBorder(),
                          ),
                          items: [
                            DropdownMenuItem(
                              value: EmploymentStatus.full_time,
                              child: Text(AppLocale.fullTime40h.getString(context)),
                            ),
                            DropdownMenuItem(
                              value: EmploymentStatus.custom,
                              child: Text(AppLocale.customHours.getString(context)),
                            ),
                            DropdownMenuItem(
                              value: EmploymentStatus.inactive,
                              child: Text(AppLocale.inactive.getString(context)),
                            ),
                          ],
                          onChanged: (value) {
                            setState(() {
                              _selectedEmploymentStatus = value;
                              if (value != EmploymentStatus.custom) {
                                _customWorkingHours = null;
                                _customHoursController.clear();
                              }
                            });
                          },
                        ),

                        const SizedBox(height: 16),

                        // Custom Hours (only if custom employment status)
                        if (_selectedEmploymentStatus == EmploymentStatus.custom) ...[
                          _buildTextField(
                            controller: _customHoursController,
                            label: AppLocale.customHoursLabel.getString(context),
                            icon: Icons.schedule,
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (_selectedEmploymentStatus == EmploymentStatus.custom) {
                                if (value == null || value.isEmpty) {
                                  return 'Custom hours are required';
                                }
                                final hours = int.tryParse(value);
                                if (hours == null || hours < 1 || hours > 60) {
                                  return AppLocale.customHoursValidation.getString(context);
                                }
                              }
                              return null;
                            },
                          ),

                          const SizedBox(height: 16),
                        ],

                        // Slovenian Licenses Section
                        _buildSlovenianLicensesSection(),

                        const SizedBox(height: 16),
                      ],

                      // Active Status (only for editing)
                      if (isEditing) ...[
                        SwitchListTile(
                          title: Text(AppLocale.active.getString(context)),
                          subtitle: Text(
                            _isActive 
                                ? 'User can sign in and access the system'
                                : 'User is disabled and cannot sign in',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          value: _isActive,
                          onChanged: (value) {
                            setState(() {
                              _isActive = value;
                            });
                          },
                          activeColor: AppTheme.primaryColor,
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            
            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(AppLocale.cancel.getString(context)),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _handleSubmit,
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : Text(
                            isEditing 
                              ? AppLocale.update.getString(context)
                              : AppLocale.create.getString(context),
                          ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
    bool enabled = true,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      enabled: enabled,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
        disabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      validator: validator,
    );
  }

  Widget _buildSlovenianLicensesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocale.slovenianLicenses.getString(context),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          AppLocale.selectLicenses.getString(context),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
        ),
        const SizedBox(height: 12),

        // NPK GOSTINSKI LOKALI License
        CheckboxListTile(
          title: Text(AppLocale.npkGostinskiLokali.getString(context)),
          subtitle: Text(AppLocale.npkGostinskiLokaliDesc.getString(context)),
          value: _selectedSlovenianLicenses.contains(SlovenianLicenseType.npk_gostinski_lokali),
          onChanged: (bool? value) {
            setState(() {
              if (value == true) {
                _selectedSlovenianLicenses.add(SlovenianLicenseType.npk_gostinski_lokali);
              } else {
                _selectedSlovenianLicenses.remove(SlovenianLicenseType.npk_gostinski_lokali);
              }
            });
          },
          activeColor: AppTheme.primaryColor,
          contentPadding: EdgeInsets.zero,
        ),

        // DELO NA OBJEKTU License
        CheckboxListTile(
          title: Text(AppLocale.deloNaObjektu.getString(context)),
          subtitle: Text(AppLocale.deloNaObjektuDesc.getString(context)),
          value: _selectedSlovenianLicenses.contains(SlovenianLicenseType.delo_na_objektu),
          onChanged: (bool? value) {
            setState(() {
              if (value == true) {
                _selectedSlovenianLicenses.add(SlovenianLicenseType.delo_na_objektu);
              } else {
                _selectedSlovenianLicenses.remove(SlovenianLicenseType.delo_na_objektu);
              }
            });
          },
          activeColor: AppTheme.primaryColor,
          contentPadding: EdgeInsets.zero,
        ),

        if (_selectedSlovenianLicenses.isEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Text(
              AppLocale.noLicensesSelected.getString(context),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final userService = ref.read(userServiceProvider);
      final isEditing = widget.user != null;

      if (isEditing) {
        // Parse custom working hours if needed
        int? customHours;
        if (_selectedEmploymentStatus == EmploymentStatus.custom && _customHoursController.text.isNotEmpty) {
          customHours = int.tryParse(_customHoursController.text);
        }

        // Update existing user
        final updatedUser = widget.user!.copyWith(
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          phone: _phoneController.text.trim(),
          role: _selectedRole,
          isActive: _isActive,
          employmentStatus: _selectedEmploymentStatus,
          customWorkingHours: customHours,
          emso: _emsoController.text.trim().isEmpty ? null : _emsoController.text.trim(),
          slovenianLicenses: _selectedSlovenianLicenses.toList(),
          updatedAt: DateTime.now(),
        );

        await userService.updateUser(updatedUser);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocale.userUpdatedSuccess.getString(context)),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      } else {
        // Parse custom working hours if needed
        int? customHours;
        if (_selectedEmploymentStatus == EmploymentStatus.custom && _customHoursController.text.isNotEmpty) {
          customHours = int.tryParse(_customHoursController.text);
        }

        // Create new user
        await userService.createUser(
          email: _emailController.text.trim(),
          password: _passwordController.text,
          firstName: _firstNameController.text.trim(),
          lastName: _lastNameController.text.trim(),
          phone: _phoneController.text.trim(),
          role: _selectedRole,
          companyId: 'default-company', // TODO: Get from current user's company
          employmentStatus: _selectedEmploymentStatus,
          customWorkingHours: customHours,
          emso: _emsoController.text.trim().isEmpty ? null : _emsoController.text.trim(),
          slovenianLicenses: _selectedSlovenianLicenses.toList(),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(AppLocale.userCreatedSuccess.getString(context)),
              backgroundColor: AppTheme.successColor,
            ),
          );
        }
      }

      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      debugPrint('Error saving user: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(AppLocale.userCreationFailed.getString(context)),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
