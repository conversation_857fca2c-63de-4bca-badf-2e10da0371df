import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/models/user_model.dart';

class UserDetailsDialog extends ConsumerWidget {
  final UserModel user;

  const UserDetailsDialog({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: AppTheme.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.white.withOpacity(0.2),
                    child: Icon(
                      _getRoleIcon(user.role),
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.fullName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _getRoleDisplayName(context, user.role),
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.9),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: user.isActive 
                          ? Colors.green.withOpacity(0.2)
                          : Colors.red.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: user.isActive ? Colors.green : Colors.red,
                        width: 1,
                      ),
                    ),
                    child: Text(
                      user.isActive 
                          ? AppLocale.active.getString(context)
                          : AppLocale.inactive.getString(context),
                      style: TextStyle(
                        color: user.isActive ? Colors.green : Colors.red,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),
            
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic Information
                    _buildSection(
                      context,
                      title: 'Basic Information',
                      children: [
                        _buildDetailRow(
                          context,
                          icon: Icons.email,
                          label: AppLocale.email.getString(context),
                          value: user.email,
                        ),
                        _buildDetailRow(
                          context,
                          icon: Icons.phone,
                          label: AppLocale.phone.getString(context),
                          value: user.phone,
                        ),
                        _buildDetailRow(
                          context,
                          icon: Icons.badge,
                          label: AppLocale.role.getString(context),
                          value: _getRoleDisplayName(context, user.role),
                          valueColor: _getRoleColor(user.role),
                        ),
                        if (user.companyId != null)
                          _buildDetailRow(
                            context,
                            icon: Icons.business,
                            label: AppLocale.company.getString(context),
                            value: user.companyId!,
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Account Information
                    _buildSection(
                      context,
                      title: 'Account Information',
                      children: [
                        _buildDetailRow(
                          context,
                          icon: Icons.calendar_today,
                          label: AppLocale.createdDate.getString(context),
                          value: DateFormat('dd/MM/yyyy HH:mm').format(user.createdAt),
                        ),
                        if (user.updatedAt != null)
                          _buildDetailRow(
                            context,
                            icon: Icons.update,
                            label: 'Last Updated',
                            value: DateFormat('dd/MM/yyyy HH:mm').format(user.updatedAt!),
                          ),
                        _buildDetailRow(
                          context,
                          icon: Icons.info,
                          label: AppLocale.status.getString(context),
                          value: user.isActive 
                              ? AppLocale.active.getString(context)
                              : AppLocale.inactive.getString(context),
                          valueColor: user.isActive ? AppTheme.successColor : AppTheme.errorColor,
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Permissions
                    _buildSection(
                      context,
                      title: AppLocale.permissions.getString(context),
                      children: [
                        _buildPermissionRow(
                          context,
                          label: AppLocale.canManageUsers.getString(context),
                          hasPermission: user.canManageUsers,
                        ),
                        _buildPermissionRow(
                          context,
                          label: AppLocale.canCreateEvents.getString(context),
                          hasPermission: user.canCreateEvents,
                        ),
                        _buildPermissionRow(
                          context,
                          label: AppLocale.canViewReports.getString(context),
                          hasPermission: user.canViewReports,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(AppLocale.cancel.getString(context)),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).pop();
                      // TODO: Open edit dialog
                    },
                    icon: const Icon(Icons.edit),
                    label: Text(AppLocale.editUser.getString(context)),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: valueColor ?? Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionRow(
    BuildContext context, {
    required String label,
    required bool hasPermission,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            hasPermission ? Icons.check_circle : Icons.cancel,
            size: 20,
            color: hasPermission ? AppTheme.successColor : AppTheme.errorColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
              ),
            ),
          ),
          Text(
            hasPermission ? 'Yes' : 'No',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: hasPermission ? AppTheme.successColor : AppTheme.errorColor,
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Color _getRoleColor(String role) {
    switch (role) {
      case 'superadmin':
        return AppTheme.errorColor;
      case 'admin':
        return AppTheme.primaryColor;
      case 'guard':
        return AppTheme.successColor;
      default:
        return Colors.grey;
    }
  }

  IconData _getRoleIcon(String role) {
    switch (role) {
      case 'superadmin':
        return Icons.admin_panel_settings;
      case 'admin':
        return Icons.manage_accounts;
      case 'guard':
        return Icons.security;
      default:
        return Icons.person;
    }
  }

  String _getRoleDisplayName(BuildContext context, String role) {
    switch (role) {
      case 'superadmin':
        return AppLocale.superAdmin.getString(context);
      case 'admin':
        return AppLocale.admin.getString(context);
      case 'guard':
        return AppLocale.guard.getString(context);
      default:
        return role;
    }
  }
}
