import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/services/user_service.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/models/user_model.dart';
import '../../../../shared/models/event_model.dart';

class UserDetailPage extends ConsumerStatefulWidget {
  final String userId;

  const UserDetailPage({
    super.key,
    required this.userId,
  });

  @override
  ConsumerState<UserDetailPage> createState() => _UserDetailPageState();
}

class _UserDetailPageState extends ConsumerState<UserDetailPage> {
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserModel?>(
      future: ref.read(userServiceProvider).getUserById(widget.userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return MainLayout(
            title: AppLocale.userDetails.getString(context),
            currentIndex: 3,
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return MainLayout(
            title: AppLocale.userDetails.getString(context),
            currentIndex: 3,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppTheme.errorColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    AppLocale.userNotFound.getString(context),
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => context.go('/users'),
                    child: Text(AppLocale.backToUsers.getString(context)),
                  ),
                ],
              ),
            ),
          );
        }

        final user = snapshot.data!;
        return MainLayout(
          title: user.fullName,
          currentIndex: 3,
          actions: [
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _showEditDialog(context, user),
              tooltip: AppLocale.editUser.getString(context),
            ),
            if (user.role != 'admin')
              IconButton(
                icon: const Icon(Icons.delete),
                onPressed: () => _showDeleteDialog(context, user),
                tooltip: AppLocale.deleteUser.getString(context),
              ),
          ],
          child: _buildUserDetail(context, user),
        );
      },
    );
  }

  Widget _buildUserDetail(BuildContext context, UserModel user) {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1024;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Back button
          Row(
            children: [
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => context.go('/guards'),
              ),
              Text(
                AppLocale.backToUsers.getString(context),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (isDesktop)
            _buildDesktopLayout(context, user)
          else
            _buildMobileLayout(context, user),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context, UserModel user) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - User Info
        Expanded(
          flex: 2,
          child: Column(
            children: [
              _buildUserInfoCard(context, user),
              const SizedBox(height: 16),
              _buildContactCard(context, user),
              if (user.isGuard) ...[
                const SizedBox(height: 16),
                _buildSlovenianInfoCard(context, user),
              ],
            ],
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Right Column - Statistics & Events
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildStatsCard(context, user),
              const SizedBox(height: 16),
              _buildRecentEventsCard(context, user),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context, UserModel user) {
    return Column(
      children: [
        _buildUserInfoCard(context, user),
        const SizedBox(height: 16),
        _buildStatsCard(context, user),
        const SizedBox(height: 16),
        _buildContactCard(context, user),
        if (user.isGuard) ...[
          const SizedBox(height: 16),
          _buildSlovenianInfoCard(context, user),
        ],
        const SizedBox(height: 16),
        _buildRecentEventsCard(context, user),
      ],
    );
  }

  Widget _buildUserInfoCard(BuildContext context, UserModel user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Profile Avatar
                CircleAvatar(
                  radius: 40,
                  backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                  child: Icon(
                    Icons.person,
                    size: 48,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(width: 20),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.fullName,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      // Role Badge
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: _getRoleColor(user.role).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: _getRoleColor(user.role).withOpacity(0.3),
                          ),
                        ),
                        child: Text(
                          user.role.toUpperCase(),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getRoleColor(user.role),
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // User Details
            _buildInfoRow(
              context,
              icon: Icons.email,
              title: AppLocale.email.getString(context),
              value: user.email,
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoRow(
              context,
              icon: Icons.calendar_today,
              title: AppLocale.memberSince.getString(context),
              value: DateFormat('dd MMMM yyyy').format(user.createdAt),
            ),
            
            const SizedBox(height: 16),
            
            _buildInfoRow(
              context,
              icon: Icons.verified_user,
              title: AppLocale.accountStatus.getString(context),
              value: user.isActive ? AppLocale.active.getString(context) : AppLocale.inactive.getString(context),
              valueColor: user.isActive ? AppTheme.successColor : AppTheme.errorColor,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Theme.of(context).textTheme.bodyMedium?.color,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: valueColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContactCard(BuildContext context, UserModel user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.contact_phone,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocale.contactInformation.getString(context),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildContactItem(
              context,
              Icons.email,
              AppLocale.email.getString(context),
              user.email,
            ),

            const SizedBox(height: 12),

            _buildContactItem(
              context,
              Icons.phone,
              AppLocale.phone.getString(context),
              user.phone,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactItem(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSlovenianInfoCard(BuildContext context, UserModel user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.badge,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  AppLocale.employmentInformation.getString(context),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // EMŠO
            if (user.emso != null && user.emso!.isNotEmpty) ...[
              _buildContactItem(
                context,
                Icons.credit_card,
                AppLocale.emso.getString(context),
                user.emso!,
              ),
              const SizedBox(height: 12),
            ],

            // Employment Status
            _buildContactItem(
              context,
              Icons.work,
              AppLocale.employmentStatusEnhanced.getString(context),
              user.employmentStatusDisplay,
            ),

            const SizedBox(height: 16),

            // Slovenian Licenses Section
            Row(
              children: [
                Icon(
                  Icons.verified_user,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocale.slovenianLicenses.getString(context),
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            if (user.slovenianLicenses.isEmpty)
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.grey,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      AppLocale.noLicensesSelected.getString(context),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              )
            else
              ...user.slovenianLicenses.map((license) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: AppTheme.successColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppTheme.successColor.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: AppTheme.successColor,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        license == SlovenianLicenseType.npk_gostinski_lokali
                            ? AppLocale.npkGostinskiLokali.getString(context)
                            : AppLocale.deloNaObjektu.getString(context),
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: AppTheme.successColor,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard(BuildContext context, UserModel user) {
    return StreamBuilder<List<EventModel>>(
      stream: ref.read(eventServiceProvider).getEvents(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        final allEvents = snapshot.data ?? [];
        final userEvents = allEvents.where((event) =>
          event.assignedGuardIds.contains(widget.userId)).toList();

        final completedEvents = userEvents.where((e) => e.status == 'completed').length;
        final totalHours = userEvents.fold<double>(0, (sum, event) =>
          sum + (event.expectedDurationMinutes / 60));
        final avgEventDuration = userEvents.isEmpty ? 0.0 :
          totalHours / userEvents.length;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.analytics,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      AppLocale.workStatistics.getString(context),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Statistics Grid - Responsive Layout
                LayoutBuilder(
                  builder: (context, constraints) {
                    // Calculate responsive grid
                    final screenWidth = constraints.maxWidth;
                    final crossAxisCount = screenWidth > 600 ? 2 : 1;
                    final childAspectRatio = screenWidth > 600 ? 1.8 : 2.5;

                    return GridView.count(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      crossAxisCount: crossAxisCount,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: childAspectRatio,
                      children: [
                        _buildStatItem(
                          context,
                          AppLocale.totalEvents.getString(context),
                          userEvents.length.toString(),
                          Icons.event,
                          AppTheme.primaryColor,
                        ),
                        _buildStatItem(
                          context,
                          AppLocale.completedEvents.getString(context),
                          completedEvents.toString(),
                          Icons.check_circle,
                          AppTheme.successColor,
                        ),
                        _buildStatItem(
                          context,
                          AppLocale.totalHours.getString(context),
                          '${totalHours.toStringAsFixed(1)}h',
                          Icons.access_time,
                          AppTheme.warningColor,
                        ),
                        _buildStatItem(
                          context,
                          'Average Event Duration',
                          '${avgEventDuration.toStringAsFixed(1)}h',
                          Icons.schedule,
                          AppTheme.primaryColor,
                        ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 16),

                // Average Hourly Rate
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.trending_up,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Average Event Duration',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Text(
                        '${avgEventDuration.toStringAsFixed(1)}h',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Flexible(
            child: Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentEventsCard(BuildContext context, UserModel user) {
    return StreamBuilder<List<EventModel>>(
      stream: ref.read(eventServiceProvider).getEvents(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        final allEvents = snapshot.data ?? [];
        final userEvents = allEvents.where((event) =>
          event.assignedGuardIds.contains(widget.userId)).toList();

        // Sort by start date, most recent first
        userEvents.sort((a, b) => b.startDateTime.compareTo(a.startDateTime));
        final recentEvents = userEvents.take(5).toList();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.history,
                      color: AppTheme.primaryColor,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      AppLocale.recentEvents.getString(context),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                if (recentEvents.isEmpty)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.grey,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            AppLocale.noEventsAssigned.getString(context),
                            style: TextStyle(
                              color: Colors.grey,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  ...recentEvents.map((event) => _buildEventListItem(context, event)),

                if (userEvents.length > 5) ...[
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () => _showAllUserEvents(context, user),
                      child: Text(AppLocale.viewAllEvents.getString(context)),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEventListItem(BuildContext context, EventModel event) {
    final statusColor = _getStatusColor(event.status);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusColor.withOpacity(0.3),
        ),
      ),
      child: InkWell(
        onTap: () => context.go('/events/${event.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Row(
          children: [
            // Status indicator
            Container(
              width: 4,
              height: 40,
              decoration: BoxDecoration(
                color: statusColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 16),

            // Event details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    event.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${event.location} • ${DateFormat('dd/MM/yyyy').format(event.startDateTime)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),

            // Status and rate
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    event.status.toUpperCase(),
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                      color: statusColor,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${(event.expectedDurationMinutes / 60).toStringAsFixed(1)}h',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.successColor,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper Methods
  Color _getRoleColor(String role) {
    switch (role.toLowerCase()) {
      case 'admin':
        return AppTheme.errorColor;
      case 'guard':
        return AppTheme.primaryColor;
      case 'supervisor':
        return AppTheme.warningColor;
      default:
        return Colors.grey;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'confirmed':
        return AppTheme.successColor;
      case 'pending':
        return AppTheme.warningColor;
      case 'completed':
        return AppTheme.primaryColor;
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return Colors.grey;
    }
  }

  void _showEditDialog(BuildContext context, UserModel user) {
    // TODO: Implement user edit dialog
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocale.comingSoon.getString(context)),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocale.deleteUser.getString(context)),
        content: Text(AppLocale.deleteUserConfirmation.getString(context)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocale.cancel.getString(context)),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref.read(userServiceProvider).deleteUser(user.id);
              if (context.mounted) {
                context.go('/guards');
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: Text(AppLocale.delete.getString(context)),
          ),
        ],
      ),
    );
  }

  void _showAllUserEvents(BuildContext context, UserModel user) {
    // TODO: Navigate to filtered events page showing only this user's events
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocale.comingSoon.getString(context)),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
