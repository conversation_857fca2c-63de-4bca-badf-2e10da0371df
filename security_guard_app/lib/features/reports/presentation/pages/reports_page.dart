import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';
import 'package:intl/intl.dart';

import '../../../../core/theme/app_theme.dart';
import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/services/event_service.dart';
import '../../../../shared/services/user_service.dart';
import '../../../../shared/models/event_model.dart';
import '../../../../shared/models/user_model.dart';

class ReportsPage extends ConsumerStatefulWidget {
  const ReportsPage({super.key});

  @override
  ConsumerState<ReportsPage> createState() => _ReportsPageState();
}

class _ReportsPageState extends ConsumerState<ReportsPage> {
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String _selectedReportType = 'overview';

  @override
  Widget build(BuildContext context) {
    return MainLayout(
      title: AppLocale.reportsAnalytics.getString(context),
      currentIndex: 4, // Reports is the 5th item (0-indexed)
      actions: [
        // Export Button
        IconButton(
          icon: const Icon(Icons.download),
          onPressed: () => _exportReport(context),
          tooltip: AppLocale.exportReport.getString(context),
        ),
      ],
      child: _buildReportsContent(context),
    );
  }

  Widget _buildReportsContent(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isDesktop = screenSize.width > 1024;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report Controls
          _buildReportControls(context),

          const SizedBox(height: 24),

          // Report Content
          if (isDesktop)
            _buildDesktopLayout(context)
          else
            _buildMobileLayout(context),
        ],
      ),
    );
  }

  Widget _buildReportControls(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.reportFilters.getString(context),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // Responsive Filter Controls
            LayoutBuilder(
              builder: (context, constraints) {
                final screenWidth = constraints.maxWidth;
                final isNarrow = screenWidth < 768;

                if (isNarrow) {
                  // Mobile layout - Stack vertically
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Report Type Selector
                      DropdownButtonFormField<String>(
                        value: _selectedReportType,
                        decoration: InputDecoration(
                          labelText: AppLocale.reportType.getString(context),
                          border: const OutlineInputBorder(),
                        ),
                        items: [
                          DropdownMenuItem(
                            value: 'overview',
                            child: Text(AppLocale.overviewReport.getString(context)),
                          ),
                          DropdownMenuItem(
                            value: 'financial',
                            child: Text(AppLocale.financialReport.getString(context)),
                          ),
                          DropdownMenuItem(
                            value: 'attendance',
                            child: Text(AppLocale.attendanceReport.getString(context)),
                          ),
                          DropdownMenuItem(
                            value: 'guards',
                            child: Text(AppLocale.guardPerformance.getString(context)),
                          ),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedReportType = value;
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Date Range and Refresh in a row
                      Row(
                        children: [
                          // Date Range Selector
                          Expanded(
                            child: InkWell(
                              onTap: () => _selectDateRange(context),
                              child: InputDecorator(
                                decoration: InputDecoration(
                                  labelText: AppLocale.dateRange.getString(context),
                                  border: const OutlineInputBorder(),
                                ),
                                child: Text(
                                  '${DateFormat('dd/MM').format(_startDate)} - ${DateFormat('dd/MM').format(_endDate)}',
                                  style: const TextStyle(fontSize: 14),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),

                          // Refresh Button
                          ElevatedButton.icon(
                            onPressed: () => setState(() {}),
                            icon: const Icon(Icons.refresh),
                            label: Text(AppLocale.refresh.getString(context)),
                          ),
                        ],
                      ),
                    ],
                  );
                } else {
                  // Desktop layout - Use flexible row
                  return Row(
                    children: [
                      // Report Type Selector
                      Flexible(
                        flex: 2,
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 250),
                          child: DropdownButtonFormField<String>(
                            value: _selectedReportType,
                            decoration: InputDecoration(
                              labelText: AppLocale.reportType.getString(context),
                              border: const OutlineInputBorder(),
                            ),
                            items: [
                              DropdownMenuItem(
                                value: 'overview',
                                child: Text(AppLocale.overviewReport.getString(context)),
                              ),
                              DropdownMenuItem(
                                value: 'financial',
                                child: Text(AppLocale.financialReport.getString(context)),
                              ),
                              DropdownMenuItem(
                                value: 'attendance',
                                child: Text(AppLocale.attendanceReport.getString(context)),
                              ),
                              DropdownMenuItem(
                                value: 'guards',
                                child: Text(AppLocale.guardPerformance.getString(context)),
                              ),
                            ],
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _selectedReportType = value;
                                });
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Date Range Selector
                      Flexible(
                        flex: 1,
                        child: ConstrainedBox(
                          constraints: const BoxConstraints(maxWidth: 180),
                          child: InkWell(
                            onTap: () => _selectDateRange(context),
                            child: InputDecorator(
                              decoration: InputDecoration(
                                labelText: AppLocale.dateRange.getString(context),
                                border: const OutlineInputBorder(),
                              ),
                              child: Text(
                                '${DateFormat('dd/MM').format(_startDate)} - ${DateFormat('dd/MM').format(_endDate)}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Refresh Button
                      ElevatedButton.icon(
                        onPressed: () => setState(() {}),
                        icon: const Icon(Icons.refresh),
                        label: Text(AppLocale.refresh.getString(context)),
                      ),
                    ],
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Left Column - Summary Cards
        Expanded(
          flex: 1,
          child: Column(
            children: [
              _buildSummaryCards(context),
              const SizedBox(height: 16),
              _buildQuickStats(context),
            ],
          ),
        ),

        const SizedBox(width: 16),

        // Right Column - Detailed Reports
        Expanded(
          flex: 2,
          child: _buildDetailedReport(context),
        ),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      children: [
        _buildSummaryCards(context),
        const SizedBox(height: 16),
        _buildQuickStats(context),
        const SizedBox(height: 16),
        _buildDetailedReport(context),
      ],
    );
  }

  Widget _buildSummaryCards(BuildContext context) {
    return StreamBuilder<List<EventModel>>(
      stream: ref.read(eventServiceProvider).getEvents(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        final events = snapshot.data ?? [];
        final filteredEvents = _filterEventsByDateRange(events);

        return _buildSummaryCardsContent(context, filteredEvents);
      },
    );
  }

  Widget _buildSummaryCardsContent(BuildContext context, List<EventModel> events) {
    final totalEvents = events.length;
    final completedEvents = events.where((e) => e.status == 'completed').length;
    final totalHours = events.fold<double>(0, (sum, event) => sum + (event.expectedDurationMinutes / 60));

    final screenSize = MediaQuery.of(context).size;
    final crossAxisCount = screenSize.width > 768 ? 4 : 2;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildSummaryCard(
          context,
          title: AppLocale.totalEvents.getString(context),
          value: totalEvents.toString(),
          icon: Icons.event,
          color: AppTheme.primaryColor,
        ),
        _buildSummaryCard(
          context,
          title: AppLocale.completedEvents.getString(context),
          value: completedEvents.toString(),
          icon: Icons.check_circle,
          color: AppTheme.successColor,
        ),
        _buildSummaryCard(
          context,
          title: AppLocale.totalHours.getString(context),
          value: totalHours.toStringAsFixed(1),
          icon: Icons.access_time,
          color: AppTheme.warningColor,
        ),

      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context, {
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.quickStats.getString(context),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            // Event Status Distribution
            StreamBuilder<List<EventModel>>(
              stream: ref.read(eventServiceProvider).getEvents(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }

                final events = _filterEventsByDateRange(snapshot.data ?? []);
                final statusCounts = <String, int>{};

                for (final event in events) {
                  statusCounts[event.status] = (statusCounts[event.status] ?? 0) + 1;
                }

                return Column(
                  children: statusCounts.entries.map((entry) {
                    final percentage = events.isEmpty ? 0.0 : (entry.value / events.length) * 100;
                    final statusColor = _getStatusColor(entry.key);

                    return Container(
                      margin: const EdgeInsets.symmetric(vertical: 6),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: statusColor.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: statusColor,
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            flex: 3,
                            child: Text(
                              entry.key.toUpperCase(),
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).textTheme.bodyLarge?.color,
                              ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: statusColor.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '${entry.value}',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: statusColor,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '${percentage.toStringAsFixed(1)}%',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Theme.of(context).textTheme.bodyMedium?.color,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedReport(BuildContext context) {
    switch (_selectedReportType) {
      case 'financial':
        return _buildFinancialReport(context);
      case 'attendance':
        return _buildAttendanceReport(context);
      case 'guards':
        return _buildGuardPerformanceReport(context);
      default:
        return _buildOverviewReport(context);
    }
  }

  Widget _buildOverviewReport(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.overviewReport.getString(context),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            StreamBuilder<List<EventModel>>(
              stream: ref.read(eventServiceProvider).getEvents(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }

                final events = _filterEventsByDateRange(snapshot.data ?? []);

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Recent Events (${events.length} total)',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    const SizedBox(height: 12),

                    if (events.isEmpty)
                      Text(
                        AppLocale.noEventsFound.getString(context),
                        style: Theme.of(context).textTheme.bodyMedium,
                      )
                    else
                      ...events.take(5).map((event) => _buildEventListItem(context, event)),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventListItem(BuildContext context, EventModel event) {
    final statusColor = _getStatusColor(event.status);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 6),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: statusColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Status indicator
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 16),

          // Event icon
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.event,
              size: 20,
              color: statusColor,
            ),
          ),
          const SizedBox(width: 16),

          // Event details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      Icons.location_on,
                      size: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        event.location,
                        style: TextStyle(
                          color: Theme.of(context).textTheme.bodySmall?.color,
                          fontSize: 13,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 14,
                      color: Theme.of(context).textTheme.bodySmall?.color,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      DateFormat('dd/MM/yyyy HH:mm').format(event.startDateTime),
                      style: TextStyle(
                        color: Theme.of(context).textTheme.bodySmall?.color,
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Status and rate
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  event.status.toUpperCase(),
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
              ),

            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFinancialReport(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.financialReport.getString(context),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),

            StreamBuilder<List<EventModel>>(
              stream: ref.read(eventServiceProvider).getEvents(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }

                final events = _filterEventsByDateRange(snapshot.data ?? []);
                final totalEvents = events.length;
                final completedEvents = events.where((e) => e.status == 'completed').length;
                final totalHours = events.fold<double>(0, (sum, event) => sum + (event.expectedDurationMinutes / 60));

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFinancialMetric(
                      context,
                      'Total Events',
                      totalEvents.toString(),
                      Icons.event,
                      AppTheme.primaryColor,
                    ),
                    const SizedBox(height: 16),
                    _buildFinancialMetric(
                      context,
                      'Completed Events',
                      completedEvents.toString(),
                      Icons.check_circle,
                      AppTheme.successColor,
                    ),
                    const SizedBox(height: 16),
                    _buildFinancialMetric(
                      context,
                      'Total Hours',
                      '${totalHours.toStringAsFixed(1)} hrs',
                      Icons.access_time,
                      AppTheme.warningColor,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialMetric(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).textTheme.bodyLarge?.color,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttendanceReport(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.attendanceReport.getString(context),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocale.comingSoon.getString(context),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGuardPerformanceReport(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.guardPerformance.getString(context),
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Text(
              AppLocale.comingSoon.getString(context),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  // Helper Methods
  List<EventModel> _filterEventsByDateRange(List<EventModel> events) {
    return events.where((event) {
      return event.startDateTime.isAfter(_startDate.subtract(const Duration(days: 1))) &&
             event.startDateTime.isBefore(_endDate.add(const Duration(days: 1)));
    }).toList();
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
      case 'confirmed':
        return AppTheme.successColor;
      case 'pending':
        return AppTheme.warningColor;
      case 'completed':
        return AppTheme.primaryColor;
      case 'cancelled':
        return AppTheme.errorColor;
      default:
        return Colors.grey;
    }
  }

  Future<void> _selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  void _exportReport(BuildContext context) {
    // TODO: Implement CSV export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(AppLocale.comingSoon.getString(context)),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }
}
