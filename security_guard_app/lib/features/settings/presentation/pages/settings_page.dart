import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localization/flutter_localization.dart';

import '../../../../core/localization/app_localization.dart';
import '../../../../shared/widgets/main_layout.dart';
import '../../../../shared/providers/theme_provider.dart';
import '../../../../shared/providers/locale_provider.dart';
import '../../../../shared/services/auth_service.dart';

class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  @override
  Widget build(BuildContext context) {
    final currentTheme = ref.watch(themeProvider);
    final currentLocale = ref.watch(localeProvider);
    final authService = ref.read(authServiceProvider);

    return MainLayout(
      title: AppLocale.settings.getString(context),
      currentIndex: 6, // Settings navigation index
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Appearance Section
            _buildSectionHeader(
              context,
              title: AppLocale.appearance.getString(context),
              icon: Icons.palette,
            ),
            const SizedBox(height: 16),
            
            // Theme Selector
            _buildThemeSelector(context, currentTheme),
            const SizedBox(height: 32),

            // Language Section
            _buildSectionHeader(
              context,
              title: AppLocale.language.getString(context),
              icon: Icons.language,
            ),
            const SizedBox(height: 16),
            
            // Language Selector
            _buildLanguageSelector(context, currentLocale),
            const SizedBox(height: 32),

            // Account Section
            _buildSectionHeader(
              context,
              title: AppLocale.account.getString(context),
              icon: Icons.account_circle,
            ),
            const SizedBox(height: 16),
            
            // Account Settings
            _buildAccountSettings(context, authService),
            const SizedBox(height: 32),

            // About Section
            _buildSectionHeader(
              context,
              title: AppLocale.about.getString(context),
              icon: Icons.info,
            ),
            const SizedBox(height: 16),
            
            // About Information
            _buildAboutSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context, {
    required String title,
    required IconData icon,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildThemeSelector(BuildContext context, AppThemeMode currentTheme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.themeMode.getString(context),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Theme Options
            ...AppThemeMode.values.map((themeMode) {
              final themeNotifier = ref.read(themeProvider.notifier);
              final isSelected = currentTheme == themeMode;
              
              return Container(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: ListTile(
                  leading: Icon(
                    themeNotifier.getThemeIcon(themeMode),
                    color: isSelected 
                        ? Theme.of(context).colorScheme.primary 
                        : Theme.of(context).iconTheme.color,
                  ),
                  title: Text(
                    _getLocalizedThemeName(context, themeMode),
                    style: TextStyle(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      color: isSelected 
                          ? Theme.of(context).colorScheme.primary 
                          : null,
                    ),
                  ),
                  subtitle: Text(_getThemeDescription(context, themeMode)),
                  trailing: isSelected 
                      ? Icon(
                          Icons.check_circle,
                          color: Theme.of(context).colorScheme.primary,
                        )
                      : null,
                  selected: isSelected,
                  selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  onTap: () {
                    ref.read(themeProvider.notifier).setTheme(themeMode);
                  },
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildLanguageSelector(BuildContext context, Locale currentLocale) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.selectLanguage.getString(context),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Language Options
            ListTile(
              leading: const Text('🇸🇮', style: TextStyle(fontSize: 24)),
              title: const Text('Slovenščina'),
              subtitle: const Text('Slovenian'),
              trailing: currentLocale.languageCode == 'sl' 
                  ? Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    )
                  : null,
              selected: currentLocale.languageCode == 'sl',
              selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              onTap: () {
                ref.read(localeProvider.notifier).setLocale(const Locale('sl', ''));
              },
            ),
            const SizedBox(height: 8),
            ListTile(
              leading: const Text('🇺🇸', style: TextStyle(fontSize: 24)),
              title: const Text('English'),
              subtitle: const Text('English'),
              trailing: currentLocale.languageCode == 'en' 
                  ? Icon(
                      Icons.check_circle,
                      color: Theme.of(context).colorScheme.primary,
                    )
                  : null,
              selected: currentLocale.languageCode == 'en',
              selectedTileColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              onTap: () {
                ref.read(localeProvider.notifier).setLocale(const Locale('en', ''));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSettings(BuildContext context, AuthService authService) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.accountSettings.getString(context),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            // Current User Info
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                child: Icon(
                  Icons.person,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
              title: Text(authService.currentFirebaseUser?.email ?? 'Unknown User'),
              subtitle: Text(AppLocale.administrator.getString(context)),
              contentPadding: EdgeInsets.zero,
            ),
            const SizedBox(height: 16),
            
            // Account Actions
            ListTile(
              leading: const Icon(Icons.security),
              title: Text(AppLocale.changePassword.getString(context)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Implement change password
                _showComingSoonDialog(context, AppLocale.changePassword.getString(context));
              },
            ),
            ListTile(
              leading: const Icon(Icons.privacy_tip),
              title: Text(AppLocale.privacy.getString(context)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                // TODO: Implement privacy settings
                _showComingSoonDialog(context, AppLocale.privacy.getString(context));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocale.appInformation.getString(context),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            ListTile(
              leading: const Icon(Icons.info),
              title: Text(AppLocale.version.getString(context)),
              subtitle: const Text('1.0.0'),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: const Icon(Icons.help),
              title: Text(AppLocale.help.getString(context)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              contentPadding: EdgeInsets.zero,
              onTap: () {
                _showComingSoonDialog(context, AppLocale.help.getString(context));
              },
            ),
            ListTile(
              leading: const Icon(Icons.contact_support),
              title: Text(AppLocale.support.getString(context)),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              contentPadding: EdgeInsets.zero,
              onTap: () {
                _showComingSoonDialog(context, AppLocale.support.getString(context));
              },
            ),
          ],
        ),
      ),
    );
  }

  String _getLocalizedThemeName(BuildContext context, AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return AppLocale.lightTheme.getString(context);
      case AppThemeMode.dark:
        return AppLocale.darkTheme.getString(context);
      case AppThemeMode.system:
        return AppLocale.systemTheme.getString(context);
    }
  }

  String _getThemeDescription(BuildContext context, AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return AppLocale.lightThemeDesc.getString(context);
      case AppThemeMode.dark:
        return AppLocale.darkThemeDesc.getString(context);
      case AppThemeMode.system:
        return AppLocale.systemThemeDesc.getString(context);
    }
  }

  void _showComingSoonDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(AppLocale.comingSoon.getString(context)),
        content: Text('${feature} ${AppLocale.comingSoonDesc.getString(context)}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(AppLocale.ok.getString(context)),
          ),
        ],
      ),
    );
  }
}
